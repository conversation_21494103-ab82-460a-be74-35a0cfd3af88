import React from 'react';
import { Typography } from '@/shared/components/common';
import { DashboardWidget } from '../types';
import { DataCountWidget, DataStorageWidget } from './data';
import { WIDGET_TYPES } from '../constants';

/**
 * Renders widget content based on widget type and content
 */
export const renderWidgetContent = (widget: DashboardWidget): React.ReactNode => {
  try {
    if (widget.type === WIDGET_TYPES.DATA_COUNT) {
      return <DataCountWidget />;
    } else if (widget.type === WIDGET_TYPES.DATA_STORAGE) {
      return <DataStorageWidget />;
    } else if (widget.content) {
      if (typeof widget.content === 'string') {
        return (
          <div className="p-4">
            <Typography variant="body2">{widget.content}</Typography>
          </div>
        );
      } else if (React.isValidElement(widget.content)) {
        return widget.content;
      } else {
        console.warn('Invalid widget content:', widget.content);
        return (
          <div className="p-4 text-center text-muted-foreground">
            <Typography variant="body2">Nội dung không hợp lệ</Typography>
          </div>
        );
      }
    } else {
      return (
        <div className="p-4 text-center text-muted-foreground">
          <Typography variant="body2">Nội dung widget</Typography>
        </div>
      );
    }
  } catch (error) {
    console.error('Error rendering widget:', error, widget);
    return (
      <div className="p-4 text-center text-red-500">
        <Typography variant="body2">Lỗi hiển thị widget</Typography>
      </div>
    );
  }
};
