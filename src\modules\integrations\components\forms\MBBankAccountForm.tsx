import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FieldValues } from 'react-hook-form';
import { useFormErrors } from '@/shared/hooks/form';
import { MBBankAccountFormValues } from '../../types/banking.types';
import { mbBankAccountSchema } from '../../schemas/banking.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface MBBankAccountFormProps {
  /**
   * D<PERSON> liệu ban đầu (cho chế độ chỉnh sửa)
   */
  initialData?: MBBankAccountFormValues;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: MBBankAccountFormValues) => Promise<void>;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái loading
   */
  loading?: boolean;
}

/**
 * Form liên kết tài khoản ngân hàng MB
 */
const MBBankAccountForm: React.FC<MBBankAccountFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const { t } = useTranslation(['integrations', 'common']);
  const { formRef } = useFormErrors<MBBankAccountFormValues>();

  // State cho form data
  const [formData, setFormData] = useState<MBBankAccountFormValues>({
    account_holder_name: initialData?.account_holder_name || '',
    account_number: initialData?.account_number || '',
    identification_number: initialData?.identification_number || '',
    phone_number: initialData?.phone_number || '',
    label: initialData?.label || '',
  });

  // State cho việc fetch tên chủ tài khoản
  const [fetchingAccountName, setFetchingAccountName] = useState(false);

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof MBBankAccountFormValues, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Mock API để lấy tên chủ tài khoản từ số tài khoản
  const fetchAccountHolderName = useCallback(async (accountNumber: string) => {
    if (!accountNumber || accountNumber.length < 6) return;

    setFetchingAccountName(true);
    try {
      // TODO: Thay thế bằng API thực tế
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      // Mock response - thay thế bằng API call thực tế
      const mockName = `Nguyễn Văn A - ${accountNumber}`;

      setFormData(prev => ({
        ...prev,
        account_holder_name: mockName,
      }));

      NotificationUtil.success({
        message: t('integrations:banking.mb.fetchNameSuccess', 'Đã lấy tên chủ tài khoản thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error fetching account holder name:', error);
      NotificationUtil.error({
        message: t('integrations:banking.mb.fetchNameError', 'Không thể lấy tên chủ tài khoản'),
        duration: 5000,
      });
    } finally {
      setFetchingAccountName(false);
    }
  }, [t]);

  // Auto-fetch account holder name when account number changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.account_number && formData.account_number.length >= 6) {
        fetchAccountHolderName(formData.account_number);
      }
    }, 1000); // Debounce 1 second

    return () => clearTimeout(timeoutId);
  }, [formData.account_number, fetchAccountHolderName]);

  // Xử lý submit form
  const handleSubmit = async (data: MBBankAccountFormValues) => {
    try {
      // Prepare data for submission, ensuring optional fields are handled correctly
      const submitData: MBBankAccountFormValues = {
        account_holder_name: data.account_holder_name,
        account_number: data.account_number,
        identification_number: data.identification_number,
        phone_number: data.phone_number,
        ...(data.label && data.label.trim() !== '' && { label: data.label }),
      };

      // Gọi callback onSubmit
      await onSubmit(submitData);
    } catch (error) {
      console.error('Form submission error:', error);
      NotificationUtil.error({
        message: t('integrations:banking.mb.submitError', 'Có lỗi xảy ra khi lưu thông tin tài khoản'),
        duration: 5000,
      });
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="border-0">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h5" className="font-semibold mb-2">
              {t('integrations:banking.mb.title', 'Liên kết tài khoản MB Bank')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integrations:banking.mb.description', 'Nhập thông tin tài khoản ngân hàng MB Bank để liên kết với hệ thống')}
            </Typography>
          </div>

          {/* Form */}
          <Form ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>} schema={mbBankAccountSchema} onSubmit={handleSubmit as (data: unknown) => void}>
            <div className="space-y-4">
              {/* Số tài khoản */}
              <FormItem
                label={t('integrations:banking.mb.accountNumber', 'Số tài khoản')}
                name="account_number"
                required
              >
                <Input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => handleInputChange('account_number', e.target.value)}
                  placeholder={t('integrations:banking.mb.accountNumberPlaceholder', 'Nhập số tài khoản MB Bank')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên chủ tài khoản */}
              <FormItem
                label={t('integrations:banking.mb.accountHolderName', 'Tên chủ tài khoản')}
                name="account_holder_name"
                required
              >
                <Input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  placeholder={t('integrations:banking.mb.accountHolderNamePlaceholder', 'Tên sẽ được tự động lấy từ API')}
                  fullWidth
                  disabled={fetchingAccountName}
                />
              </FormItem>

              {/* Số CMND/CCCD */}
              <FormItem
                label={t('integrations:banking.mb.identificationNumber', 'Số CMND/CCCD')}
                name="identification_number"
                required
              >
                <Input
                  type="text"
                  value={formData.identification_number}
                  onChange={(e) => handleInputChange('identification_number', e.target.value)}
                  placeholder={t('integrations:banking.mb.identificationNumberPlaceholder', 'Nhập số CMND/CCCD đăng ký MB Bank')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>

              {/* Số điện thoại */}
              <FormItem
                label={t('integrations:banking.mb.phoneNumber', 'Số điện thoại')}
                name="phone_number"
                required
              >
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                  placeholder={t('integrations:banking.mb.phoneNumberPlaceholder', 'Nhập số điện thoại đăng ký MB Bank')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên gợi nhớ */}
              <FormItem
                label={t('integrations:banking.mb.label', 'Tên gợi nhớ')}
                name="label"
              >
                <Input
                  type="text"
                  value={formData.label}
                  onChange={(e) => handleInputChange('label', e.target.value)}
                  placeholder={t('integrations:banking.mb.labelPlaceholder', 'Nhập tên gợi nhớ (tùy chọn)')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {t('common:cancel', 'Hủy')}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                isLoading={loading}
              >
                {initialData 
                  ? t('common:update', 'Cập nhật')
                  : t('common:save', 'Lưu')
                }
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default MBBankAccountForm;
