import React from 'react';
import Icon from './Icon';

/**
 * Test component để kiểm tra các icon ngân hàng
 */
const BankIconTest: React.FC = () => {
  const bankIcons = [
    { name: 'acb-bank', label: 'ACB Bank', description: 'Asia Commercial Bank' },
    { name: 'mb-bank', label: 'MB Bank', description: 'Military Bank' },
    { name: 'ocb-bank', label: 'OCB Bank', description: 'Orient Commercial Bank' },
    { name: 'kienlong-bank', label: 'Kiên Long Bank', description: 'Kiên Long Commercial Joint Stock Bank' },
  ];

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-center text-gray-800">
        Bank Icons Test
      </h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        {bankIcons.map((bank) => (
          <div key={bank.name} className="flex flex-col items-center space-y-3 p-4 border rounded-lg hover:shadow-md transition-shadow">
            <div className="p-3 bg-gray-50 rounded-lg">
              <Icon name={bank.name as 'acb-bank' | 'mb-bank' | 'ocb-bank' | 'kienlong-bank'} size="xl" />
            </div>
            <div className="text-center">
              <h3 className="font-semibold text-gray-800">{bank.label}</h3>
              <p className="text-sm text-gray-600 mt-1">{bank.description}</p>
              <code className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded mt-2 inline-block">
                {bank.name}
              </code>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-8 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3 text-gray-800">
          Usage Examples:
        </h3>
        <div className="space-y-2">
          <div className="flex items-center space-x-4">
            <Icon name="acb-bank" size="md" />
            <code className="text-sm bg-white px-2 py-1 rounded border">
              &lt;Icon name="acb-bank" size="md" /&gt;
            </code>
          </div>
          <div className="flex items-center space-x-4">
            <Icon name="mb-bank" size="lg" />
            <code className="text-sm bg-white px-2 py-1 rounded border">
              &lt;Icon name="mb-bank" size="lg" /&gt;
            </code>
          </div>
          <div className="flex items-center space-x-4">
            <Icon name="ocb-bank" size="sm" />
            <code className="text-sm bg-white px-2 py-1 rounded border">
              &lt;Icon name="ocb-bank" size="sm" /&gt;
            </code>
          </div>
          <div className="flex items-center space-x-4">
            <Icon name="kienlong-bank" size="xl" />
            <code className="text-sm bg-white px-2 py-1 rounded border">
              &lt;Icon name="kienlong-bank" size="xl" /&gt;
            </code>
          </div>
        </div>
      </div>

      <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
        <h3 className="text-lg font-semibold mb-2 text-green-800">
          ✅ Integration Status
        </h3>
        <ul className="text-sm text-green-700 space-y-1">
          <li>• Real bank logos imported from PNG files</li>
          <li>• Icons properly integrated into Icon component</li>
          <li>• TypeScript types updated</li>
          <li>• Ready to use in UserIntegrationManagementPage</li>
        </ul>
      </div>
    </div>
  );
};

export default BankIconTest;
