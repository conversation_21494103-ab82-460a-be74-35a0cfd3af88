import React, { useState, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Pagination } from '@/shared/components/common';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { NotificationUtil } from '@/shared/utils/notification';
import TrashDatasetGrid from '../components/TrashDatasetGrid';

import {
  useDeletedUserDataFineTuneList,
  useRestoreUserDataFineTune,
} from '@/modules/admin/dataset/user-data-fine-tune/hooks/useUserDataFineTune';
import {
  UserDataFineTuneResponseDto,
  UserDataFineTuneSortBy,
} from '@/modules/admin/dataset/user-data-fine-tune/types/user-data-fine-tune.types';
import { SortDirection } from '@/shared/dto/request/query.dto';

/**
 * Trang quản lý Data Fine Tune đã xóa mềm
 */
const TrashDataFineTunePage: React.FC<Record<string, never>> = () => {
  const { t } = useTranslation(['admin-dataset', 'common']);

  // State cho tìm kiếm và phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(12);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy] = useState<UserDataFineTuneSortBy>(UserDataFineTuneSortBy.CREATED_AT);
  const [sortDirection] = useState<SortDirection>(SortDirection.DESC);

  // API hooks
  const { mutateAsync: restoreDataset } = useRestoreUserDataFineTune();

  // Xử lý khôi phục dataset
  const handleRestore = useCallback(
    async (dataset: UserDataFineTuneResponseDto) => {
      try {
        await restoreDataset(dataset.id);
        NotificationUtil.success({
          message: t('admin-dataset:trash.restoreSuccess', 'Khôi phục dataset thành công'),
          duration: 3000,
        });
      } catch (error) {
        console.error('Error restoring dataset:', error);
        NotificationUtil.error({
          message: t('admin-dataset:trash.restoreError', 'Lỗi khi khôi phục dataset'),
          duration: 3000,
        });
      }
    },
    [restoreDataset, t]
  );

  // Tạo query params cho API
  const queryParams = useMemo(() => {
    const params = {
      page: currentPage,
      limit: itemsPerPage,
      sortDirection: sortDirection,
      sortBy: sortBy,
    };

    if (searchTerm) {
      return { ...params, search: searchTerm };
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection]);

  // API hooks
  const { data: datasetsData, isLoading } = useDeletedUserDataFineTuneList(queryParams);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  const handleItemsPerPageChange = useCallback((newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset về trang 1
  }, []);

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  return (
    <div className="space-y-6">
      {/* MenuIconBar */}
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('admin-dataset:filters.all', 'Tất cả'),
            icon: 'list',
            onClick: () => '',
          },
        ]}
        showDateFilter={false}
        showColumnFilter={false}
        // Không có onAdd prop để ẩn nút thêm mới cho trang trash
      />

      {/* Dataset Grid */}
      <TrashDatasetGrid
        datasets={datasetsData?.items || []}
        loading={isLoading}
        onRestore={handleRestore}
      />

      {/* Pagination - Hiển thị khi có dữ liệu */}
      {datasetsData?.items && datasetsData.items.length > 0 && (
        <div className="flex justify-end mt-8">
          <Pagination
            variant="simple"
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
            totalPages={datasetsData.meta?.totalPages || 1}
            onPageChange={handlePageChange}
            onItemsPerPageChange={handleItemsPerPageChange}
            showFirstLastButtons={false}
            showItemsPerPageSelector={true}
            showPageInfo={false}
            itemsPerPageOptions={[10, 20, 50, 100]}
            size="md"
            borderless={true}
          />
        </div>
      )}
    </div>
  );
};

export default TrashDataFineTunePage;
