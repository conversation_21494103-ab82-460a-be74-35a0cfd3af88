import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

import {
  Card,
  Button,
  Typography,
  Modal,
  ResponsiveImage,
  IconCard,
} from '@/shared/components/common';
import LoginForm from '../components/LoginForm';
import RegisterForm from '../components/RegisterForm';
import ForgotPasswordForm from '../components/ForgotPasswordForm';
// Import logo
import logoImage from '@/shared/assets/images/logo/logo.png';
// Import hooks
import { useGoogleAuthUrl, useFacebookAuthUrl, useZaloAuthUrl } from '../hooks/useAuthQuery';
// Import env utility
import { env } from '@/shared/utils';

/**
 * Login page component
 */
const LoginPage: React.FC = () => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState<string>('login');
  const [showForgotPassword, setShowForgotPassword] = useState<boolean>(false);

  // Hooks for social login
  const googleAuthUrl = useGoogleAuthUrl();
  const facebookAuthUrl = useFacebookAuthUrl();
  const zaloAuthUrl = useZaloAuthUrl();

  // Handle forgot password button click
  const handleForgotPassword = () => {
    setShowForgotPassword(true);
  };

  // Handle forgot password modal close
  const handleCloseForgotPassword = () => {
    setShowForgotPassword(false);
  };

  // Handle Google login
  const handleGoogleLogin = () => {
    // Use the URL from env utility
    if (env.googleLoginUrl) {
      window.location.href = env.googleLoginUrl;
    } else {
      // Fallback to API if direct URL is not available
      googleAuthUrl.mutate(undefined, {
        onSuccess: response => {
          if (response.result?.url) {
            window.location.href = response.result.url;
          }
        },
        onError: error => {
          console.error('Failed to get Google auth URL:', error);
        },
      });
    }
  };

  // Handle Facebook login
  const handleFacebookLogin = () => {
    // Use the URL from env utility
    if (env.facebookLoginUrl) {
      window.location.href = env.facebookLoginUrl;
    } else {
      // Fallback to API if direct URL is not available
      facebookAuthUrl.mutate(undefined, {
        onSuccess: response => {
          if (response.result?.url) {
            window.location.href = response.result.url;
          }
        },
        onError: error => {
          console.error('Failed to get Facebook auth URL:', error);
        },
      });
    }
  };

  // Handle Zalo login
  const handleZaloLogin = () => {
    // Use the URL from env utility
    if (env.zaloLoginUrl) {
      window.location.href = env.zaloLoginUrl;
    } else {
      // Fallback to API if direct URL is not available
      zaloAuthUrl.mutate(env.zaloRedirectUri, {
        onSuccess: response => {
          if (response.result?.url) {
            window.location.href = response.result.url;
          }
        },
        onError: error => {
          console.error('Failed to get Zalo auth URL:', error);
        },
      });
    }
  };

  return (
    <Card variant="elevated" className="w-full max-w-md">
      <div className="flex flex-col items-center mb-">
        {/* Logo */}
        <div className="flex justify-center items-center w-full h-10">
          <ResponsiveImage
            src={logoImage}
            alt="RedAI Logo"
            className="h-8 object-contain max-w-[120px]"
          />
        </div>
      </div>

      <div className="mb-6">
        <div className="grid grid-cols-2 gap-0 border-b border-gray-200 dark:border-gray-700 mb-6">
          <button
            className={`py-3 font-medium text-sm transition-colors ${
              activeTab === 'login'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('login')}
          >
            {t('auth.login')}
          </button>
          <button
            className={`py-3 font-medium text-sm transition-colors ${
              activeTab === 'register'
                ? 'text-primary border-b-2 border-primary'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
            onClick={() => setActiveTab('register')}
          >
            {t('auth.register')}
          </button>
        </div>

        {activeTab === 'login' && (
          <div className="space-y-6">
            <LoginForm onForgotPassword={handleForgotPassword} />

            <div className="relative">
              <div className="relative flex justify-center text-sm">
                <span className="px-2 text-gray-500 dark:text-gray-400">
                  {t('auth.orContinueWith')}
                </span>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-3">
              <Button
                variant="ghost"
                className=" flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-blue-400 dark:hover:border-blue-500"
                onClick={handleFacebookLogin}
                aria-label={t('auth.loginWithFacebook')}
              >
                <IconCard icon="facebook" size="lg" variant="default" />
              </Button>
              <Button
                variant="ghost"
                className=" flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-red-400 dark:hover:border-red-500"
                onClick={handleGoogleLogin}
                aria-label={t('auth.loginWithGoogle')}
              >
                <IconCard icon="google" size="lg" variant="default" />
              </Button>
              <Button
                variant="ghost"
                className="flex items-center justify-center border border-gray-200 dark:border-gray-700 rounded-md hover:border-blue-400 dark:hover:border-blue-500"
                onClick={handleZaloLogin}
                aria-label={t('auth.loginWithZalo')}
              >
                <IconCard icon="zalo" size="lg" variant="default" />
              </Button>
            </div>
          </div>
        )}

        {activeTab === 'register' && (
          <div className="space-y-6">
            <RegisterForm />
          </div>
        )}
      </div>

      {/* Forgot Password Modal */}
      <Modal
        title={t('auth.resetPassword')}
        isOpen={showForgotPassword}
        onClose={handleCloseForgotPassword}
        size="md"
        footer={
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleCloseForgotPassword}>
              {t('common.cancel')}
            </Button>
          </div>
        }
      >
        <div className="p-4">
          <Typography variant="body2" color="muted" className="mb-6">
            {t('auth.forgotPasswordDescription')}
          </Typography>

          <ForgotPasswordForm onSuccess={handleCloseForgotPassword} />
        </div>
      </Modal>
    </Card>
  );
};

export default LoginPage;
