import React, { useCallback, useMemo } from 'react';
import {
  DashboardSidebar,
  DashboardCard,
  EmptyDashboard,
  DashboardTabs
} from '../components';
import { Icon } from '@/shared/components/common';
import { DashboardWidget, MenuItem } from '../types';
import { useDashboardTabs } from '../hooks/useDashboardTabs';
import '../styles/dashboard.css';

const DashboardPage: React.FC = () => {
  // Use tab system instead of direct widgets state
  const {
    tabsState,
    currentTab,
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    reorderTabs,
    changeTabMode,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    saveToStorage,
  } = useDashboardTabs();

  // Get current tab widgets
  const widgets = useMemo(() => currentTab?.widgets || [], [currentTab?.widgets]);

  const getWidgetTypeFromMenuItem = (menuItem: MenuItem): DashboardWidget['type'] => {
    // Map menu items to appropriate widget types
    switch (menuItem.id) {
      case 'data-count':
        return 'data-count';
      case 'data-storage':
        return 'data-storage';
      case 'revenue':
      case 'revenue-analysis':
      case 'revenue-trends':
        return 'chart';
      case 'orders':
      case 'order-status':
        return 'table';
      case 'api-usage':
      case 'user-analytics':
        return 'metric';
      default:
        return 'custom';
    }
  };

  const handleMenuItemClick = useCallback((menuItem: MenuItem) => {
    // Only allow adding widgets in edit mode
    if (currentTab?.mode !== 'edit') {
      return;
    }

    // Check if widget already exists
    const existingWidget = widgets.find(w => w.id === `${menuItem.id}-widget`);
    if (existingWidget) {
      // Widget already exists, don't add duplicate
      return;
    }

    // Find next available position
    const maxY = widgets.length > 0 ? Math.max(...widgets.map(w => w.y + w.h)) : 0;

    // Create new widget based on menu item
    const newWidget: DashboardWidget = {
      id: `${menuItem.id}-widget`,
      title: menuItem.title,
      type: getWidgetTypeFromMenuItem(menuItem),
      x: 0,
      y: maxY,
      w: menuItem.id === 'data-count' ? 12 : menuItem.id === 'data-storage' ? 6 : 6,
      h: menuItem.id === 'data-count' ? 6 : menuItem.id === 'data-storage' ? 4 : 5,
      minW: 4,
      minH: 3,
      maxW: 12,
      maxH: 10,
      isEmpty: (menuItem.id === 'data-count' || menuItem.id === 'data-storage') ? false : true
    };

    // Add widget to current tab
    addWidgetToCurrentTab(newWidget);
  }, [widgets, currentTab?.mode, addWidgetToCurrentTab]);

  const handleRemoveWidget = useCallback((widgetId: string) => {
    // Only allow removing widgets in edit mode
    if (currentTab?.mode !== 'edit') {
      return;
    }
    removeWidgetFromCurrentTab(widgetId);
  }, [currentTab?.mode, removeWidgetFromCurrentTab]);

  // Layout type for react-grid-layout
  interface Layout {
    i: string;
    x: number;
    y: number;
    w: number;
    h: number;
    minW?: number;
    minH?: number;
    maxW?: number;
    maxH?: number;
  }

  const handleLayoutChange = useCallback((layout: Layout[], layouts: { [key: string]: Layout[] }) => {
    // Cập nhật layout của widgets
    console.log('Layout changed:', layout, layouts);
    // TODO: Update widget positions in state if needed
  }, []);

  const handleSave = useCallback(() => {
    saveToStorage();
  }, [saveToStorage]);

  const handleHelpClick = useCallback(() => {
    console.log('Show help');
    // Implement help logic here
  }, []);

  const showEmptyState = widgets.length === 0;

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Navbar - with smooth transition */}
      <div
        className={`navbar-container overflow-hidden ${
          currentTab?.mode === 'edit'
            ? 'navbar-visible max-h-20'
            : 'navbar-hidden max-h-0'
        }`}
      >
        <DashboardSidebar
          onMenuItemClick={handleMenuItemClick}
        />
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Dashboard Tabs */}
        <DashboardTabs
          tabs={tabsState.tabs}
          currentTabId={tabsState.currentTabId}
          onTabChange={switchToTab}
          onTabCreate={createTab}
          onTabRename={renameTab}
          onTabDelete={deleteTab}
          onTabReorder={reorderTabs}
          onModeChange={changeTabMode}
          onSave={handleSave}
        />

        {/* Content Area */}
        <div className="flex-1 flex flex-col min-h-0">
          {showEmptyState ? (
            <EmptyDashboard
              title="Dashboard Analytics"
              description="Chọn một mục từ menu để thêm widget vào dashboard"
              actionText="Trợ giúp về Dashboard"
              onAction={handleHelpClick}
            />
          ) : (
            <>
              {/* Scrollable Dashboard Grid */}
              <div
                className="flex-1 overflow-auto dashboard-main-scroll scrollbar-hide"
                style={{
                  maxHeight: '70vh'
                }}
              >
                <div className="p-6 pt-2">
                  <div className="dashboard-container">
                    <DashboardCard
                      widgets={widgets}
                      onLayoutChange={handleLayoutChange}
                      onRemoveWidget={handleRemoveWidget}
                      isDraggable={true}
                      isResizable={true}
                      mode={currentTab?.mode || 'edit'}
                      className="dashboard-grid-container"
                    />
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Floating Edit Button - only show in view mode */}
        {currentTab?.mode === 'view' && (
          <div className="fixed bottom-6 right-6 z-50">
            <button
              onClick={() => changeTabMode('edit')}
              className="floating-edit-btn flex items-center gap-2 px-4 py-3 bg-primary text-primary-foreground rounded-full hover:bg-primary/90 transition-all duration-200 hover:scale-105"
              title="Chuyển sang chế độ chỉnh sửa để hiện navbar"
              type="button"
            >
              <Icon name="edit" className="w-5 h-5" />
              <span className="text-sm font-medium">Chỉnh sửa</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;
