import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Select,
  Button,
  Card,
  Typography,
  FormGrid,
  CollapsibleCard,
  Switch,
  IconCard,
} from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FieldValues } from 'react-hook-form';
import { useFormErrors } from '@/shared/hooks/form';
import { NotificationUtil } from '@/shared/utils/notification';
import { smsTemplateFormSchema, type SmsTemplateFormData, type SmsTemplateVariableData } from '../schemas';
import { useCreateSmsTemplate, useUpdateSmsTemplate, useSmsTemplate } from '../hooks';
import { SmsTemplateStatus } from '../../types';

interface SmsTemplateFormProps {
  templateId?: string;
  onSuccess?: () => void;
  onCancel?: () => void;
  mode?: 'create' | 'edit';
}

const SmsTemplateForm: React.FC<SmsTemplateFormProps> = ({
  templateId,
  onSuccess,
  onCancel,
  mode = 'create',
}) => {
  const { t } = useTranslation(['common', 'sms']);
  const { formRef, setFormErrors } = useFormErrors<SmsTemplateFormData>();
  
  // State
  const [templateName, setTemplateName] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [category, setCategory] = useState<string>('marketing');
  const [content, setContent] = useState<string>('');
  const [variables, setVariables] = useState<SmsTemplateVariableData[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [language, setLanguage] = useState<string>('vi');
  const [isDefault, setIsDefault] = useState<boolean>(false);
  const [status, setStatus] = useState<string>('pending');
  const [newTag, setNewTag] = useState<string>('');

  // Hooks
  const { data: existingTemplate, isLoading: templateLoading } = useSmsTemplate(templateId || '');
  const createTemplateMutation = useCreateSmsTemplate();
  const updateTemplateMutation = useUpdateSmsTemplate();

  // Load existing template data
  React.useEffect(() => {
    if (existingTemplate && mode === 'edit') {
      setTemplateName(existingTemplate.name);
      setDescription(''); // SmsTemplateDto doesn't have description
      setCategory('marketing'); // SmsTemplateDto doesn't have category, default to marketing
      setContent(existingTemplate.content || ''); // content is string in SmsTemplateDto
      setVariables([]); // SmsTemplateDto doesn't have variables, use params instead
      setTags([]); // SmsTemplateDto doesn't have tags
      setLanguage('vi'); // SmsTemplateDto doesn't have language, default to vi
      setIsDefault(false); // SmsTemplateDto doesn't have isDefault
      setStatus(existingTemplate.status);

      // Convert params to variables if needed
      if (existingTemplate.params && existingTemplate.params.length > 0) {
        const convertedVariables: SmsTemplateVariableData[] = existingTemplate.params.map((param, index) => ({
          name: param,
          description: `Variable ${index + 1}`,
          type: 'text',
          required: false,
        }));
        setVariables(convertedVariables);
      }
    }
  }, [existingTemplate, mode]);

  // Options
  const categoryOptions = [
    { value: 'marketing', label: t('sms:template.categories.marketing', 'Marketing') },
    { value: 'transactional', label: t('sms:template.categories.transactional', 'Giao dịch') },
    { value: 'reminder', label: t('sms:template.categories.reminder', 'Nhắc nhở') },
    { value: 'alert', label: t('sms:template.categories.alert', 'Cảnh báo') },
    { value: 'otp', label: t('sms:template.categories.otp', 'OTP') },
    { value: 'notification', label: t('sms:template.categories.notification', 'Thông báo') },
    { value: 'welcome', label: t('sms:template.categories.welcome', 'Chào mừng') },
    { value: 'promotional', label: t('sms:template.categories.promotional', 'Khuyến mãi') },
  ];

  const statusOptions = [
    { value: 'pending', label: t('sms:template.status.pending', 'Chờ duyệt') },
    { value: 'active', label: t('sms:template.status.active', 'Hoạt động') },
    { value: 'inactive', label: t('sms:template.status.inactive', 'Không hoạt động') },
    { value: 'rejected', label: t('sms:template.status.rejected', 'Bị từ chối') },
  ];

  const variableTypeOptions = [
    { value: 'text', label: t('sms:template.variableTypes.text', 'Văn bản') },
    { value: 'number', label: t('sms:template.variableTypes.number', 'Số') },
    { value: 'date', label: t('sms:template.variableTypes.date', 'Ngày') },
    { value: 'url', label: t('sms:template.variableTypes.url', 'URL') },
    { value: 'phone', label: t('sms:template.variableTypes.phone', 'Số điện thoại') },
  ];

  const languageOptions = [
    { value: 'vi', label: t('sms:template.languages.vi', 'Tiếng Việt') },
    { value: 'en', label: t('sms:template.languages.en', 'English') },
  ];

  // Handlers
  const handleSubmit = async (data: SmsTemplateFormData) => {
    try {
      // Data is already validated by the Form component with schema
      const validatedData = data as SmsTemplateFormData;

      if (mode === 'create') {
        // CreateSmsTemplateDto expects: brandnameId, name, content, params
        await createTemplateMutation.mutateAsync({
          brandnameId: 1, // TODO: Get from form or context
          name: validatedData.name,
          content: validatedData.content.text,
          params: validatedData.content.variables.map(v => v.name),
        });

        NotificationUtil.success({ message: t('sms:template.createSuccess', 'Tạo mẫu tin nhắn thành công') });
      } else {
        // UpdateSmsTemplateDto expects: name?, content?, params?, status?
        await updateTemplateMutation.mutateAsync({
          id: templateId!,
          data: {
            name: validatedData.name,
            content: validatedData.content.text,
            params: validatedData.content.variables.map(v => v.name),
            status: validatedData.status as SmsTemplateStatus,
          },
        });

        NotificationUtil.success({ message: t('sms:template.updateSuccess', 'Cập nhật mẫu tin nhắn thành công') });
      }

      onSuccess?.();
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'issues' in error) {
        // Zod validation errors
        const fieldErrors: Record<string, string> = {};
        const issues = (error as { issues: Array<{ path: string[]; message: string }> }).issues;
        issues.forEach((issue) => {
          const path = issue.path.join('.');
          fieldErrors[path] = issue.message;
        });
        setFormErrors(fieldErrors as Record<string, string>);
      } else {
        const errorMessage = error instanceof Error ? error.message : t('common:error.unknown', 'Có lỗi xảy ra');
        NotificationUtil.error({ message: errorMessage });
      }
    }
  };

  const handleAddVariable = () => {
    const newVariable: SmsTemplateVariableData = {
      name: '',
      description: '',
      type: 'text',
      required: false,
    };
    setVariables([...variables, newVariable]);
  };

  const handleUpdateVariable = (index: number, field: keyof SmsTemplateVariableData, value: string | boolean) => {
    const updatedVariables = [...variables];
    const currentVariable = updatedVariables[index];

    // Ensure all required properties are present
    const updatedVariable: SmsTemplateVariableData = {
      name: currentVariable?.name || '',
      description: currentVariable?.description || '',
      type: currentVariable?.type || 'text',
      required: currentVariable?.required || false,
      defaultValue: currentVariable?.defaultValue,
      validation: currentVariable?.validation,
      ...currentVariable,
      [field]: value,
    };

    updatedVariables[index] = updatedVariable;
    setVariables(updatedVariables);
  };

  const handleRemoveVariable = (index: number) => {
    setVariables(variables.filter((_, i) => i !== index));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const characterCount = content.length;
  const smsCount = Math.ceil(characterCount / 160);

  if (templateLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="w-full bg-background text-foreground">
      <Form ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>} schema={smsTemplateFormSchema} onSubmit={handleSubmit as (data: unknown) => void}>
        <div className="space-y-6">
          {/* Basic Information */}
          <Card>
            <div className="space-y-4">
              <Typography variant="h3">
                {t('sms:template.basicInfo', 'Thông tin cơ bản')}
              </Typography>
              
              <FormGrid columns={2} columnsSm={1} gap="md">
                <FormItem name="name" label={t('sms:template.name', 'Tên mẫu')} required>
                  <Input
                    value={templateName}
                    onChange={(e) => setTemplateName(e.target.value)}
                    placeholder={t('sms:template.namePlaceholder', 'Nhập tên mẫu tin nhắn')}
                    fullWidth
                  />
                </FormItem>

                <FormItem name="category" label={t('sms:template.category', 'Danh mục')} required>
                  <Select
                    value={category}
                    onChange={(value) => setCategory(value as string)}
                    options={categoryOptions}
                    placeholder={t('sms:template.selectCategory', 'Chọn danh mục')}
                    fullWidth
                  />
                </FormItem>
              </FormGrid>

              <FormItem name="description" label={t('sms:template.description', 'Mô tả')}>
                <Textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={t('sms:template.descriptionPlaceholder', 'Nhập mô tả cho mẫu tin nhắn')}
                  rows={3}
                  fullWidth
                />
              </FormItem>
            </div>
          </Card>

          {/* Content */}
          <CollapsibleCard title={t('sms:template.content', 'Nội dung')}>
            <div className="space-y-4">
              <FormItem name="content.text" label={t('sms:template.messageContent', 'Nội dung tin nhắn')} required>
                <div>
                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    placeholder={t('sms:template.contentPlaceholder', 'Nhập nội dung mẫu tin nhắn. Sử dụng {{variable}} để thêm biến.')}
                    rows={6}
                    fullWidth
                  />
                  <div className="flex justify-between text-sm text-muted-foreground mt-1">
                    <span>{t('sms:template.characterCount', 'Số ký tự: {{count}}', { count: characterCount })}</span>
                    <span>{t('sms:template.smsCount', 'Số tin: {{count}}', { count: smsCount })}</span>
                  </div>
                </div>
              </FormItem>
            </div>
          </CollapsibleCard>

          {/* Variables */}
          <CollapsibleCard title={t('sms:template.variables', 'Biến')}>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <Typography variant="body2" className="text-muted-foreground">
                  {t('sms:template.variablesDescription', 'Định nghĩa các biến có thể sử dụng trong mẫu tin nhắn')}
                </Typography>
                <Button type="button" variant="outline" size="sm" onClick={handleAddVariable}>
                  {t('sms:template.addVariable', 'Thêm biến')}
                </Button>
              </div>

              {variables.map((variable, index) => (
                <Card key={index} className="p-4">
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="font-medium">
                        {t('sms:template.variable', 'Biến {{index}}', { index: index + 1 })}
                      </Typography>
                      <IconCard
                        icon="trash-2"
                        variant="danger"
                        size="sm"
                        onClick={() => handleRemoveVariable(index)}
                      />
                    </div>

                    <FormGrid columns={2} columnsSm={1} gap="md">
                      <FormItem label={t('sms:template.variableName', 'Tên biến')} required>
                        <Input
                          value={variable.name}
                          onChange={(e) => handleUpdateVariable(index, 'name', e.target.value)}
                          placeholder="customerName"
                          fullWidth
                        />
                      </FormItem>

                      <FormItem label={t('sms:template.variableType', 'Loại')} required>
                        <Select
                          value={variable.type}
                          onChange={(value) => handleUpdateVariable(index, 'type', value as string)}
                          options={variableTypeOptions}
                          fullWidth
                        />
                      </FormItem>
                    </FormGrid>

                    <FormItem label={t('sms:template.variableDescription', 'Mô tả')} required>
                      <Input
                        value={variable.description}
                        onChange={(e) => handleUpdateVariable(index, 'description', e.target.value)}
                        placeholder={t('sms:template.variableDescriptionPlaceholder', 'Mô tả biến này')}
                        fullWidth
                      />
                    </FormItem>

                    <FormItem label={t('sms:template.variableRequired', 'Bắt buộc')}>
                      <Switch
                        checked={variable.required}
                        onChange={(checked) => handleUpdateVariable(index, 'required', checked)}
                      />
                    </FormItem>
                  </div>
                </Card>
              ))}
            </div>
          </CollapsibleCard>

          {/* Tags */}
          <CollapsibleCard title={t('sms:template.tags', 'Thẻ')}>
            <div className="space-y-4">
              <div className="flex space-x-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder={t('sms:template.addTagPlaceholder', 'Nhập thẻ mới')}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
                  className="flex-1"
                />
                <Button type="button" variant="outline" onClick={handleAddTag}>
                  {t('sms:template.addTag', 'Thêm')}
                </Button>
              </div>

              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="inline-flex items-center px-2 py-1 rounded-md bg-secondary text-secondary-foreground text-sm cursor-pointer hover:bg-secondary/80"
                      onClick={() => handleRemoveTag(tag)}
                    >
                      {tag} ×
                    </div>
                  ))}
                </div>
              )}
            </div>
          </CollapsibleCard>

          {/* Settings */}
          <CollapsibleCard title={t('sms:template.settings', 'Cài đặt')}>
            <div className="space-y-4">
              <FormGrid columns={2} columnsSm={1} gap="md">
                <FormItem name="language" label={t('sms:template.language', 'Ngôn ngữ')}>
                  <Select
                    value={language}
                    onChange={(value) => setLanguage(value as string)}
                    options={languageOptions}
                    fullWidth
                  />
                </FormItem>

                <FormItem name="status" label={t('sms:template.status', 'Trạng thái')} required>
                  <Select
                    value={status}
                    onChange={(value) => setStatus(value as string)}
                    options={statusOptions}
                    fullWidth
                  />
                </FormItem>
              </FormGrid>

              <FormItem name="isDefault" label={t('sms:template.isDefault', 'Mẫu mặc định')}>
                <Switch
                  checked={isDefault}
                  onChange={setIsDefault}
                />
              </FormItem>
            </div>
          </CollapsibleCard>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                {t('common:cancel', 'Hủy')}
              </Button>
            )}
            
            <Button
              type="submit"
              variant="primary"
              isLoading={createTemplateMutation.isPending || updateTemplateMutation.isPending}
            >
              {mode === 'create' 
                ? t('sms:template.create', 'Tạo mẫu')
                : t('sms:template.update', 'Cập nhật')
              }
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default SmsTemplateForm;
