import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

/**
 * Trang hiển thị tổng số lượng dữ liệu
 */
const DataCountPage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'data-count-overview',
      title: 'Tổng số lượng dữ liệu',
      type: 'data-count' as const,
      x: 0,
      y: 0,
      w: 12,
      h: 6,
      minW: 6,
      minH: 4,
      maxW: 12,
      maxH: 12,
      isEmpty: false
    }
  ];

  return (
    <div className="h-full">
      <DashboardCard
        widgets={widgets}
        isDraggable={true}
        isResizable={true}
        className="h-full"
      />
    </div>
  );
};

export default DataCountPage;
