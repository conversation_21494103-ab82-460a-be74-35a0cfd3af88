{"integration": {"title": "<PERSON><PERSON><PERSON>", "bankAccounts": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>ài khoản ngân hàng"}, "providerModel": {"title": "<PERSON><PERSON><PERSON>n lý <PERSON> keys Model", "form": {"view": "Xem Provider Model", "edit": "Chỉnh sửa Provider Model", "create": "Tạo Provider Model", "viewDescription": "<PERSON><PERSON> thông tin chi tiết của Provider Model", "editDescription": "Chỉnh sửa thông tin Provider Model. Chỉ có thể thay đổi tên và API key.", "createDescription": "<PERSON><PERSON>o mới Provider Model đ<PERSON> tích hợp với các nhà cung cấp AI", "cannotChange": "<PERSON><PERSON><PERSON><PERSON> thể thay đổi", "fields": {"type": "Loại Provider", "name": "T<PERSON>n Provider Model", "namePlaceholder": "Nhập tên provider model", "apiKey": "API Key", "apiKeyPlaceholder": "Nhập API key", "apiKeyPlaceholderEdit": "<PERSON><PERSON> trống nếu không muốn thay đổi API key..."}}, "actions": {"cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "create": "Tạo Provider Model", "edit": "Chỉnh sửa", "delete": "Xóa", "retry": "<PERSON><PERSON><PERSON> lại"}, "empty": {"title": "Chưa có Provider Model nào", "description": "<PERSON>h<PERSON><PERSON> Provider Model đ<PERSON> bắt đầu sử dụng."}, "error": {"title": "Lỗi tải dữ liệu", "description": "<PERSON><PERSON> lỗi xảy ra khi tải danh sách Provider Model. <PERSON><PERSON> lòng thử lại."}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a", "delete": "Bạn có chắc chắn muốn xóa Provider Model này?", "bulkDelete": "Bạn có chắc chắn muốn xóa {{count}} provider model đã chọn?", "noItemsSelected": "<PERSON><PERSON><PERSON>ng có item nào đư<PERSON> chọn"}, "validation": {"name": {"required": "<PERSON><PERSON><PERSON> không đ<PERSON><PERSON><PERSON> để trống", "maxLength": "<PERSON>ê<PERSON> khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "type": {"invalid": "Loại nhà cung cấp không hợp lệ"}, "apiKey": {"required": "API key không đ<PERSON><PERSON><PERSON> để trống", "minLength": "API key ph<PERSON>i có ít nhất 10 ký tự", "format": "API key chỉ đư<PERSON><PERSON> chứa chữ cái, s<PERSON>, dấu gạch ngang, gạch dưới và dấu chấm"}}}, "shipping": {"title": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> chuyển", "description": "<PERSON><PERSON><PERSON> hợp với các nhà vận chuyển GHN, GHTK, Viettel Post", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà vận chuyển", "editProvider": "Chỉnh sửa nhà vận chuyển", "viewProvider": "<PERSON>em chi tiết nhà vận chuyển", "addFirstProvider": "Thê<PERSON> nhà vận chuyển đầu tiên", "selectProviderType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "testConnection": "Test Connection", "test": "Test", "runTest": "Run Test", "rateCalculator": "Rate Calculator", "create": "Tạo", "default": "Mặc định", "ghtk": {"title": "Giao h<PERSON>ng ti<PERSON> k<PERSON> (GHTK)", "description": "<PERSON><PERSON><PERSON> hợp v<PERSON><PERSON> dịch vụ giao hàng tiết kiệm - chi <PERSON><PERSON><PERSON> thấp, thời gian giao hàng 3-5 ng<PERSON>y", "breadcrumb": "<PERSON><PERSON><PERSON> h<PERSON>ng ti<PERSON><PERSON> k<PERSON>"}, "ghn": {"title": "Giao <PERSON> (GHN)", "description": "<PERSON><PERSON><PERSON> hợp v<PERSON><PERSON> dịch vụ giao hàng nhanh - giao hàng trong 1-2 ng<PERSON><PERSON>, p<PERSON><PERSON> cao hơn", "breadcrumb": "<PERSON><PERSON><PERSON>h"}, "form": {"createTitle": "<PERSON><PERSON><PERSON><PERSON> nhà vận chuyển", "editTitle": "Chỉnh sửa nhà vận chuyển"}, "list": {"columns": {"providerName": "<PERSON><PERSON><PERSON> nhà vận chuyển", "providerType": "<PERSON><PERSON><PERSON>", "shopId": "Shop ID", "default": "Mặc định", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "filters": {"providerType": "<PERSON><PERSON><PERSON> nhà vận chuyển", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "actions": {"edit": "Chỉnh sửa", "test": "<PERSON><PERSON><PERSON> tra", "setDefault": "Đặt làm mặc định", "delete": "Xóa"}, "confirmations": {"deleteTitle": "<PERSON><PERSON><PERSON> nhà vận chuyển", "delete": "Bạn có chắc chắn muốn xóa nhà vận chuyển này?"}, "empty": {"title": "<PERSON><PERSON><PERSON> có nhà vận chuyển nào", "description": "<PERSON>ạn chưa thêm nhà vận chuyển nào. <PERSON><PERSON><PERSON> thêm nhà vận chuyển đầu tiên."}, "validation": {"providerType": {"invalid": "<PERSON>ạ<PERSON> nhà vận chuyển không hợp lệ"}, "providerName": {"required": "<PERSON>ên nhà vận chuyển là bắt buộc", "maxLength": "Tên nhà vận chuyển không được quá 100 ký tự"}, "apiKey": {"required": "API Key là bắt buộc", "maxLength": "API Key không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "apiSecret": {"maxLength": "API Secret không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "shopId": {"maxLength": "Shop ID không đư<PERSON><PERSON> quá 100 ký tự"}, "clientId": {"maxLength": "Client ID không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "settings": {"invalidJson": "<PERSON><PERSON><PERSON> hình không đúng định dạng JSON"}, "name": {"required": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c"}, "phone": {"required": "<PERSON><PERSON> điện thoại là bắt buộc"}, "address": {"required": "Địa chỉ là bắt buộc"}, "weight": {"min": "<PERSON><PERSON><PERSON><PERSON> lư<PERSON><PERSON> tối thiểu là 0.1kg"}, "serviceType": {"required": "Loại d<PERSON>ch v<PERSON> là bắ<PERSON> bu<PERSON>c"}}}, "common": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp với dịch vụ bên ngo<PERSON>i", "back": "Quay lại", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "confirm": "<PERSON><PERSON><PERSON>", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "Tắt", "retry": "<PERSON><PERSON><PERSON> lại", "error": "Đ<PERSON> xảy ra lỗi"}, "sms": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp các nhà cung cấp dịch v<PERSON>", "providers": "<PERSON>hà cung cấp SMS", "providersDescription": "<PERSON><PERSON><PERSON><PERSON> lý các nhà cung cấp dịch v<PERSON>", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp", "editProvider": "Chỉnh sửa nhà cung cấp SMS", "deleteProvider": "<PERSON>óa nhà cung cấp", "testProvider": "Test nhà cung cấp SMS", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "<PERSON><PERSON> dụ: SMS Marketing, SMS Thông báo", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "name": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "nameHelp": "<PERSON>ên để nhận diện cấu hình này", "namePlaceholder": "VD: Twilio Production", "displayName": "<PERSON><PERSON><PERSON> hiển thị", "displayNameHelp": "<PERSON><PERSON><PERSON> hiển thị trong giao di<PERSON>n", "displayNamePlaceholder": "VD: SMS Chính", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "credentials": "Thông tin xác thực", "isActive": "<PERSON><PERSON><PERSON>", "isDefault": "Đặt làm mặc định", "default": "Mặc định", "provider": "<PERSON>hà cung cấp SMS", "providerType": "Loại nhà cung cấp", "selectProvider": "<PERSON><PERSON><PERSON> nhà cung cấp", "otherProvider": "K<PERSON><PERSON><PERSON>", "fromPhone": "<PERSON><PERSON> điện tho<PERSON>i g<PERSON>i", "fromPhoneHelp": "<PERSON><PERSON> điện thoại hiển thị khi gửi SMS", "fromNumber": "Số gửi", "fromName": "<PERSON><PERSON><PERSON>", "lastTested": "Test lần cuối", "apiKey": "API Key", "apiKeyHelp": "API Key từ nhà cung cấp", "apiSecret": "API Secret", "apiSecretHelp": "API Secret từ nhà cung cấp", "accountSidHelp": "Account SID từ <PERSON><PERSON><PERSON>", "authTokenHelp": "<PERSON>th Token từ <PERSON><PERSON><PERSON>", "accessKeyIdHelp": "AWS Access Key ID", "secretAccessKeyHelp": "AWS Secret Access Key", "regionHelp": "AWS Region (vd: us-east-1)", "usernameHelp": "<PERSON><PERSON><PERSON>", "passwordHelp": "Password tài <PERSON>n", "endpointHelp": "URL endpoint của API", "configurationSteps": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON> c<PERSON>u hình", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "Test thành công", "testFailed": "Test thất bại", "test": "Test", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "update": "<PERSON><PERSON><PERSON>", "testPhoneNumber": "<PERSON><PERSON> điện tho<PERSON><PERSON> nhận", "testPhoneNumberHelp": "<PERSON><PERSON><PERSON><PERSON> số điện thoại để nhận tin nhắn test", "testMessage": "<PERSON><PERSON><PERSON> dung tin nhắn", "testMessageHelp": "Nội dung tin nhắn test (tối đa 160 ký tự)", "testMessagePlaceholder": "<PERSON><PERSON><PERSON> là tin nhắn test từ hệ thống", "sendTest": "Gửi test", "searchPlaceholder": "T<PERSON>m kiếm theo tên...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "filterByType": "<PERSON><PERSON><PERSON> the<PERSON>", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "allTypes": "<PERSON><PERSON><PERSON> c<PERSON> lo<PERSON>i", "active": "<PERSON><PERSON><PERSON> động", "showingResults": "<PERSON><PERSON><PERSON> thị {{start}}-{{end}} trong tổng số {{total}} kết quả", "loading": "<PERSON><PERSON> tả<PERSON>...", "noResultsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "tryDifferentFilters": "Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm", "noProviders": "Chưa có nhà cung cấp nào", "addFirstProvider": "<PERSON>hê<PERSON> nhà cung cấp SMS đầu tiên để bắt đầu", "deleteConfirmation": "<PERSON>ạn có chắc chắn muốn xóa nhà cung cấp \"{{name}}\"?", "deleteWarning": "<PERSON><PERSON>nh động này không thể hoàn tác. T<PERSON>t cả cấu hình liên quan sẽ bị xóa.", "createSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật nhà cung cấp thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> nhà cung cấp thành công", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "testSentSuccess": "<PERSON><PERSON><PERSON> tin nhắn test thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu", "deleteError": "Có lỗi xảy ra khi xóa", "statusUpdateError": "<PERSON><PERSON> lỗi xảy ra", "testSentError": "Có lỗi xảy ra khi gửi test", "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON>", "error": "Lỗi", "testing": "Đang test", "pending": "Chờ xử lý"}, "twilio": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> t<PERSON>ch hợp v<PERSON><PERSON> dịch vụ SMS Twilio", "form": {"title": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin để tạo tích hợp <PERSON><PERSON><PERSON> mới", "integrationName": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "integrationNameHelp": "<PERSON>ên để nhận diện tích hợp này", "integrationNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> Production SMS", "authToken": "<PERSON><PERSON><PERSON>", "authTokenHelp": "<PERSON>th Token từ <PERSON><PERSON><PERSON>", "authTokenPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "baseDomain": "Twilio Base Domain", "baseDomainHelp": "Domain cơ sở củ<PERSON> (ví dụ: api.twilio.com)", "baseDomainPlaceholder": "Ví dụ: api.twilio.com", "create": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p", "cancel": "<PERSON><PERSON><PERSON>"}, "success": {"created": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON><PERSON><PERSON> thành công", "createdDescription": "<PERSON><PERSON><PERSON> hợp \"{{name}}\" đã đư<PERSON><PERSON> tạo thành công."}, "error": {"createFailed": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> hợ<PERSON><PERSON><PERSON> thất bại", "createFailedDescription": "<PERSON><PERSON> xảy ra lỗi khi tạo tích hợp. <PERSON><PERSON> lòng thử lại."}}}, "email": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> hình tích hợp với nhà cung cấp <PERSON>", "configName": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "configNameHelp": "Ví dụ: <PERSON><PERSON>, <PERSON><PERSON> c<PERSON> nhân", "configNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "senderEmail": "<PERSON><PERSON>", "senderName": "<PERSON><PERSON><PERSON><PERSON>i", "senderNameHelp": "<PERSON><PERSON>n hiển thị khi người nhận xem email", "senderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ng<PERSON><PERSON>i", "smtpHost": "SMTP Host", "smtpHostHelp": "Ví dụ: smtp.gmail.com, smtp.office365.com", "smtpPort": "SMTP Port", "smtpPortHelp": "Ví dụ: 587 cho TLS, 465 cho SSL", "requireSSL": "<PERSON><PERSON><PERSON> c<PERSON>u SSL", "securityProtocol": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> b<PERSON><PERSON> mật", "requireAuth": "<PERSON><PERSON><PERSON> c<PERSON>u x<PERSON>c thực", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testResult": "<PERSON><PERSON><PERSON> quả kiểm tra kết nối", "testSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công đến máy chủ email", "testError": "<PERSON><PERSON><PERSON><PERSON> thể kết nối đến máy chủ email. <PERSON><PERSON> lòng kiểm tra lại cấu hình.", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình <PERSON> thành công", "saveError": "Lỗi khi lưu cấu hình email", "smtp": {"title": "<PERSON><PERSON><PERSON> h<PERSON>nh SM<PERSON>", "description": "<PERSON><PERSON><PERSON> hình máy chủ SMTP cho gửi email đi", "form": {"fields": {"serverName": "<PERSON><PERSON><PERSON> m<PERSON> chủ", "host": "Máy chủ SMTP", "port": "Cổng", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "password": "<PERSON><PERSON><PERSON>", "useSsl": "Sử dụng SSL", "useStartTls": "<PERSON>ử dụng StartTLS", "isActive": "<PERSON><PERSON><PERSON>", "additionalSettings": "<PERSON><PERSON><PERSON> đặt b<PERSON> sung (JSON)"}, "placeholders": {"serverName": "<PERSON><PERSON><PERSON><PERSON> tên máy chủ SMTP", "host": "smtp.gmail.com", "port": "587", "username": "<EMAIL>", "password": "<PERSON><PERSON><PERSON><PERSON> mật khẩu hoặc app password", "additionalSettings": "{\"timeout\": 30000}"}, "test": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "testDescription": "<PERSON><PERSON><PERSON><PERSON> email để nhận email test từ cấu hình SMTP này"}, "actions": {"save": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh", "sendTest": "Gửi test"}, "saveSuccess": "Cấu hình SMTP đã được lưu thành công!", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu cấu hình SMTP"}}, "social": {"title": "<PERSON><PERSON><PERSON> h<PERSON>p mạng xã hội", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với các mạng xã hội", "facebook": "Facebook", "instagram": "Instagram", "twitter": "Twitter", "linkedin": "LinkedIn", "youtube": "YouTube", "tiktok": "TikTok", "connect": "<PERSON><PERSON><PERSON>", "disconnect": "<PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "connectSuccess": "<PERSON><PERSON><PERSON> n<PERSON>i thành công với {{platform}}", "connectError": "Lỗi khi kết nối với {{platform}}", "disconnectSuccess": "<PERSON><PERSON> ngắt kết nối với {{platform}}", "disconnectError": "Lỗi khi ngắt kết nối với {{platform}}", "networkAriaLabel": "<PERSON><PERSON><PERSON> xã hội {{name}}"}, "facebook": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài k<PERSON>n Facebook đã liên kết", "addPage": "Thêm Facebook Page", "connecting": "<PERSON><PERSON> kết nối...", "processing": "<PERSON><PERSON> x<PERSON> lý kết nối Facebook...", "search": "<PERSON><PERSON><PERSON> Facebook Page...", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Facebook Pages", "noPages": "Chưa có Facebook Page nào", "noPagesDescription": "<PERSON><PERSON>n chưa liên kết Facebook Page nào. <PERSON><PERSON><PERSON> thêm Facebook Page để bắt đầu.", "confirmDelete": "Bạn có chắc chắn muốn xóa Facebook Page này?", "connectAgent": "<PERSON><PERSON><PERSON>", "disconnectAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "status": {"label": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "error": "Lỗi"}, "agent": {"label": "Agent"}, "pageName": "<PERSON><PERSON><PERSON>", "personalName": "<PERSON><PERSON><PERSON> c<PERSON> nhân", "pageId": "Page ID", "isActive": "<PERSON><PERSON> ho<PERSON>t động", "hasError": "<PERSON><PERSON> lỗi"}, "accounts": {"title": "<PERSON><PERSON><PERSON> k<PERSON>n liên kết", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản đã liên kết với hệ thống", "addAccount": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "removeAccount": "<PERSON><PERSON><PERSON> t<PERSON>", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountType": "<PERSON><PERSON><PERSON> tà<PERSON>", "linkedDate": "<PERSON><PERSON><PERSON> li<PERSON>", "noAccounts": "<PERSON><PERSON><PERSON> có tài khoản nào đ<PERSON><PERSON><PERSON> liên kết", "confirmRemove": "Bạn có chắc chắn muốn xóa tài khoản này?", "removeSuccess": "<PERSON><PERSON><PERSON> tài kho<PERSON>n thành công", "removeError": "Lỗi khi xóa tài k<PERSON>n", "defaultAccount": "<PERSON><PERSON><PERSON> k<PERSON>n mặc định", "failedToLoad": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách tài k<PERSON>n"}, "website": {"title": "<PERSON><PERSON><PERSON> Website", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp với website", "domain": "<PERSON><PERSON><PERSON>", "apiKey": "API Key", "secretKey": "Secret Key", "webhookUrl": "Webhook URL", "generateKey": "Tạo key mới", "copyKey": "Sao chép", "keyCopied": "Đã sao chép vào clipboard", "saveSuccess": "<PERSON><PERSON><PERSON> c<PERSON>u hình website thành công", "saveError": "Lỗi khi lưu cấu hình website", "confirmActivate": "Bạn có chắc chắn muốn bật website này?", "confirmDeactivate": "Bạn có chắc chắn muốn tắt website này?", "host": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "verified": "<PERSON><PERSON> xác thực", "notVerified": "<PERSON><PERSON><PERSON> x<PERSON> thực", "connected": "<PERSON><PERSON> kết nối", "notConnected": "<PERSON><PERSON><PERSON> k<PERSON>", "agent": "Agent", "noAgent": "<PERSON><PERSON><PERSON> k<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "widgetScript": "Widge<PERSON>", "widgetScriptDesc": "Sao chép và dán đoạn script này vào website của bạn để tích hợp chat widget.", "createTitle": "Thêm Website Mới", "createSuccess": "Tạo website thành công!", "createSuccessDesc": "Website đã đ<PERSON><PERSON><PERSON> thêm vào danh sách.", "createError": "Tạo website thất bại!", "createErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "creating": "<PERSON><PERSON> tạo...", "create": "Tạo Website", "deleteSuccess": "Xóa website thành công!", "deleteSuccessDesc": "Website đã đư<PERSON>c xóa khỏi danh sách.", "deleteError": "Xóa website thất bại!", "deleteErrorDesc": "<PERSON><PERSON> lòng thử lại sau.", "copySuccess": "Đã sao chép!", "copySuccessDesc": "Script đã đư<PERSON>c sao chép vào clipboard.", "copyScript": "Sao chép", "noWebsites": "Chưa có Website nào", "noWebsitesDescription": "Bạn chưa thêm Website nào. Hãy thêm Website để bắt đầu.", "addWebsite": "Thêm Website", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noSearchResultsDescription": "Không có website nào phù hợp với tiêu chí tìm kiếm của bạn.", "clearFilters": "Xóa bộ lọc", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "confirmDeleteDesc": "Bạn có chắc chắn muốn xóa website này? Hành động này không thể hoàn tác.", "deleting": "Đang xóa...", "form": {"websiteName": "Tên Website", "websiteNamePlaceholder": "Nhập tên website", "host": "Host/Domain", "hostPlaceholder": "redai.vn hoặc https://www.redai.vn", "hostDescription": "Nhập domain hoặc URL đầy đủ. <PERSON><PERSON> thống sẽ tự động chuẩn hóa."}, "filter": {"websiteName": "Tên Website", "host": "Host", "createdAt": "<PERSON><PERSON><PERSON>", "verify": "<PERSON>r<PERSON><PERSON> thái xác thực", "asc": "<PERSON><PERSON><PERSON>", "desc": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "verified": "<PERSON><PERSON> xác thực", "unverified": "<PERSON><PERSON><PERSON> x<PERSON> thực"}}, "bankAccount": {"title": "<PERSON><PERSON><PERSON> hợp tài k<PERSON>n ngân hàng", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tài khoản ngân hàng đã tích hợp với hệ thống", "createTitle": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n ngân hàng", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng để tích hợp với hệ thống", "bankName": "<PERSON><PERSON> h<PERSON>", "selectBank": "<PERSON><PERSON><PERSON> ng<PERSON> hàng", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số tài <PERSON>n", "accountNumberHelp": "<PERSON><PERSON><PERSON><PERSON> số tài k<PERSON>n ngân hàng (6-20 ký tự số)", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "idNumber": "Số CMND/CCCD", "idNumberPlaceholder": "Nhập số CMND/CCCD", "idNumberHelp": "Nhập số CMND/CCCD đã đăng ký với ngân hàng", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "phoneNumberHelp": "Số điện thoại đã đăng ký với ngân hàng để nhận OTP", "storeName": "<PERSON><PERSON><PERSON> đi<PERSON> bán", "storeNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên điểm bán", "storeNameHelp": "<PERSON><PERSON><PERSON> cửa hàng/đi<PERSON><PERSON> bán hàng của bạn", "storeAddress": "Đ<PERSON>a chỉ điểm bán", "storeAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ điểm bán", "storeAddressHelp": "Đ<PERSON>a chỉ chi tiết của cửa hàng/điểm bán hàng", "create": "<PERSON><PERSON><PERSON> t<PERSON>", "bankInfo": "Thông tin ngân hàng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "statusActive": "<PERSON><PERSON><PERSON> đ<PERSON>", "statusPending": "<PERSON><PERSON> xác thực", "statusInactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "statusSuspended": "<PERSON><PERSON><PERSON>", "statusExpired": "<PERSON><PERSON><PERSON>", "virtualAccount": {"title": "<PERSON><PERSON><PERSON>", "yes": "<PERSON><PERSON>", "no": "K<PERSON>ô<PERSON>"}, "createVA": "<PERSON><PERSON><PERSON> tà<PERSON> k<PERSON>o", "createSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n thành công", "createSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> tạo thành công", "createError": "<PERSON><PERSON><PERSON> tài k<PERSON>n thất bại", "createErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài khoản ngân hàng. <PERSON><PERSON> lòng kiểm tra lại thông tin.", "createCompleteSuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n hoàn tất", "createCompleteSuccessDescription": "<PERSON><PERSON><PERSON> kho<PERSON>n ngân hàng đã được tạo và kích hoạt thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON>c xóa thành công", "deleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "deleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON>a tài <PERSON>n", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa tài khoản ngân hàng này? Hành động này không thể hoàn tác.", "bulkDeleteSuccess": "<PERSON><PERSON><PERSON> thành công", "bulkDeleteSuccessDescription": "<PERSON><PERSON> xóa {{count}} tài kho<PERSON>n ngân hàng", "bulkDeleteError": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "bulkDeleteErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể xóa các tài khoản ngân hàng. <PERSON><PERSON> lòng thử lại.", "bulkDeleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON>a nhiều tài k<PERSON>n", "bulkDeleteConfirmMessage": "Bạn có chắc chắn muốn xóa {{count}} tài khoản ngân hàng đã chọn? Hành động này không thể hoàn tác.", "selectAccountsToDelete": "<PERSON><PERSON> lòng chọn ít nhất một tài khoản để xóa", "createVASuccess": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thành công", "createVASuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ảo đã đư<PERSON><PERSON> tạo thành công", "createVAError": "<PERSON><PERSON><PERSON> tài k<PERSON>n <PERSON>o thất bại", "createVAErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài kho<PERSON>n ảo. <PERSON><PERSON> lòng thử lại.", "activateSuccess": "<PERSON><PERSON><PERSON> ho<PERSON>t thành công", "activateSuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerification": "<PERSON><PERSON><PERSON>", "otpVerificationDescription": "<PERSON><PERSON><PERSON><PERSON> mã O<PERSON> được gửi đến số điện thoại đã đăng ký với {{bankName}}", "enterOtp": "<PERSON>hậ<PERSON> mã OTP", "otpSent": "Mã OTP đã đ<PERSON><PERSON><PERSON> g<PERSON>i", "otpSentDescription": "<PERSON><PERSON> lòng kiểm tra tin nhắn SMS trên điện thoại của bạn", "otpSendError": "Gửi OTP thất bại", "otpSendErrorDescription": "<PERSON><PERSON><PERSON><PERSON> thể gửi mã OTP. <PERSON><PERSON> lòng thử lại sau.", "resendOtp": "Gửi lại mã OTP", "resendOtpCountdown": "G<PERSON>i lại mã sau {{countdown}} giây", "verify": "<PERSON><PERSON><PERSON> th<PERSON>c", "otpVerifySuccess": "<PERSON><PERSON><PERSON> thực thành công", "otpVerifySuccessDescription": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng đã đư<PERSON><PERSON> kích hoạt thành công", "otpVerifyError": "<PERSON><PERSON><PERSON> thực thất bại", "otpVerifyErrorDescription": "Mã OTP không đúng hoặc đã hết hạn. <PERSON><PERSON> lòng thử lại.", "invalidOtpLength": "Mã OTP không đúng độ dài", "invalidOtpLengthDescription": "Mã OTP phải có {{length}} ký tự"}, "banking": {"bankAccount": "<PERSON><PERSON><PERSON> k<PERSON>n ngân hàng", "bank": "<PERSON><PERSON> h<PERSON>", "connectionMethod": "<PERSON><PERSON><PERSON><PERSON> thức kết n<PERSON>i", "estimatedBalance": "Số dư tạm t<PERSON>h", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "connectApi": "Kết nối API Banking", "acb": {"title": "Liên kết tài khoản ACB", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng ACB để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chủ tài kho<PERSON>n ACB", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản ACB", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON>hập số điện thoại đăng ký ACB", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản ACB thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản ACB", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản"}, "mb": {"title": "Liên kết tài khoản MB Bank", "description": "<PERSON>hậ<PERSON> thông tin tài khoản ngân hàng MB Bank để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "Tên sẽ được tự động lấy từ API", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản MB Bank", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký MB Bank", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "Nhập số điện thoại đăng ký MB Bank", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "fetchNameSuccess": "<PERSON><PERSON> lấy tên chủ tài khoản thành công", "fetchNameError": "<PERSON><PERSON><PERSON><PERSON> thể lấy tên chủ tài khoản", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản MB Bank thành công", "saveError": "C<PERSON> lỗi xảy ra khi lưu thông tin tài khoản MB Bank", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản"}, "ocb": {"title": "Liên kết tài khoản OCB", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng OCB để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "Tên sẽ được tự động lấy từ API", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài khoản OCB", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký OCB", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "<PERSON>hậ<PERSON> số điện thoại đăng ký OCB", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "fetchNameSuccess": "<PERSON><PERSON> lấy tên chủ tài khoản thành công", "fetchNameError": "<PERSON><PERSON><PERSON><PERSON> thể lấy tên chủ tài khoản", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản OCB thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản OCB", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản"}, "kienlong": {"title": "Liên kết tài kho<PERSON>n Kiên Long Bank", "description": "<PERSON><PERSON><PERSON><PERSON> thông tin tài khoản ngân hàng Kiên Long để liên kết với hệ thống", "accountHolderName": "<PERSON>ên chủ tài k<PERSON>n", "accountHolderNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chủ tài kho<PERSON>n Ki<PERSON>n Long Bank", "accountNumber": "Số tài <PERSON>n", "accountNumberPlaceholder": "Nhập số tài k<PERSON>n Kiên Long Bank", "identificationNumber": "Số CMND/CCCD", "identificationNumberPlaceholder": "Nhập số CMND/CCCD đăng ký tài k<PERSON>n", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "phoneNumberPlaceholder": "Nhập số điện thoại đăng ký Kiên Long Bank", "label": "<PERSON><PERSON>n g<PERSON>i nh<PERSON>", "labelPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên gợi nh<PERSON> (tù<PERSON> chọn)", "saveSuccess": "<PERSON><PERSON> lưu thông tin tài khoản Kiên Long Bank thành công", "saveError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản Kiên Long Bank", "submitError": "<PERSON><PERSON> lỗi xảy ra khi lưu thông tin tài khoản"}}, "ai": {"title": "<PERSON><PERSON><PERSON><PERSON> lý nhà cung cấp AI", "description": "<PERSON><PERSON><PERSON><PERSON> lý các nhà cung cấp AI và cấu hình kết nối", "id": "ID", "name": "<PERSON><PERSON><PERSON> nhà cung cấp", "desc": "<PERSON><PERSON>", "baseUrl": "URL cơ sở", "apiVersion": "<PERSON><PERSON><PERSON> b<PERSON>", "models": "<PERSON><PERSON> hình hỗ trợ", "maxTokens": "<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "addProvider": "<PERSON><PERSON><PERSON><PERSON> nhà cung cấp AI", "editProvider": "<PERSON><PERSON><PERSON> nhà cung cấp AI", "providersList": "<PERSON><PERSON> s<PERSON>ch nh<PERSON> cung cấp <PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên nhà cung cấp AI", "baseUrlPlaceholder": "Nhập URL cơ sở API", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả", "apiVersionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> bản <PERSON>", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm nhà cung cấp...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa nhà cung cấp AI {{name}}?"}, "apiKeys": {"title": "<PERSON><PERSON><PERSON>n lý <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý các <PERSON> Keys để tích hợp với các dịch vụ bên ngoài", "id": "ID", "apiKey": "API Key", "desc": "<PERSON><PERSON>", "scope": "<PERSON><PERSON><PERSON><PERSON> truy cập", "environment": "<PERSON>ô<PERSON> trường", "expiredAt": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON>", "addNew": "Tạo API Key mới", "createNew": "Tạo API Key mới", "list": "<PERSON>h sách API Keys", "descriptionPlaceholder": "<PERSON>hập mô tả cho API Key", "selectDate": "<PERSON><PERSON><PERSON> ng<PERSON> hết hạn", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo mô tả...", "filterByStatus": "<PERSON><PERSON><PERSON> theo trạng thái", "allStatuses": "<PERSON><PERSON><PERSON> cả trạng thái", "enable": "<PERSON><PERSON><PERSON>", "disable": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "confirmDeleteMessage": "Bạn có chắc chắn muốn xóa API Key này?", "createSuccess": "Đã tạo API Key mới với mô tả: {{description}}", "deleteSuccess": "Đã xóa API Key: {{apiKey}}", "toggleSuccess": "Đã {{action}} API Key: {{apiKey}}"}, "myIntegrations": "<PERSON><PERSON><PERSON> h<PERSON> c<PERSON>a tôi", "myIntegrationsDescription": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp cá nhân của bạn", "name": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>", "type": "<PERSON><PERSON><PERSON> t<PERSON><PERSON> h<PERSON>p", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "endpoint": "Endpoint", "description": "<PERSON><PERSON>", "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "loadError": "Lỗi khi tải danh sách tích hợp", "deleteSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "deleteError": "Lỗi khi xóa tích hợp", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "Lỗi khi cập nhật trạng thái", "testConnectionSuccess": "<PERSON><PERSON><PERSON> tra kết nối thành công", "testConnectionError": "Lỗi khi kiểm tra kết nối", "deleteConfirmTitle": "<PERSON><PERSON><PERSON> n<PERSON>n x<PERSON><PERSON> tích hợp", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa tích hợp \"{{name}}\"? Hành động này không thể hoàn tác.", "createIntegration": "<PERSON><PERSON><PERSON> t<PERSON>ch hợp mới", "editIntegration": "Chỉnh sửa tích hợp", "viewIntegration": "<PERSON>em chi ti<PERSON>t tí<PERSON> hợp", "createIntegrationDescription": "<PERSON><PERSON><PERSON> tích hợp mới để kết nối với dịch vụ bên ngoài", "editIntegrationDescription": "Chỉnh sửa thông tin tích hợp", "viewIntegrationDescription": "<PERSON><PERSON> thông tin chi tiết của tích hợp", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tích hợp", "selectType": "<PERSON><PERSON><PERSON> lo<PERSON>i tích hợp", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cho tích hợp", "endpointPlaceholder": "https://api.example.com", "createSuccess": "<PERSON><PERSON><PERSON> tích hợp thành công", "createError": "Lỗi khi tạo tích hợp", "updateSuccess": "<PERSON><PERSON><PERSON> nhật tích hợp thành công", "updateError": "Lỗi khi cập nhật tích hợp", "calendar": {"title": "T<PERSON>ch hợp Google Calendar", "description": "<PERSON><PERSON><PERSON> hợp và đồng bộ lịch với Google Calendar", "form": {"createTitle": "Thêm Google Calendar", "editTitle": "Chỉnh sửa Google Calendar", "accountName": {"label": "<PERSON><PERSON><PERSON> tà<PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên tà<PERSON>n", "helpText": "<PERSON>ên để nhận diện cấu hình này"}, "clientId": {"label": "Client ID", "placeholder": "Nhập Client ID từ Google Console", "helpText": "Client ID từ Google Cloud Console"}, "clientSecret": {"label": "Client Secret", "placeholder": "Nhập Client Secret", "helpText": "Client Secret từ Google Cloud Console"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "helpText": "Refresh <PERSON>ken để duy trì kết nối"}, "calendarId": {"label": "Calendar ID", "placeholder": "Nhập Calendar ID (t<PERSON><PERSON> ch<PERSON>)", "helpText": "ID của calendar cụ thể, để trống để dùng calendar chính"}, "isActive": {"label": "<PERSON><PERSON><PERSON>", "helpText": "Bật/tắt tích hợp n<PERSON>y"}, "syncEnabled": {"label": "<PERSON><PERSON>ng bộ tự động", "helpText": "Tự động đồng bộ sự kiện"}, "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i"}, "list": {"title": "<PERSON><PERSON> s<PERSON>ch Google Calendar", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp Google Calendar", "createNew": "Thêm Google Calendar", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON>ếm theo tên tài k<PERSON>n...", "resultsCount": "<PERSON><PERSON><PERSON> thị {{count}} trong tổng số {{total}} kết quả"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "syncEnabled": "<PERSON><PERSON><PERSON> bộ bật", "syncDisabled": "Đồng bộ tắt", "syncing": "<PERSON><PERSON> đồng bộ"}, "actions": {"test": "<PERSON><PERSON><PERSON> tra", "sync": "<PERSON><PERSON><PERSON> bộ"}, "details": {"clientId": "Client ID", "lastSync": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "neverSynced": "<PERSON><PERSON><PERSON> đồng bộ", "created": "Tạo", "updated": "<PERSON><PERSON><PERSON>"}, "syncStatus": {"eventsCount": "Số sự kiện"}, "modal": {"editTitle": "Chỉnh sửa Google Calendar", "deleteTitle": "Xóa Google Calendar", "deleteConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteDescription": "Bạn có chắc chắn muốn xóa cấu hình {{name}}? Hành động này không thể hoàn tác."}, "empty": {"noConfigurations": "<PERSON><PERSON><PERSON> có cấu hình nào", "noConfigurationsDescription": "<PERSON><PERSON>n ch<PERSON>a thêm cấu hình <PERSON> Calendar nào", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsDescription": "<PERSON><PERSON><PERSON><PERSON> có cấu hình nào phù hợp với bộ lọc", "createFirst": "<PERSON><PERSON><PERSON><PERSON> cấu hình đầu tiên", "clearFilters": "Xóa bộ lọc"}, "error": {"loadFailed": "<PERSON><PERSON><PERSON> danh sách thất bại"}, "defaultCalendar": "Calendar chính", "validation": {"accountName": {"required": "<PERSON><PERSON><PERSON> tài k<PERSON>n là bắt buộc", "maxLength": "Tên tài khoản không đư<PERSON>c quá 100 ký tự"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "clientSecret": {"required": "Client Secret là b<PERSON><PERSON> bu<PERSON>c", "maxLength": "Client Secret không đ<PERSON><PERSON><PERSON> quá 255 ký tự"}, "refreshToken": {"required": "Refresh <PERSON> là b<PERSON> buộc"}, "testEventTitle": {"maxLength": "Tiêu đề sự kiện không được quá 200 ký tự"}, "testEventDescription": {"maxLength": "<PERSON><PERSON> tả sự kiện không được quá 1000 ký tự"}, "summary": {"required": "Ti<PERSON><PERSON> đề sự kiện là bắt buộc"}}}, "enterpriseStorage": {"title": "Tích hợp Enterprise Storage", "description": "<PERSON><PERSON><PERSON> hợp với AWS S3, Azure Blob Storage", "form": {"createTitle": "Thêm Enterprise Storage", "editTitle": "Chỉnh sửa Enterprise Storage"}}, "externalAgents": {"title": "<PERSON><PERSON><PERSON>n lý External Agents", "description": "<PERSON><PERSON><PERSON> hợp với c<PERSON>c external agents thông qua MCP, REST API, WebSocket", "overview": "<PERSON><PERSON><PERSON> quan", "protocolDistribution": "<PERSON><PERSON> bố giao thức", "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "recentActivity": "<PERSON><PERSON><PERSON> động gần đây", "gettingStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "gettingStartedDescription": "<PERSON><PERSON>a có external agent <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cấu hình. <PERSON><PERSON><PERSON> bắt đầu bằng cách tạo agent đầ<PERSON> tiên của bạn.", "createDescription": "Tạo một external agent mới với cấu hình giao thức tùy chỉnh", "manageDescription": "<PERSON><PERSON><PERSON><PERSON> lý tất cả external agents hiện có và cài đặt của chúng", "protocolsDescription": "<PERSON>em và cấu hình các giao thức được hỗ trợ", "analyticsDescription": "<PERSON>em phân tích hiệu suất và thống kê sử dụng", "noRecentActivity": "<PERSON><PERSON><PERSON> có hoạt động gần đây", "activityWillAppear": "<PERSON><PERSON><PERSON> động của external agents sẽ xuất hiện ở đây", "step1": "<PERSON><PERSON><PERSON> giao thức tích hợ<PERSON> (MCP, Google Agent, REST API, v.v.)", "step2": "<PERSON><PERSON><PERSON> hình endpoint và thông tin xác thực", "step3": "<PERSON><PERSON><PERSON> tra kết nối và bắt đầu sử dụng"}, "cloudStorage": {"title": "<PERSON><PERSON><PERSON> hợp Cloud Storage", "description": "<PERSON><PERSON><PERSON><PERSON> lý tích hợp vớ<PERSON> các dịch vụ lưu trữ đám mây", "providers": {"google-drive": "Google Drive", "onedrive": "Microsoft OneDrive", "dropbox": "Dropbox", "box": "Box"}, "form": {"createTitle": "Thêm Cloud Storage", "editTitle": "Chỉnh sửa Cloud Storage", "providerType": {"label": "<PERSON><PERSON><PERSON> cung cấp", "placeholder": "<PERSON><PERSON><PERSON> nhà cung cấp", "helpText": "Chọn dịch vụ cloud storage muốn tích hợp"}, "providerName": {"label": "<PERSON><PERSON><PERSON> c<PERSON>u hình", "placeholder": "<PERSON><PERSON><PERSON><PERSON> tên cấu hình", "helpText": "<PERSON>ên để nhận diện cấu hình này"}, "clientId": {"label": "Client ID", "placeholder": "Nhập Client ID", "helpText": "Client ID từ console của nhà cung cấp"}, "clientSecret": {"label": "Client Secret", "placeholder": "Nhập Client Secret", "helpText": "Client Secret từ console của nhà cung cấp"}, "refreshToken": {"label": "Refresh <PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON>", "helpText": "Token để duy trì kết nối"}, "rootFolderId": {"label": "Root Folder ID", "placeholder": "Nhập Root Folder ID (t<PERSON><PERSON> ch<PERSON>)", "helpText": "<PERSON> thư mụ<PERSON>, để trống để dùng thư mục ch<PERSON>h"}, "isActive": {"label": "<PERSON><PERSON><PERSON>", "helpText": "Bật/tắt tích hợp n<PERSON>y"}, "autoSync": {"label": "<PERSON><PERSON>ng bộ tự động", "helpText": "Tự động đồng bộ file"}, "syncFolders": {"label": "<PERSON><PERSON><PERSON> m<PERSON> đồng bộ", "placeholder": "<PERSON><PERSON><PERSON><PERSON> danh s<PERSON>ch thư mụ<PERSON> (JSON)", "helpText": "<PERSON><PERSON> s<PERSON>ch thư mục cần đồng bộ (định dạng JSON array)"}, "testConnection": "<PERSON><PERSON><PERSON> tra kết n<PERSON>i", "connectWithOAuth": "<PERSON><PERSON><PERSON> n<PERSON>i với {{provider}}", "authSuccess": "<PERSON><PERSON><PERSON> thực thành công", "authFailed": "<PERSON><PERSON><PERSON> thực thất bại"}, "list": {"title": "Danh sách Cloud Storage", "description": "<PERSON><PERSON><PERSON><PERSON> lý các tích hợp cloud storage", "createNew": "Thêm Cloud Storage", "searchPlaceholder": "T<PERSON>m kiếm theo tên...", "resultsCount": "<PERSON><PERSON><PERSON> thị {{count}} trong tổng số {{total}} kết quả"}, "filters": {"all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "byProvider": "<PERSON> cung cấp"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "syncEnabled": "<PERSON><PERSON><PERSON> bộ bật", "syncDisabled": "Đồng bộ tắt", "syncing": "<PERSON><PERSON> đồng bộ", "error": "Lỗi"}, "actions": {"test": "<PERSON><PERSON><PERSON> tra", "sync": "<PERSON><PERSON><PERSON> bộ", "browse": "<PERSON><PERSON><PERSON><PERSON> file", "upload": "<PERSON><PERSON><PERSON>", "download": "<PERSON><PERSON><PERSON>", "share": "<PERSON><PERSON> sẻ", "delete": "Xóa"}, "details": {"provider": "<PERSON><PERSON><PERSON> cung cấp", "clientId": "Client ID", "lastSync": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "neverSynced": "<PERSON><PERSON><PERSON> đồng bộ", "created": "Tạo", "updated": "<PERSON><PERSON><PERSON>", "storageQuota": "<PERSON><PERSON> l<PERSON>", "usedSpace": "Đã sử dụng", "availableSpace": "<PERSON>òn lại"}, "modal": {"editTitle": "Chỉnh sửa Cloud Storage", "deleteTitle": "Xóa Cloud Storage", "deleteConfirm": "<PERSON><PERSON><PERSON>n x<PERSON>a", "deleteDescription": "Bạn có chắc chắn muốn xóa cấu hình {{name}}? Hành động này không thể hoàn tác."}, "empty": {"noConfigurations": "<PERSON><PERSON><PERSON> có cấu hình nào", "noConfigurationsDescription": "Bạn chưa thêm cấu hình cloud storage nào", "noResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "noResultsDescription": "<PERSON><PERSON><PERSON><PERSON> có cấu hình nào phù hợp với bộ lọc", "createFirst": "<PERSON><PERSON><PERSON><PERSON> cấu hình đầu tiên", "clearFilters": "Xóa bộ lọc"}, "error": {"loadFailed": "<PERSON><PERSON><PERSON> danh sách thất bại", "createFailed": "<PERSON><PERSON><PERSON> c<PERSON>u hình thất bại", "updateFailed": "<PERSON><PERSON><PERSON> nhật cấu hình thất bại", "deleteFailed": "<PERSON><PERSON><PERSON> cấu hình thất bại", "testFailed": "<PERSON><PERSON><PERSON> tra kết nối thất bại", "syncFailed": "<PERSON>ồng bộ thất bại", "authFailed": "<PERSON><PERSON><PERSON> thực thất bại", "uploadFailed": "<PERSON><PERSON><PERSON> lên thất bại", "downloadFailed": "<PERSON><PERSON><PERSON> xuống thất bại"}, "success": {"created": "<PERSON><PERSON><PERSON> c<PERSON>u hình thành công", "updated": "<PERSON><PERSON><PERSON> nh<PERSON>t cấu hình thành công", "deleted": "<PERSON><PERSON><PERSON> cấu hình thành công", "testPassed": "<PERSON><PERSON><PERSON> tra kết nối thành công", "syncCompleted": "Đồng bộ hoàn tất", "authCompleted": "<PERSON><PERSON><PERSON> thực thành công", "uploaded": "<PERSON><PERSON><PERSON> lên thành công", "downloaded": "<PERSON><PERSON><PERSON> xu<PERSON>ng thành công"}, "validation": {"providerType": {"invalid": "<PERSON><PERSON><PERSON> cung cấp không hợp lệ"}, "providerName": {"required": "<PERSON><PERSON><PERSON> c<PERSON>u hình là bắt buộc", "maxLength": "<PERSON>ên cấu hình không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "clientId": {"required": "Client ID là b<PERSON> buộc", "maxLength": "Client ID khô<PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "clientSecret": {"required": "Client Secret là b<PERSON><PERSON> bu<PERSON>c", "maxLength": "Client Secret không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 255 ký tự"}, "refreshToken": {"required": "Refresh <PERSON> là b<PERSON> buộc"}, "syncFolders": {"invalidJson": "<PERSON><PERSON> s<PERSON>ch thư mục phải là JSON array hợp lệ"}, "testFolderName": {"maxLength": "<PERSON><PERSON><PERSON> thư mục test không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "testFileName": {"maxLength": "Tên file test không đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá 100 ký tự"}, "fileName": {"required": "Tên file là bắt buộc"}, "folderName": {"required": "<PERSON><PERSON><PERSON> thư mục là bắt buộc"}, "query": {"required": "Từ khóa tìm kiếm là bắt buộc"}, "operation": {"invalid": "<PERSON><PERSON> t<PERSON>c kh<PERSON>ng h<PERSON>p lệ"}, "fileIds": {"required": "<PERSON><PERSON> s<PERSON>ch file là bắt buộc"}, "fileId": {"required": "ID file là b<PERSON><PERSON> buộc"}, "permission": {"invalid": "<PERSON><PERSON><PERSON><PERSON> truy cập không hợp lệ"}}}}}