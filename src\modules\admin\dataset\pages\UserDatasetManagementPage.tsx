import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Trang tổng quan quản lý User Dataset
 */
const UserDatasetManagementPage: React.FC = () => {
  const { t } = useTranslation('admin-dataset');

  return (
    <PageWrapper>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Dataset Fine-tune Card */}
        <ModuleCard
          title={t('dataFineTune.title', 'Dataset Fine-tune')}
          description={t(
            'dataFineTune.description',
            'Quản lý dataset để huấn luyện và fine-tune các model AI.'
          )}
          icon="database"
          linkTo="/admin/dataset/data-fine-tune"
        />

        {/* Trash Card */}
        <ModuleCard
          title={t('trash.title', 'Thùng rác')}
          description={t(
            'trash.description',
            'Quản lý các dataset đã xóa mềm, bao gồm khôi phục và xóa vĩnh viễn dataset.'
          )}
          icon="trash"
          linkTo="/admin/dataset/data-fine-tune/trash"
        />
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default UserDatasetManagementPage;
