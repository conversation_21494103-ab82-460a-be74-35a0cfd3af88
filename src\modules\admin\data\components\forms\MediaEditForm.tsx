import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Card,
  IconCard,
  Typography,
  Chip,
} from '@/shared/components/common';
import { z } from 'zod';
import { FormRef } from '@/shared/components/common/Form/Form';
import { AdminMediaDto } from '@/modules/admin/data/media/types/media.types';
import { UpdateMediaDto } from '@/modules/data/media/types/media.types';
import { useUpdateAdminMedia } from '@/modules/admin/data/media/hooks/useMedia';
import { NotificationUtil } from '@/shared/utils/notification';

// Schema cho form sửa media
const updateMediaSchema = z.object({
  name: z.string().min(1, 'Tên media là bắt buộc'),
  description: z.string().optional(),
  tags: z.string().optional(),
});

type UpdateMediaFormValues = z.infer<typeof updateMediaSchema>;

interface MediaEditFormProps {
  media: AdminMediaDto;
  onSuccess?: () => void;
  onCancel: () => void;
}

/**
 * Form chỉnh sửa media cho admin theo quy tắc RedAI
 */
const MediaEditForm: React.FC<MediaEditFormProps> = ({ media, onSuccess, onCancel }) => {
  const { t } = useTranslation(['admin', 'common']);
  const formRef = useRef<FormRef<UpdateMediaFormValues>>(null);
  const updateMediaMutation = useUpdateAdminMedia(media.id);

  // State cho form data
  const [formData, setFormData] = useState<UpdateMediaFormValues>({
    name: media.name || '',
    description: media.description || '',
    tags: media.tags?.join(', ') || '',
  });

  // Cập nhật form data khi media thay đổi
  useEffect(() => {
    setFormData({
      name: media.name || '',
      description: media.description || '',
      tags: media.tags?.join(', ') || '',
    });
  }, [media]);

  // Xử lý submit form
  const handleSubmit = async (values: UpdateMediaFormValues) => {
    try {
      const updateData: UpdateMediaDto = {
        name: values.name,
        ...(values.description && { description: values.description }),
        ...(values.tags && { tags: values.tags.split(',').map(tag => tag.trim()).filter(tag => tag) }),
      };

      await updateMediaMutation.mutateAsync(updateData);

      NotificationUtil.success({
        message: t('admin:data.media.updateSuccess', 'Cập nhật media thành công'),
        duration: 3000,
      });

      onSuccess?.();
    } catch (error) {
      console.error('Lỗi khi cập nhật media:', error);
      NotificationUtil.error({
        message: t('admin:data.media.updateError', 'Có lỗi xảy ra khi cập nhật media'),
        duration: 5000,
      });
    }
  };

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof UpdateMediaFormValues, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <Card>
      <Typography variant="h5">{t('admin:data.media.editMedia', 'Chỉnh sửa media')}</Typography>

      {/* Media Preview */}
      {media.viewUrl && (
        <div className="mb-4">
          <img
            src={media.viewUrl}
            alt={media.name}
            className="max-w-full h-auto max-h-64 object-contain rounded-lg"
          />
        </div>
      )}

      {/* Thông tin bổ sung cho admin */}
      <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <Typography variant="body2" className="text-gray-500">
              {t('admin:data.media.author', 'Người tạo')}
            </Typography>
            <Typography variant="body2">{media.author}</Typography>
          </div>
          <div>
            <Typography variant="body2" className="text-gray-500">
              {t('admin:data.media.storageKey', 'Khóa lưu trữ')}
            </Typography>
            <Typography variant="body2" className="break-all">
              <a
                href={media.storageKey}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 underline"
              >
                {media.storageKey}
              </a>
            </Typography>
          </div>
        </div>
      </div>

      <Form
        ref={formRef as unknown as React.RefObject<FormRef<Record<string, unknown>>>}
        schema={updateMediaSchema}
        onSubmit={handleSubmit as unknown as (data: Record<string, unknown>) => void}
        className="space-y-4 w-full"
      >
        <FormItem
          name="name"
          label={t('admin:data.media.form.name', 'Tên media')}
          required
        >
          <Input
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder={t('admin:data.media.form.namePlaceholder', 'Nhập tên media')}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="description"
          label={t('admin:data.media.form.description', 'Mô tả')}
        >
          <Textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder={t('admin:data.media.form.descriptionPlaceholder', 'Nhập mô tả cho media')}
            rows={3}
            fullWidth
          />
        </FormItem>

        <FormItem
          name="tags"
          label={t('admin:data.media.form.tags', 'Thẻ')}
        >
          <Input
            value={formData.tags}
            onChange={(e) => handleInputChange('tags', e.target.value)}
            placeholder={t('admin:data.media.form.tagsPlaceholder', 'Nhập các thẻ, phân cách bằng dấu phẩy')}
            fullWidth
          />
        </FormItem>

        {/* Hiển thị tags hiện tại */}
        {media.tags && media.tags.length > 0 && (
          <div>
            <Typography variant="body2" className="text-gray-500 mb-2">
              {t('admin:data.media.form.currentTags', 'Thẻ hiện tại')}
            </Typography>
            <div className="flex flex-wrap gap-2">
              {media.tags.map((tag, index) => (
                <Chip key={index} size="sm" variant="default">
                  {tag}
                </Chip>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4 border-t border-gray-200 dark:border-gray-700">
          <IconCard
            icon="close"
            variant="secondary"
            size="md"
            title={t('common:cancel', 'Hủy')}
            onClick={onCancel}
          />
          <IconCard
            icon="check"
            variant="primary"
            size="md"
            title={t('common:save', 'Lưu')}
            onClick={() => formRef.current?.submit()}
            isLoading={updateMediaMutation.isPending}
          />
        </div>
      </Form>
    </Card>
  );
};

export default MediaEditForm;
