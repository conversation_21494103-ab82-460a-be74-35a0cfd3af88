import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Trang tổng quan quản lý Tools của người dùng
 */
const ToolManagementPage: React.FC = () => {
  const { t } = useTranslation(['tools']);

  return (
    <PageWrapper>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Tools Card */}
        <ModuleCard
          title={t('tools:tools', 'Công cụ')}
          description={t(
            'tools:toolsDescription',
            'Quản lý các công cụ của bạn, bao gồm xem, sao chép và tùy chỉnh công cụ.'
          )}
          icon="settings"
          linkTo="/tools/list"
        />

        <ModuleCard
          title={t('tools:integration.title', 'Tích hợp')}
          description={t(
            'tools:integration.description',
            'Tích hợp các công cụ của bạn với các dịch vụ bên ngoài.'
          )}
          icon="link"
          linkTo="/tools/integrations"
        />
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default ToolManagementPage;
