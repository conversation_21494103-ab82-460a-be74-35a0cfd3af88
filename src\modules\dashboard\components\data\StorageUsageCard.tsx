import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography } from '@/shared/components/common';

interface StorageUsageCardProps {
  usedFormatted: string; // "2.4 GB"
  totalFormatted: string; // "10 GB"
  percentage: number; // percentage used
  className?: string;
}

const StorageUsageCard: React.FC<StorageUsageCardProps> = ({
  usedFormatted,
  totalFormatted,
  percentage,
  className = '',
}) => {
  const { t } = useTranslation(['data', 'common']);

  // X<PERSON>c định màu sắc dựa trên phần trăm sử dụng
  const getUsageColor = () => {
    if (percentage >= 90) return 'text-red-500';
    if (percentage >= 75) return 'text-orange-500';
    if (percentage >= 50) return 'text-yellow-500';
    return 'text-green-500';
  };

  const getProgressColor = () => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  return (
    <Card className={`${className}`}>
      <div className="space-y-4">
        {/* Header */}
        <div className="flex justify-between items-center">
          <Typography variant="h6" className="font-medium">
            {t('data:storage.currentUsage', 'Dung lượng hiện tại')}
          </Typography>
          <Typography variant="body2" className={`font-semibold ${getUsageColor()}`}>
            {percentage.toFixed(1)}%
          </Typography>
        </div>

        {/* Usage Details */}
        <div className="flex justify-between items-center text-sm text-muted-foreground">
          <span>Đã sử dụng: {usedFormatted}</span>
          <span>Tổng cộng: {totalFormatted}</span>
        </div>

        {/* Progress Bar */}
        <div className="relative">
          <div className="w-full h-3 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div
              className={`h-full ${getProgressColor()} transition-all duration-500 ease-out rounded-full relative`}
              style={{ width: `${Math.min(percentage, 100)}%` }}
            >
              {/* Shine effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse" />
            </div>
          </div>

          {/* Usage indicator */}
          <div
            className="absolute top-0 h-3 w-1 bg-white border border-gray-400 rounded-sm transform -translate-x-1/2"
            style={{ left: `${Math.min(percentage, 100)}%` }}
          />
        </div>

        {/* Usage Warning */}
        {percentage >= 80 && (
          <div className={`p-3 rounded-lg ${
            percentage >= 90 
              ? 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800' 
              : 'bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800'
          }`}>
            <Typography variant="body2" className={`${
              percentage >= 90 ? 'text-red-700 dark:text-red-300' : 'text-orange-700 dark:text-orange-300'
            }`}>
              {percentage >= 90 
                ? t('data:storage.warningCritical', 'Dung lượng sắp hết! Hãy nâng cấp ngay để tránh gián đoạn dịch vụ.')
                : t('data:storage.warningHigh', 'Dung lượng đang cao. Cân nhắc nâng cấp để đảm bảo hiệu suất tốt nhất.')
              }
            </Typography>
          </div>
        )}
      </div>
    </Card>
  );
};

export default StorageUsageCard;
