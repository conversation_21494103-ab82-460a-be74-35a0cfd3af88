/**
 * Email Template Adapter Service
 * Bridges the gap between backend template-email API and frontend email template expectations
 */

import { TemplateEmailBusinessService } from './template-email-business.service';
import { TemplateEmail, TemplateEmailQueryParams, TemplateEmailOverviewResponseDto, UpdateTemplateEmailRequest } from '../types/template-email.types';
import { EmailTemplateDto, EmailTemplateQueryDto, EmailTemplateStatus, EmailTemplateType } from '../types/email.types';
import { PaginatedResult } from '@/shared/dto/response/api-response.dto';

/**
 * Adapter service to convert between backend template-email and frontend email-template formats
 */
export class EmailTemplateAdapterService {
  /**
   * Convert backend TemplateEmail to frontend EmailTemplateDto
   */
  static convertToEmailTemplateDto(backendTemplate: TemplateEmail): EmailTemplateDto {
    // Handle placeholders - backend can send either array or object
    let placeholderNames: string[] = [];
    if (Array.isArray(backendTemplate.placeholders)) {
      placeholderNames = backendTemplate.placeholders;
    } else if (backendTemplate.placeholders && typeof backendTemplate.placeholders === 'object') {
      placeholderNames = Object.keys(backendTemplate.placeholders);
    }

    // Map status from backend to frontend
    let status = EmailTemplateStatus.ACTIVE;
    if (backendTemplate.status) {
      switch (backendTemplate.status.toUpperCase()) {
        case 'DRAFT':
          status = EmailTemplateStatus.DRAFT;
          break;
        case 'ARCHIVED':
          status = EmailTemplateStatus.ARCHIVED;
          break;
        default:
          status = EmailTemplateStatus.ACTIVE;
      }
    }

    // Build result object conditionally to avoid undefined values
    const result: EmailTemplateDto = {
      id: backendTemplate.id.toString(),
      name: backendTemplate.name,
      subject: backendTemplate.subject,
      htmlContent: backendTemplate.content,
      type: EmailTemplateType.NEWSLETTER, // Default type since backend doesn't have this
      status,
      tags: backendTemplate.tags || [],
      variables: placeholderNames.map(placeholder => ({
        name: placeholder,
        type: 'TEXT' as const,
        defaultValue: '',
        required: false,
        description: `Variable: ${placeholder}`,
      })),
      createdAt: new Date(Number(backendTemplate.createdAt)),
      updatedAt: new Date(Number(backendTemplate.updatedAt)),
    };

    return result;
  }

  /**
   * Convert frontend EmailTemplateQueryDto to backend TemplateEmailQueryParams
   */
  static convertToBackendQueryParams(frontendQuery: EmailTemplateQueryDto): TemplateEmailQueryParams {
    // Build query params conditionally to avoid undefined values
    const result: TemplateEmailQueryParams = {};

    // Only include properties if they have values
    if (frontendQuery.search !== undefined) {
      result.name = frontendQuery.search; // Backend uses 'name' for search
    }

    if (frontendQuery.page !== undefined) {
      result.page = frontendQuery.page;
    }

    if (frontendQuery.limit !== undefined) {
      result.limit = frontendQuery.limit;
    }

    if (frontendQuery.sortBy !== undefined) {
      result.sortBy = frontendQuery.sortBy;
    }

    if (frontendQuery.sortDirection !== undefined) {
      result.sortDirection = frontendQuery.sortDirection;
    }

    return result;
  }

  /**
   * Get email templates using backend API and convert to frontend format
   */
  static async getEmailTemplates(query?: EmailTemplateQueryDto): Promise<PaginatedResult<EmailTemplateDto>> {
    const backendQuery = query ? this.convertToBackendQueryParams(query) : {};

    const backendResult = await TemplateEmailBusinessService.getTemplateEmails(backendQuery);

    const convertedItems = backendResult.items.map(item => {
      const converted = this.convertToEmailTemplateDto(item);
      return converted;
    });

    const result = {
      items: convertedItems,
      meta: backendResult.meta,
    };

    return result;
  }

  /**
   * Get email template by ID using backend API and convert to frontend format
   */
  static async getEmailTemplate(id: string): Promise<EmailTemplateDto> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    const backendTemplate = await TemplateEmailBusinessService.getTemplateEmailById(numericId);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Create email template using backend API
   * Maps frontend data structure to backend CreateTemplateEmailDto
   */
  static async createEmailTemplate(data: {
    name: string;
    subject: string;
    htmlContent: string;
    textContent?: string;
    type?: string;
    previewText?: string;
    tags?: string[];
    variables?: Array<{ name: string; type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE'; required?: boolean; defaultValue?: string; description?: string }>;
  }): Promise<EmailTemplateDto> {
    // Map frontend data to backend CreateTemplateEmailRequest conditionally
    const backendData: {
      name: string;
      subject: string;
      content: string;
      tags: string[];
      variables: Array<{
        name: string;
        type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
        required: boolean;
        defaultValue?: string;
        description?: string;
      }>;
      textContent?: string;
      type?: string;
      previewText?: string;
    } = {
      name: data.name,
      subject: data.subject,
      content: data.htmlContent, // htmlContent -> content
      tags: data.tags || [],
      variables: data.variables?.map(v => {
        const variable: {
          name: string;
          type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE';
          required: boolean;
          defaultValue?: string;
          description?: string;
        } = {
          name: v.name,
          type: v.type,
          required: v.required || false,
        };

        // Only include optional properties if they have values
        if (v.defaultValue !== undefined) {
          variable.defaultValue = v.defaultValue;
        }

        if (v.description !== undefined) {
          variable.description = v.description;
        }

        return variable;
      }) || [],
    };

    // Only include optional properties if they have values
    if (data.textContent !== undefined) {
      backendData.textContent = data.textContent;
    }

    if (data.type !== undefined) {
      backendData.type = data.type;
    }

    if (data.previewText !== undefined) {
      backendData.previewText = data.previewText;
    }

    const backendTemplate = await TemplateEmailBusinessService.createTemplateEmail(backendData);
    return this.convertToEmailTemplateDto(backendTemplate);
  }

  /**
   * Update email template using backend API
   */
  static async updateEmailTemplate(
    id: string,
    data: {
      name?: string;
      subject?: string;
      htmlContent?: string;
      textContent?: string;
      type?: string;
      status?: string;
      previewText?: string;
      tags?: string[];
      variables?: Array<{ name: string; type: 'TEXT' | 'NUMBER' | 'DATE' | 'URL' | 'IMAGE'; required?: boolean; defaultValue?: string; description?: string }>;
    }
  ): Promise<EmailTemplateDto> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    // Build backend data conditionally to avoid undefined values
    const backendData: UpdateTemplateEmailRequest = {};

    // Only include properties if they have values
    if (data.name !== undefined) {
      backendData.name = data.name;
    }

    if (data.subject !== undefined) {
      backendData.subject = data.subject;
    }

    if (data.htmlContent !== undefined) {
      backendData.htmlContent = data.htmlContent;
    }

    if (data.textContent !== undefined) {
      backendData.textContent = data.textContent;
    }

    if (data.type !== undefined) {
      backendData.type = data.type;
    }

    if (data.previewText !== undefined) {
      backendData.previewText = data.previewText;
    }

    if (data.tags !== undefined) {
      backendData.tags = data.tags;
    }

    if (data.variables !== undefined) {
      backendData.variables = data.variables;
    }

    console.log('🔄 [EmailTemplateAdapterService] Calling business service with:', { numericId, backendData });

    const backendTemplate = await TemplateEmailBusinessService.updateTemplateEmail(numericId, backendData);

    console.log('✅ [EmailTemplateAdapterService] Business service returned:', backendTemplate);

    const result = this.convertToEmailTemplateDto(backendTemplate);

    console.log('✅ [EmailTemplateAdapterService] Final result:', result);

    return result;
  }

  /**
   * Delete email template using backend API
   */
  static async deleteEmailTemplate(id: string): Promise<boolean> {
    const numericId = parseInt(id, 10);
    if (isNaN(numericId)) {
      throw new Error('Invalid template ID');
    }

    return TemplateEmailBusinessService.deleteTemplateEmail(numericId);
  }

  /**
   * Bulk delete email templates using backend API
   */
  static async bulkDeleteEmailTemplates(ids: string[]): Promise<boolean> {
    const numericIds = ids.map(id => {
      const numericId = parseInt(id, 10);
      if (isNaN(numericId)) {
        throw new Error(`Invalid template ID: ${id}`);
      }
      return numericId;
    });

    return TemplateEmailBusinessService.bulkDeleteTemplateEmails(numericIds);
  }

  /**
   * Get email template statistics
   */
  static async getEmailTemplateStatistics(): Promise<{
    totalTemplates: number;
    activeTemplates: number;
    draftTemplates: number;
    recentTemplates: EmailTemplateDto[];
  }> {
    const backendStats = await TemplateEmailBusinessService.getTemplateStatistics();

    return {
      totalTemplates: backendStats.totalTemplates,
      activeTemplates: backendStats.activeTemplates,
      draftTemplates: backendStats.draftTemplates,
      recentTemplates: backendStats.recentTemplates.map(template =>
        this.convertToEmailTemplateDto(template)
      ),
    };
  }

  /**
   * Preview template with variables
   */
  static previewTemplate(content: string, variables: Record<string, string>): string {
    return TemplateEmailBusinessService.previewTemplate(content, variables);
  }

  /**
   * Validate template placeholders
   */
  static validateTemplatePlaceholders(content: string, placeholders: string[]): {
    isValid: boolean;
    missingPlaceholders: string[];
    unusedPlaceholders: string[];
  } {
    return TemplateEmailBusinessService.validateTemplatePlaceholders(content, placeholders);
  }

  /**
   * Get template email overview statistics
   */
  static async getOverview(): Promise<TemplateEmailOverviewResponseDto> {
    return TemplateEmailBusinessService.getOverview();
  }
}
