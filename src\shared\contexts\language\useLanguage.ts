import { useContext } from 'react';
import LanguageContext, { LanguageContextType } from './LanguageContext';

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    // Fallback cho trường hợp development/hot reload
    if (process.env['NODE_ENV'] === 'development') {
      console.warn('useLanguage called outside LanguageProvider, using fallback values');
      return {
        language: 'vi',
        setLanguage: () => {},
        availableLanguages: [
          { code: 'vi', name: 'Tiếng Việt' },
          { code: 'en', name: 'English' },
          { code: 'zh', name: '中文' }
        ]
      };
    }
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

export default useLanguage;
