import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { Card, Table, Typography, Button } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { formatTimestamp } from '@/shared/utils/date';

// Types cho email provider
interface EmailProviderConfig {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'inactive' | 'error';
  host?: string;
  port?: number;
  username?: string;
  apiKey?: string;
  createdAt: string;
  updatedAt: string;
}

// Mock data cho demo
const mockEmailConfigs: EmailProviderConfig[] = [
  {
    id: '1',
    name: 'Production SMTP',
    type: 'smtp',
    status: 'active',
    host: 'smtp.gmail.com',
    port: 587,
    username: '<EMAIL>',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
  },
  {
    id: '2',
    name: 'SendGrid Marketing',
    type: 'sendgrid',
    status: 'active',
    apiKey: 'SG.***************',
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-10T09:00:00Z',
  },
];

/**
 * Trang quản lý Email Provider cụ thể
 */
const EmailProviderPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const { provider } = useParams<{ provider: string }>();

  // State
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  // Slide form hooks
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Provider info mapping
  const providerInfo = useMemo(() => {
    const providers: Record<string, { title: string; description: string; icon: string }> = {
      smtp: {
        title: 'SMTP Configuration',
        description: 'Cấu hình máy chủ SMTP cho gửi email',
        icon: 'mail',
      },
      outlook: {
        title: 'Microsoft Outlook',
        description: 'Tích hợp với Microsoft Outlook',
        icon: 'mail',
      },
      yahoo: {
        title: 'Yahoo Mail',
        description: 'Tích hợp với Yahoo Mail',
        icon: 'mail',
      },
      sendgrid: {
        title: 'SendGrid',
        description: 'Dịch vụ email marketing SendGrid',
        icon: 'mail',
      },
      mailchimp: {
        title: 'Mailchimp Transactional',
        description: 'Email transactional qua Mailchimp',
        icon: 'mail',
      },
      'amazon-ses': {
        title: 'Amazon SES',
        description: 'Amazon Simple Email Service',
        icon: 'mail',
      },
      mailgun: {
        title: 'Mailgun',
        description: 'Dịch vụ email API Mailgun',
        icon: 'mail',
      },
      gmail: {
        title: 'Gmail',
        description: 'Tích hợp với Gmail API',
        icon: 'mail',
      },
    };
    return (
      providers[provider || 'smtp'] ||
      (providers['smtp'] as { title: string; description: string; icon: string })
    );
  }, [provider]);

  // Table columns
  const columns: TableColumn<EmailProviderConfig>[] = [
    {
      title: t('common:name'),
      dataIndex: 'name',
      key: 'name',
      sortable: true,
    },
    {
      title: t('integration:status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: unknown) => (
        <span
          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
            status === 'active'
              ? 'bg-green-100 text-green-800'
              : status === 'error'
                ? 'bg-red-100 text-red-800'
                : 'bg-gray-100 text-gray-800'
          }`}
        >
          {t(`integration:status.${status}`)}
        </span>
      ),
    },
    {
      title: t('integration:host'),
      dataIndex: 'host',
      key: 'host',
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium">
          {value as string || '-'}
        </Typography>
      ),
    },
    {
      title: t('integration:port'),
      dataIndex: 'port',
      key: 'port',
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium">
          {value as number || '-'}
        </Typography>
      ),
    },
    {
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      sortable: true,
      render: (value: unknown) => (
        <Typography variant="body2" className="font-medium">
          {formatTimestamp(value as string)}
        </Typography>
      ),
    },
  ];

  // Filter data
  const filteredData = useMemo(() => {
    return mockEmailConfigs.filter(
      config =>
        config.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        config.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [searchTerm]);

  // Handlers
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setCurrentPage(1);
  };

  const handleSortChange = () => {
    // Sort logic will be implemented when connected to real API
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
            <span className="text-2xl">{providerInfo.icon === 'mail' ? '📧' : '⚙️'}</span>
          </div>
          <div>
            <Typography variant="h4" className="font-bold">
              {providerInfo.title}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {providerInfo.description}
            </Typography>
          </div>
        </div>

        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={() => showCreateForm()}
          items={[
            {
              id: 'all',
              label: t('common:all'),
              icon: 'list',
              onClick: () => {},
            },
          ]}
          showDateFilter={false}
          showColumnFilter={false}
        />

        {/* Table */}
        <Card>
          <Table<EmailProviderConfig>
            columns={columns}
            data={filteredData}
            rowKey="id"
            loading={false}
            sortable={true}
            onSortChange={handleSortChange}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: filteredData.length,
              onChange: handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Create Form */}
        <SlideInForm isVisible={isCreateFormVisible}>
          <div className="p-6">
            <Typography variant="h5" className="mb-4">
              {t('integration:addNewProvider', { provider: providerInfo.title })}
            </Typography>
            <div className="space-y-4">
              <Typography variant="body2" className="text-muted-foreground">
                {t('integration:providerFormComingSoon')}
              </Typography>
              <Button onClick={hideCreateForm} variant="outline">
                {t('common:close')}
              </Button>
            </div>
          </div>
        </SlideInForm>
      </div>
    </div>
  );
};

export default EmailProviderPage;
