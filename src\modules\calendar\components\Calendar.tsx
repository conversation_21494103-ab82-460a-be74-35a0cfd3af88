import React, { useCallback, useMemo, useEffect, useRef, useState } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import { DateSelectArg, EventClickArg, EventChangeArg } from '@fullcalendar/core';
import { CalendarProps, CalendarEvent } from '../types';
import { useTheme } from '@/shared/contexts/theme';
import { useTranslation } from 'react-i18next';
import { addDays, addHours, startOfDay, subDays } from 'date-fns';
import viLocale from '../locales/vi.fullcalendar';
import ViewSelect from './ViewSelect';

import { Hidden, Icon, Button } from '@/shared/components/common';
/**
 * T<PERSON><PERSON> các loại sự kiện mẫu với các class CSS tương ứng
 */
const generateSampleEvents = (): CalendarEvent[] => {
  const today = new Date();
  const startOfToday = startOfDay(today);

  return [
    {
      id: '1',
      title: 'Họp nhóm',
      start: addHours(startOfToday, 10),
      end: addHours(startOfToday, 12),
      description: 'Cuộc họp nhóm hàng tuần để thảo luận về tiến độ dự án',
      location: 'Phòng họp A',
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    },
    {
      id: '2',
      title: 'Thuyết trình khách hàng',
      start: addHours(addDays(startOfToday, 1), 14),
      end: addHours(addDays(startOfToday, 1), 16),
      description: 'Thuyết trình các tính năng mới cho khách hàng',
      location: 'Phòng họp B',
      className: 'calendar-event-appointment',
      extendedProps: {
        type: 'appointment',
      },
    },
    {
      id: '3',
      title: 'Hạn chót dự án',
      start: addDays(startOfToday, 3),
      allDay: true,
      description: 'Hạn chót cuối cùng để nộp dự án',
      className: 'calendar-event-deadline',
      extendedProps: {
        type: 'deadline',
      },
    },
    {
      id: '4',
      title: 'Ăn trưa cùng nhóm',
      start: addHours(addDays(startOfToday, 2), 12),
      end: addHours(addDays(startOfToday, 2), 13),
      description: 'Ăn trưa cùng nhóm tại nhà hàng gần đó',
      location: 'Nhà hàng Ý',
      className: 'calendar-event-appointment',
      extendedProps: {
        type: 'appointment',
      },
    },
    {
      id: '5',
      title: 'Đánh giá mã nguồn',
      start: addHours(subDays(startOfToday, 1), 15),
      end: addHours(subDays(startOfToday, 1), 17),
      description: 'Buổi đánh giá mã nguồn cho tính năng mới',
      location: 'Trực tuyến',
      className: 'calendar-event-meeting',
      extendedProps: {
        type: 'meeting',
      },
    },
    {
      id: '6',
      title: 'Lập kế hoạch',
      start: addHours(startOfToday, 14),
      end: addHours(startOfToday, 16),
      description: 'Buổi lập kế hoạch cho sprint tiếp theo',
      location: 'Phòng họp C',
      className: 'calendar-event-planning',
      extendedProps: {
        type: 'planning',
      },
    },
    {
      id: '7',
      title: 'Hội thảo đào tạo',
      start: addDays(startOfToday, 5),
      end: addDays(startOfToday, 6),
      allDay: true,
      description: 'Hội thảo đào tạo hai ngày về công nghệ mới',
      location: 'Trung tâm đào tạo',
      className: 'calendar-event-workshop',
      extendedProps: {
        type: 'workshop',
      },
    },
    {
      id: '8',
      title: 'Demo sản phẩm',
      start: addHours(addDays(startOfToday, 4), 11),
      end: addHours(addDays(startOfToday, 4), 12),
      description: 'Demo các tính năng mới của sản phẩm',
      location: 'Phòng demo',
      className: 'calendar-event-appointment',
      extendedProps: {
        type: 'appointment',
      },
    },
  ];
};

const Calendar: React.FC<CalendarProps> = ({
  events = [],
  initialDate,
  initialView = 'dayGridMonth',
  weekends = true,
  editable = true,
  selectable = true,
  allDaySlot = false,
  height = 'auto',
  className = '',
  onDateSelect,
  onEventClick,
  onEventChange,
}) => {
  const { t } = useTranslation(['common', 'calendar']);
  const { themeMode } = useTheme();
  const calendarRef = useRef<FullCalendar>(null);
  const [currentView, setCurrentView] = useState<string>(initialView);

  // Use sample events if no events are provided
  const displayEvents = useMemo(() => {
    try {
      return events.length > 0 ? events : generateSampleEvents();
    } catch (error) {
      console.error('Error generating sample events:', error);
      return [];
    }
  }, [events]);

  // Handle date selection
  const handleDateSelect = useCallback(
    (selectInfo: DateSelectArg) => {
      if (onDateSelect) {
        onDateSelect(selectInfo);
      }
    },
    [onDateSelect]
  );

  // Handle event click
  const handleEventClick = useCallback(
    (clickInfo: EventClickArg) => {
      if (onEventClick) {
        onEventClick(clickInfo);
      }
    },
    [onEventClick]
  );

  // Handle event change (drag, resize)
  const handleEventChange = useCallback(
    (changeInfo: EventChangeArg) => {
      if (onEventChange) {
        onEventChange(changeInfo);
      }
    },
    [onEventChange]
  );

  // Handle view change from ViewSelect
  const handleViewChange = useCallback((view: string) => {
    setCurrentView(view);
    if (calendarRef.current) {
      const apiInstance = calendarRef.current.getApi();
      apiInstance.changeView(view);
    }
  }, []);

  // Determine calendar class based on theme
  const calendarClass = `calendar-container ${themeMode === 'dark' ? 'fc-theme-dark' : 'fc-theme-light'} ${className}`;

  // Update currentView when calendar is initialized
  useEffect(() => {
    // Set initial view after calendar is initialized
    setTimeout(() => {
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        setCurrentView(apiInstance.view.type);

        // Update title initially
        const titleEl = document.getElementById('calendar-title');
        if (titleEl) {
          titleEl.textContent = apiInstance.view.title;
        }

        // Add event listener for view changes to update title
        apiInstance.on('datesSet', () => {
          const titleEl = document.getElementById('calendar-title');
          if (titleEl) {
            titleEl.textContent = apiInstance.view.title;
          }
        });
      }
    }, 500);
  }, []);

  // Ensure CSS is loaded and calendar is initialized properly
  useEffect(() => {
    // Sử dụng một timeout dài hơn để đảm bảo CSS được tải đầy đủ
    const timer = setTimeout(() => {
      const calendarEl = document.querySelector('.calendar-container');
      if (calendarEl) {
        calendarEl.classList.add('calendar-initialized');
      }

      // Kích hoạt resize event để FullCalendar tính toán lại kích thước
      window.dispatchEvent(new Event('resize'));

      // Sử dụng API của FullCalendar để render lại calendar
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    }, 500);

    // Thêm một resize handler để đảm bảo calendar luôn có kích thước đúng
    const handleResize = () => {
      const calendarEl = document.querySelector('.fc');
      if (calendarEl) {
        // Đảm bảo calendar chiếm toàn bộ chiều rộng của container
        (calendarEl as HTMLElement).style.width = '100%';
      }

      // Cập nhật kích thước của calendar khi cửa sổ thay đổi kích thước
      if (calendarRef.current) {
        const apiInstance = calendarRef.current.getApi();
        apiInstance.updateSize();
      }
    };

    window.addEventListener('resize', handleResize);

    // Gọi resize handler ngay lập tức
    handleResize();

    // Thêm một timer bổ sung để đảm bảo calendar được render đúng kích thước
    const secondTimer = setTimeout(() => {
      handleResize();
    }, 1000);

    return () => {
      clearTimeout(timer);
      clearTimeout(secondTimer);
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Re-render calendar when theme changes
  useEffect(() => {
    // Sử dụng key để buộc FullCalendar re-render hoàn toàn khi theme thay đổi
    const calendarEl = document.querySelector('.calendar-container');
    if (calendarEl) {
      // Đảm bảo calendar vẫn hiển thị khi thay đổi theme
      calendarEl.classList.add('calendar-initialized');

      // Thêm một class tạm thời để áp dụng transition
      calendarEl.classList.add('theme-transition');

      // Xóa class transition sau khi hoàn thành
      setTimeout(() => {
        calendarEl.classList.remove('theme-transition');

        // Kích hoạt resize event để FullCalendar tính toán lại kích thước
        window.dispatchEvent(new Event('resize'));

        // Đảm bảo calendar chiếm toàn bộ chiều rộng
        const fcEl = document.querySelector('.fc');
        if (fcEl) {
          (fcEl as HTMLElement).style.width = '100%';
        }

        // Sử dụng API của FullCalendar để cập nhật kích thước
        if (calendarRef.current) {
          const apiInstance = calendarRef.current.getApi();
          apiInstance.updateSize();
        }
      }, 300);
    }
  }, [themeMode]);

  return (
    <div className={calendarClass}>
      <div className="calendar-custom-header">
        <div className="calendar-custom-header-left">
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-prev-button"
            onClick={() => calendarRef.current?.getApi().prev()}
          >
            <Icon name="chevron-left" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-nav-button calendar-next-button"
            onClick={() => calendarRef.current?.getApi().next()}
          >
            <Icon name="chevron-right" size="sm" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="calendar-today-button"
            onClick={() => calendarRef.current?.getApi().today()}
          >
            {t('calendar:today', 'Hôm nay')}
          </Button>
        </div>
        <div className="calendar-custom-header-center">
          <h2 className="calendar-title" id="calendar-title"></h2>
        </div>
        <div className="calendar-custom-header-right">
          <ViewSelect currentView={currentView} onViewChange={handleViewChange} />
          <Button
            variant="primary"
            className="calendar-add-event-button"
            leftIcon={<Icon name="plus" size="sm" />}
            onClick={() => {
              const calendarApi = calendarRef.current?.getApi();
              if (calendarApi) {
                const start = new Date();
                const end = new Date();
                end.setHours(end.getHours() + 1);

                if (onDateSelect) {
                  onDateSelect({
                    start,
                    end,
                    allDay: false,
                    view: calendarApi.view,
                    jsEvent: new MouseEvent('click'),
                  } as DateSelectArg);
                }
              }
            }}
          >
            <Hidden hideOnMobile>
              <span>{t('calendar:addEvent', 'Thêm sự kiện')}</span>
            </Hidden>
          </Button>
        </div>
      </div>
      <FullCalendar
        ref={calendarRef}
        key={`calendar-${themeMode}`} // Thêm key để buộc re-render khi theme thay đổi
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
        initialView={initialView}
        {...(initialDate && { initialDate })}
        locale={viLocale}
        headerToolbar={false}
        buttonText={{
          today: t('calendar:today', 'Hôm nay'),
        }}
        events={displayEvents.map(event => {
          // Build event object conditionally to avoid undefined values
          const eventInput: {
            id: string;
            title: string;
            start: string | Date;
            end?: string | Date;
            allDay?: boolean;
            backgroundColor?: string;
            borderColor?: string;
            textColor?: string;
            classNames?: string[];
          } = {
            id: event.id,
            title: event.title,
            start: event.start,
          };

          // Only include optional properties if they have values
          if (event.end !== undefined) {
            eventInput.end = event.end;
          }

          if (event.allDay !== undefined) {
            eventInput.allDay = event.allDay;
          }

          if (event.backgroundColor !== undefined) {
            eventInput.backgroundColor = event.backgroundColor;
          }

          if (event.borderColor !== undefined) {
            eventInput.borderColor = event.borderColor;
          }

          if (event.textColor !== undefined) {
            eventInput.textColor = event.textColor;
          }

          if (event.classNames !== undefined) {
            eventInput.classNames = event.classNames;
          }

          return eventInput;
        })}
        editable={editable}
        selectable={selectable}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={weekends}
        allDaySlot={allDaySlot}
        height={height}
        select={handleDateSelect}
        eventClick={handleEventClick}
        eventChange={handleEventChange}
        eventTimeFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        slotLabelFormat={{
          hour: '2-digit',
          minute: '2-digit',
          hour12: false,
        }}
        eventDidMount={info => {
          // Ensure event classes are applied correctly
          if (info.event.extendedProps?.['type']) {
            const type = info.event.extendedProps['type'];
            info.el.classList.add(`calendar-event-${type}`);
          }
        }}
        themeSystem="standard"
        firstDay={1} // Bắt đầu từ thứ 2
        fixedWeekCount={false} // Số tuần hiển thị linh hoạt theo tháng
        showNonCurrentDates={false} // Ẩn các ngày không thuộc tháng hiện tại
      />
    </div>
  );
};

export default Calendar;
