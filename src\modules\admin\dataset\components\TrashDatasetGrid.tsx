import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ResponsiveGrid } from '@/shared/components/common';
import TrashDatasetCard from './TrashDatasetCard';
import { UserDataFineTuneResponseDto } from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface TrashDatasetGridProps {
  /**
   * Danh sách datasets đã xóa
   */
  datasets: UserDataFineTuneResponseDto[];

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Callback khi khôi phục dataset
   */
  onRestore?: (dataset: UserDataFineTuneResponseDto) => void;

  /**
   * CSS class tùy chỉnh
   */
  className?: string;
}

/**
 * Component hiển thị grid các dataset đã xóa mềm
 */
const TrashDatasetGrid: React.FC<TrashDatasetGridProps> = ({
  datasets,
  loading = false,
  onRestore,
  className = '',
}) => {
  const { t } = useTranslation(['admin-dataset', 'common']);

  // Hiển thị loading state
  if (loading) {
    return (
      <div className={`flex justify-center items-center py-12 ${className}`}>
        <div className="text-gray-500">{t('common:loading', 'Đang tải...')}</div>
      </div>
    );
  }

  // Hiển thị empty state khi không có dữ liệu
  if (!datasets || datasets.length === 0) {
    return (
      <Card className={`p-12 text-center ${className}`}>
        <div className="text-gray-500">
          {t('admin-dataset:trash.noData', 'Không có dataset nào đã xóa')}
        </div>
      </Card>
    );
  }

  // Hiển thị grid datasets
  return (
    <div className={className}>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {datasets.map(dataset => (
          <div key={dataset.id} className="h-full">
            <TrashDatasetCard dataset={dataset} onRestore={onRestore!} />
          </div>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default TrashDatasetGrid;
