import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Form,
  FormItem,
  Input,
  Toggle,
  Icon,
  Typography,
} from '@/shared/components/common';
import { FormStatus } from '../types/profile.types';
import { useProfileNotification } from '../contexts/ProfileNotificationContext';
import { PROFILE_CARD_IDS } from '../constants/profile-cards';
import ProfileCard from './ProfileCard';

/**
 * Component form bảo mật - Simplified version
 */
const SecurityForm: React.FC = () => {
  const { t } = useTranslation(['profile', 'validation']);
  const [formStatus, setFormStatus] = useState<FormStatus>(FormStatus.IDLE);
  const [showPassword, setShowPassword] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const { showNotification } = useProfileNotification();

  // Dữ liệu mẫu cho 2FA settings
  const mockTwoFactorAuth = {
    otpSmsEnabled: false,
    otpEmailEnabled: true,
    googleAuthenticatorEnabled: false,
  };

  // X<PERSON> lý khi submit form - chỉ hiển thị thông báo thành công
  const handleSubmit = (data: {
    otpSmsEnabled: boolean;
    otpEmailEnabled: boolean;
    googleAuthenticatorEnabled: boolean;
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }) => {
    setFormStatus(FormStatus.SUBMITTING);

    // Giả lập thời gian xử lý
    setTimeout(() => {
      setFormStatus(FormStatus.IDLE);
      showNotification(
        'success',
        t('profile:messages.passwordChangeSuccess', 'Cập nhật bảo mật thành công')
      );
      console.log('Security form data:', data);
    }, 1000);
  };

  // Toggle hiển thị mật khẩu
  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPassword(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  // Xử lý toggle 2FA - chỉ hiển thị thông báo
  const handleToggle2FA = (type: string, enabled: boolean) => {
    showNotification(
      'success',
      t('profile:messages.updateSuccess', `Cập nhật ${type} thành công`)
    );
    console.log(`${type} toggled to:`, enabled);
  };

  const cardTitle = (
    <div className="flex items-center">
      <Icon name="lock" className="mr-2 text-primary" />
      <Typography variant="subtitle1" weight="semibold" color="dark">
        {t('profile:security.title', 'Bảo mật')}
      </Typography>
    </div>
  );

  const isSubmitting = formStatus === FormStatus.SUBMITTING;

  return (
    <ProfileCard cardId={PROFILE_CARD_IDS.SECURITY} title={cardTitle}>
      <Form
        onSubmit={handleSubmit as unknown as (data: Record<string, unknown>) => void}
        defaultValues={{
          otpSmsEnabled: mockTwoFactorAuth.otpSmsEnabled,
          otpEmailEnabled: mockTwoFactorAuth.otpEmailEnabled,
          googleAuthenticatorEnabled: mockTwoFactorAuth.googleAuthenticatorEnabled,
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        }}
      >
        <div className="space-y-6">
          {/* SMS Authentication */}
          <FormItem name="otpSmsEnabled" label={t('profile:security.smsAuth', 'Xác thực SMS')}>
            <Toggle
              checked={mockTwoFactorAuth.otpSmsEnabled}
              onChange={checked => handleToggle2FA('SMS', checked)}
            />
          </FormItem>

          {/* Email Authentication */}
          <FormItem name="otpEmailEnabled" label={t('profile:security.emailAuth', 'Xác thực Email')}>
            <Toggle
              checked={mockTwoFactorAuth.otpEmailEnabled}
              onChange={checked => handleToggle2FA('Email', checked)}
            />
          </FormItem>

          {/* Google Authenticator */}
          <FormItem name="googleAuthenticatorEnabled" label={t('profile:security.googleAuth', 'Google Authenticator')}>
            <Toggle
              checked={mockTwoFactorAuth.googleAuthenticatorEnabled}
              onChange={checked => handleToggle2FA('Google Authenticator', checked)}
            />
          </FormItem>

          <div className="mt-6 mb-4">
            <Typography variant="subtitle1" weight="semibold" color="dark">
              {t('profile:security.passwordSection', 'Đổi mật khẩu')}
            </Typography>
          </div>

          {/* Mật khẩu hiện tại */}
          <FormItem name="currentPassword" label={t('profile:security.currentPassword', 'Mật khẩu hiện tại')}>
            <Input
              type={showPassword.current ? 'text' : 'password'}
              placeholder={t('profile:security.currentPassword', 'Mật khẩu hiện tại')}
              fullWidth
              rightIcon={
                <div
                  className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={() => togglePasswordVisibility('current')}
                >
                  <Icon name={showPassword.current ? 'eye-off' : 'eye'} size="sm" />
                </div>
              }
            />
          </FormItem>

          {/* Mật khẩu mới */}
          <FormItem name="newPassword" label={t('profile:security.newPassword', 'Mật khẩu mới')}>
            <Input
              type={showPassword.new ? 'text' : 'password'}
              placeholder={t('profile:security.newPassword', 'Mật khẩu mới')}
              fullWidth
              rightIcon={
                <div
                  className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={() => togglePasswordVisibility('new')}
                >
                  <Icon name={showPassword.new ? 'eye-off' : 'eye'} size="sm" />
                </div>
              }
            />
          </FormItem>

          {/* Xác nhận mật khẩu mới */}
          <FormItem name="confirmPassword" label={t('profile:security.confirmPassword', 'Xác nhận mật khẩu mới')}>
            <Input
              type={showPassword.confirm ? 'text' : 'password'}
              placeholder={t('profile:security.confirmPassword', 'Xác nhận mật khẩu mới')}
              fullWidth
              rightIcon={
                <div
                  className="cursor-pointer text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                  onClick={() => togglePasswordVisibility('confirm')}
                >
                  <Icon name={showPassword.confirm ? 'eye-off' : 'eye'} size="sm" />
                </div>
              }
            />
          </FormItem>

          {/* Buttons */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              variant="outline"
              type="button"
              disabled={isSubmitting}
            >
              {t('profile:buttons.cancel', 'Hủy')}
            </Button>
            <Button variant="primary" type="submit" isLoading={isSubmitting}>
              {t('profile:buttons.save', 'Lưu')}
            </Button>
          </div>
        </div>
      </Form>
    </ProfileCard>
  );
};

export default SecurityForm;
