import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Typography, IconCard, Loading, Icon, Avatar, CollapsibleCard, Input, GenericCustomFieldSelector } from '@/shared/components/common';
import CustomFieldRenderer from '@/modules/business/components/CustomFieldRenderer';
import { useAudience, useBulkUpdateAudienceCustomFields } from '../../hooks/useAudienceQuery';
import { useMarketingCustomFields } from '../../hooks/useMarketingCustomFieldQuery';
import { MarketingCustomFieldBusinessService } from '../../services/marketing-custom-field.service';
import { MarketingCustomFieldResponse } from '../../types/custom-field.types';
import { BulkUpdateCustomFieldsDto, CreateCustomFieldDto } from '../../types/audience.types';

// Interface cho trường tùy chỉnh đã chọn với giá trị - mở rộng từ MarketingCustomFieldResponse
interface SelectedCustomField extends MarketingCustomFieldResponse {
  value: Record<string, unknown>; // Giá trị người dùng nhập
}

interface AudienceDetailViewProps {
  /**
   * ID audience
   */
  audienceId: number;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi chỉnh sửa
   */
  onEdit?: () => void;
}

/**
 * Component hiển thị chi tiết audience
 */
const AudienceDetailView: React.FC<AudienceDetailViewProps> = ({ audienceId, onClose, onEdit }) => {
  const { t } = useTranslation(['marketing']);

  // Sử dụng hook useAudience để lấy dữ liệu audience
  const { data: audience, isLoading, error } = useAudience(audienceId);

  // Hook để bulk update custom fields
  const bulkUpdateCustomFields = useBulkUpdateAudienceCustomFields(audienceId);

  // State cho custom fields
  const [audienceCustomFields, setAudienceCustomFields] = useState<SelectedCustomField[]>([]);

  // Fetch marketing custom fields
  const { data: customFieldsData } = useMarketingCustomFields({});
  const customFields = useMemo(() => customFieldsData?.items || [], [customFieldsData]);

  // Create search function for GenericCustomFieldSelector - GỌI API THỰC SỰ
  const searchFunction = useCallback(async (params: { search?: string; page?: number; limit?: number }) => {
    console.log('🔍 AudienceDetailView searchFunction called with params:', params);

    try {
      const apiParams = {
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 20,
        sortBy: 'id',
        sortDirection: 'ASC' as const,
      };

      console.log('📡 Calling MarketingCustomFieldBusinessService with:', apiParams);
      const response = await MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(apiParams);
      console.log('✅ API response:', response);

      // Transform data for GenericCustomFieldSelector
      const items = response.result?.items || [];
      const meta = response.result?.meta || {};

      const transformedResult = {
        items: items.map((item: { id: number; displayName: string; dataType: string }) => ({
          id: item.id,
          label: item.displayName,
          dataType: item.dataType,
          type: item.dataType,
        })),
        totalItems: meta.totalItems || 0,
        totalPages: meta.totalPages || 0,
        currentPage: meta.currentPage || 1,
        hasNextPage: (meta.currentPage || 1) < (meta.totalPages || 1),
      };

      console.log('🔄 Transformed result:', transformedResult);
      return transformedResult;
    } catch (error) {
      console.error('❌ Error in AudienceDetailView search:', error);
      throw error;
    }
  }, []);

  // State cho avatar
  const [avatarUrl, setAvatarUrl] = useState<string>(`https://i.pravatar.cc/150?img=${audienceId}`);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State cho editing mode
  const [isEditingGeneral, setIsEditingGeneral] = useState(false);
  const [isSavingGeneral, setIsSavingGeneral] = useState(false);
  const [isEditingCustomFields, setIsEditingCustomFields] = useState(false);
  const [isSavingCustomFields, setIsSavingCustomFields] = useState(false);
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    phone: '',
    countryCode: '',
    totalContacts: 0,
  });

  // Thêm/xóa trường tùy chỉnh vào audience - cấu trúc mới
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number) => {
      setAudienceCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.id === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        // Tìm custom field từ danh sách để lấy thông tin đầy đủ
        const customField = customFields.find(field => field.id === fieldId);
        if (!customField) {
          console.warn(`Custom field with id ${fieldId} not found`);
          return prev;
        }

        // Thêm trường mới với cấu trúc MarketingCustomFieldResponse + value
        const newField: SelectedCustomField = {
          ...customField,
          value: { value: '' }, // Giá trị mặc định
        };

        return [...prev, newField];
      });
    },
    [customFields]
  );

  // Xóa trường tùy chỉnh khỏi audience
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setAudienceCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong audience
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setAudienceCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Xử lý thay đổi avatar
  const handleAvatarClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const handleAvatarChange = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert(t('marketing:audience.avatar.invalidFileType', 'Vui lòng chọn file hình ảnh'));
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert(t('marketing:audience.avatar.fileTooLarge', 'File quá lớn. Vui lòng chọn file nhỏ hơn 5MB'));
      return;
    }

    setIsUploadingAvatar(true);

    try {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);
      setAvatarUrl(previewUrl);

      // Here you would typically upload to your server
      // For now, we'll just use the preview URL
      console.log('Avatar file selected:', file.name);

      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error('Error uploading avatar:', error);
      alert(t('marketing:audience.avatar.uploadError', 'Có lỗi xảy ra khi tải lên avatar'));
      // Reset to original avatar on error
      setAvatarUrl(`https://i.pravatar.cc/150?img=${audienceId}`);
    } finally {
      setIsUploadingAvatar(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  }, [audienceId, t]);

  // Initialize form data when audience data is loaded
  useEffect(() => {
    if (audience) {
      setEditFormData({
        name: audience.name || '',
        email: audience.email || '',
        phone: audience.phone || '',
        countryCode: audience.countryCode || '',
        totalContacts: audience.totalContacts || 0,
      });
    }
  }, [audience]);

  // Xử lý bắt đầu chỉnh sửa thông tin chung
  const handleStartEditGeneral = useCallback(() => {
    setIsEditingGeneral(true);
  }, []);

  // Xử lý hủy chỉnh sửa thông tin chung
  const handleCancelEditGeneral = useCallback(() => {
    setIsEditingGeneral(false);
    // Reset form data to original values
    if (audience) {
      setEditFormData({
        name: audience.name || '',
        email: audience.email || '',
        phone: audience.phone || '',
        countryCode: audience.countryCode || '',
        totalContacts: audience.totalContacts || 0,
      });
    }
  }, [audience]);

  // Xử lý lưu thay đổi thông tin chung
  const handleSaveEditGeneral = useCallback(async () => {
    setIsSavingGeneral(true);
    try {
      // Here you would call your API to update the audience
      console.log('Saving audience general data:', editFormData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setIsEditingGeneral(false);
      // You might want to refetch the audience data here

    } catch (error) {
      console.error('Error saving audience:', error);
      alert(t('marketing:audience.edit.saveError', 'Có lỗi xảy ra khi lưu thông tin'));
    } finally {
      setIsSavingGeneral(false);
    }
  }, [editFormData, t]);

  // Xử lý thay đổi form data
  const handleFormDataChange = useCallback((field: string, value: string | number) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  // Xử lý bắt đầu chỉnh sửa custom fields
  const handleStartEditCustomFields = useCallback(() => {
    setIsEditingCustomFields(true);
  }, []);

  // Xử lý hủy chỉnh sửa custom fields
  const handleCancelEditCustomFields = useCallback(() => {
    setIsEditingCustomFields(false);
    // Reset custom fields về trạng thái ban đầu nếu cần
  }, []);

  // Xử lý lưu custom fields
  const handleSaveEditCustomFields = useCallback(async () => {
    setIsSavingCustomFields(true);
    try {
      // Transform audienceCustomFields to BulkUpdateCustomFieldsDto format
      const bulkUpdateData: BulkUpdateCustomFieldsDto = {
        fields: audienceCustomFields.map((field): CreateCustomFieldDto => ({
          fieldId: field.id,
          fieldValue: field.value?.['value'] ?? '',
        })),
      };

      console.log('Saving audience custom fields:', bulkUpdateData);

      // Call API to bulk update custom fields
      await bulkUpdateCustomFields.mutateAsync(bulkUpdateData);

      setIsEditingCustomFields(false);
      console.log('Custom fields saved successfully');

    } catch (error) {
      console.error('Error saving custom fields:', error);
      alert(t('marketing:audience.customFields.saveError', 'Có lỗi xảy ra khi lưu trường tùy chỉnh'));
    } finally {
      setIsSavingCustomFields(false);
    }
  }, [audienceCustomFields, bulkUpdateCustomFields, t]);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="alert-circle" className="w-16 h-16 text-red-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-red-600 mb-2">
            {t('marketing:audience.loadError')}
          </Typography>
          <Typography variant="body2" className="text-gray-500">
            {error.message}
          </Typography>
        </div>
      </div>
    );
  }

  // Not found state
  if (!audience) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Icon name="users" className="w-16 h-16 text-gray-300 mb-4 mx-auto" />
          <Typography variant="h6" className="text-gray-500 mb-2">
            {t('marketing:audience.notFound')}
          </Typography>
          <Typography variant="body2" className="text-gray-400">
            {t('marketing:audience.notFoundDescription')}
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-border pb-4">
        <div>
          <Typography variant="h4" className="text-foreground">
            {t('marketing:audience.detailForm')}
          </Typography>
          <Typography variant="body2" className="text-muted mt-1">
            {audience.name}
          </Typography>
        </div>
        <div className="flex space-x-3">
          {onEdit && (
            <IconCard
              icon="settings"
              variant="secondary"
              size="md"
              title={t('marketing:audience.edit.advanced', 'Cài đặt nâng cao')}
              onClick={onEdit}
            />
          )}
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('marketing:audience.close', 'Đóng')}
            onClick={onClose}
          />
        </div>
      </div>

      {/* Thông tin chung */}
      <CollapsibleCard
        title={
          <div className="flex items-center justify-between w-full">
            <Typography variant="h6" className="font-medium">
              {t('marketing:audience.generalInfo', 'Thông tin chung')}
            </Typography>
            <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
              {isEditingGeneral ? (
                <>
                  <IconCard
                    icon="x"
                    variant="ghost"
                    size="sm"
                    title={t('marketing:audience.edit.cancel', 'Hủy')}
                    onClick={handleCancelEditGeneral}
                  />
                  <IconCard
                    icon="check"
                    variant="primary"
                    size="sm"
                    title={t('marketing:audience.edit.save', 'Lưu')}
                    onClick={handleSaveEditGeneral}
                    disabled={isSavingGeneral}
                  />
                </>
              ) : (
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  title={t('marketing:audience.edit.title', 'Chỉnh sửa')}
                  onClick={handleStartEditGeneral}
                />
              )}
            </div>
          </div>
        }
        defaultOpen={true}
        className="mb-4"
      >

        {/* Avatar và thông tin cơ bản */}
        <div className="flex items-start space-x-4 mb-6">
          <div className="relative flex flex-col items-center">
            <div
              className="relative cursor-pointer group"
              onClick={handleAvatarClick}
              title={t('marketing:audience.avatar.clickToChange', 'Nhấp để thay đổi avatar')}
            >
              <Avatar
                src={avatarUrl}
                alt={audience.name}
                size="3xl"
                className={`transition-all duration-200 ${isUploadingAvatar ? 'opacity-50' : 'group-hover:opacity-80'}`}
              />
              {/* Overlay icon */}
              <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 bg-black bg-opacity-30 rounded-full">
                <Icon name="camera" className="w-8 h-8 text-white" />
              </div>
              {/* Loading indicator */}
              {isUploadingAvatar && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <Loading size="sm" />
                </div>
              )}
            </div>
            <Typography variant="caption" className="text-muted mt-2 text-center">
              {t('marketing:audience.avatar.clickToChange', 'Nhấp để thay đổi avatar')}
            </Typography>
            {/* Hidden file input */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleAvatarChange}
              className="hidden"
            />
          </div>

          <div className="flex-1">
            <Typography variant="body2" className="text-muted mb-2">
              ID: {audience.id}
            </Typography>
            <Typography variant="h5" className="text-foreground font-semibold mb-1">
              {audience.name}
            </Typography>
            {audience.email && (
              <Typography variant="body2" className="text-muted">
                {audience.email}
              </Typography>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* ID Field - Always read-only */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.id', 'ID')}
            </Typography>
            <Typography variant="body1" className="text-foreground font-medium">
              {audience.id}
            </Typography>
          </div>

          {/* Name Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.name', 'Tên đối tượng')}
            </Typography>
            {isEditingGeneral ? (
              <Input
                value={editFormData.name}
                onChange={(e) => handleFormDataChange('name', e.target.value)}
                placeholder={t('marketing:audience.name.placeholder', 'Nhập tên đối tượng')}
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.name}
              </Typography>
            )}
          </div>

          {/* Email Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.email', 'Email')}
            </Typography>
            {isEditingGeneral ? (
              <Input
                type="email"
                value={editFormData.email}
                onChange={(e) => handleFormDataChange('email', e.target.value)}
                placeholder={t('marketing:audience.email.placeholder', 'Nhập email')}
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.email || t('marketing:audience.noData', 'Chưa có dữ liệu')}
              </Typography>
            )}
          </div>

          {/* Phone Field */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.phone', 'Số điện thoại')}
            </Typography>
            {isEditingGeneral ? (
              <Input
                value={editFormData.phone}
                onChange={(e) => handleFormDataChange('phone', e.target.value)}
                placeholder={t('marketing:audience.phone.placeholder', 'Nhập số điện thoại')}
                fullWidth
              />
            ) : (
              <Typography variant="body1" className="text-foreground font-medium">
                {audience.phone ?
                  (audience.countryCode ? `${audience.countryCode} ${audience.phone}` : audience.phone) :
                  t('marketing:audience.noData', 'Chưa có dữ liệu')
                }
              </Typography>
            )}
          </div>

          {/* Total Contacts Field - Always read-only */}
          <div>
            <Typography variant="body2" className="text-muted mb-1">
              {t('marketing:audience.totalContacts', 'Tổng số liên hệ')}
            </Typography>
            <Typography variant="body1" className="text-foreground font-medium">
              {audience.totalContacts || 0}
            </Typography>
          </div>
        </div>
      </CollapsibleCard>

      {/* Trường tùy chỉnh */}
      <CollapsibleCard
        title={
          <div className="flex items-center justify-between w-full">
            <Typography variant="h6" className="font-medium">
              {t('marketing:audience.customFields.title', 'Trường tùy chỉnh')}
            </Typography>
            <div className="flex space-x-2" onClick={(e) => e.stopPropagation()}>
              {isEditingCustomFields ? (
                <>
                  <IconCard
                    icon="x"
                    variant="ghost"
                    size="sm"
                    title={t('marketing:audience.edit.cancel', 'Hủy')}
                    onClick={handleCancelEditCustomFields}
                  />
                  <IconCard
                    icon="check"
                    variant="primary"
                    size="sm"
                    title={t('marketing:audience.customFields.save', 'Lưu trường tùy chỉnh')}
                    onClick={handleSaveEditCustomFields}
                    disabled={isSavingCustomFields}
                  />
                </>
              ) : (
                <IconCard
                  icon="edit"
                  variant="ghost"
                  size="sm"
                  title={t('marketing:audience.customFields.edit', 'Chỉnh sửa trường tùy chỉnh')}
                  onClick={handleStartEditCustomFields}
                />
              )}
            </div>
          </div>
        }
        defaultOpen={false}
        className="mb-4"
      >
        <div className="space-y-4">
          <GenericCustomFieldSelector
            onFieldSelect={(fieldData) => {
              handleToggleCustomFieldToProduct(fieldData.id);
            }}
            selectedFieldIds={audienceCustomFields.map(f => f.id)}
            placeholder={t(
              'marketing:audience.customFields.searchPlaceholder',
              'Nhấp để tải dữ liệu hoặc nhập từ khóa để tìm kiếm...'
            )}
            searchFunction={searchFunction}
            title={t('marketing:customField.title', 'Trường tùy chỉnh')}
            translationNamespace="marketing"
            usePortal={true}
          />

          {audienceCustomFields.length > 0 && (
            <div className="space-y-3">
              {audienceCustomFields.map(field => {
                // Adapter để chuyển đổi cấu trúc mới sang cấu trúc cũ cho CustomFieldRenderer
                const adaptedField = {
                  id: field.id,
                  fieldId: field.id,
                  label: field.displayName,
                  component: field.dataType as string,
                  type: field.dataType as string,
                  required: false,
                  configJson: field.config as Record<string, unknown>,
                  value: field.value,
                };

                return (
                  <CustomFieldRenderer
                    key={field.id}
                    field={adaptedField}
                    value={String(field.value?.['value'] ?? '')}
                    onChange={value => handleUpdateCustomFieldInProduct(field.id, value)}
                    onRemove={() => handleRemoveCustomFieldFromProduct(field.id)}
                  />
                );
              })}
            </div>
          )}

          {/* Display existing attributes from audience data */}
          {audience.attributes && audience.attributes.length > 0 && (
            <div className="space-y-3">
              <Typography variant="body2" className="font-medium text-muted">
                {t('marketing:audience.existingAttributes', 'Thuộc tính hiện có:')}
              </Typography>
              {audience.attributes.map((attribute) => (
                <div key={attribute.id} className="flex items-center justify-between p-3 bg-muted/20 rounded-lg">
                  <Typography variant="body2" className="text-muted">
                    {attribute.name}
                  </Typography>
                  <Typography variant="body1" className="text-foreground font-medium">
                    {attribute.value}
                  </Typography>
                </div>
              ))}
            </div>
          )}
        </div>
      </CollapsibleCard>
    </div>
  );
};

export default AudienceDetailView;
