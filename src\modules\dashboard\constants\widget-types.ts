/**
 * Widget type constants for dashboard components
 * Centralized widget type definitions for type safety and consistency
 */

export const WIDGET_TYPES = {
  DATA_COUNT: 'data-count',
  DATA_STORAGE: 'data-storage',
  CHART: 'chart',
  METRIC: 'metric',
  TABLE: 'table',
  CUSTOM: 'custom'
} as const;

/**
 * Widget type union type derived from WIDGET_TYPES constants
 */
export type WidgetType = typeof WIDGET_TYPES[keyof typeof WIDGET_TYPES];

/**
 * Widget type validation helper
 */
export const isValidWidgetType = (type: string): type is WidgetType => {
  return Object.values(WIDGET_TYPES).includes(type as WidgetType);
};

/**
 * Get widget type display name
 */
export const getWidgetTypeDisplayName = (type: WidgetType): string => {
  const displayNames: Record<WidgetType, string> = {
    [WIDGET_TYPES.DATA_COUNT]: 'Tổng số lượng dữ liệu',
    [WIDGET_TYPES.DATA_STORAGE]: 'Dung lượng dữ liệu',
    [WIDGET_TYPES.CHART]: 'Biểu đồ',
    [WIDGET_TYPES.METRIC]: 'Chỉ số',
    [WIDGET_TYPES.TABLE]: 'Bảng dữ liệu',
    [WIDGET_TYPES.CUSTOM]: 'Tùy chỉnh'
  };
  
  return displayNames[type] || 'Không xác định';
};

/**
 * Widget type categories for grouping
 */
export const WIDGET_CATEGORIES = {
  DATA: 'data',
  VISUALIZATION: 'visualization',
  CUSTOM: 'custom'
} as const;

export type WidgetCategory = typeof WIDGET_CATEGORIES[keyof typeof WIDGET_CATEGORIES];

/**
 * Map widget types to categories
 */
export const WIDGET_TYPE_CATEGORIES: Record<WidgetType, WidgetCategory> = {
  [WIDGET_TYPES.DATA_COUNT]: WIDGET_CATEGORIES.DATA,
  [WIDGET_TYPES.DATA_STORAGE]: WIDGET_CATEGORIES.DATA,
  [WIDGET_TYPES.CHART]: WIDGET_CATEGORIES.VISUALIZATION,
  [WIDGET_TYPES.METRIC]: WIDGET_CATEGORIES.VISUALIZATION,
  [WIDGET_TYPES.TABLE]: WIDGET_CATEGORIES.VISUALIZATION,
  [WIDGET_TYPES.CUSTOM]: WIDGET_CATEGORIES.CUSTOM
};
