/**
 * WorkflowBuilder - Main component cho email automation workflow builder
 */

import React, { useCallback, useMemo } from 'react';
import {
  ReactFlow,
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  ReactFlowProvider,
} from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { Card } from '@/shared/components/common';
import { useWorkflowBuilder } from '../../hooks/useWorkflowBuilder';
import { Toolbox } from '../panels/Toolbox';
import { PropertiesPanel } from '../panels/PropertiesPanel';
import { ExecutionPanel } from '../panels/ExecutionPanel';
import { SendEmailNode } from '../nodes/ActionNodes/SendEmailNode';
import { WaitNode } from '../nodes/ActionNodes/WaitNode';
import { IfElseNode } from '../nodes/ConditionNodes/IfElseNode';
import type { WorkflowBuilderProps } from './WorkflowBuilder.types';
import type { WorkflowEdge, Workflow } from '../../types';

/**
 * Node types mapping
 */
const nodeTypes = {
  sendEmail: SendEmailNode,
  wait: WaitNode,
  ifElse: IfElseNode,
};

/**
 * WorkflowBuilder component
 */
const WorkflowBuilderContent: React.FC<WorkflowBuilderProps> = ({
  workflowId,
  initialWorkflow,
  onSave,
  onTest,
  onPublish,
  readOnly = false,
}) => {
  // Build hook params conditionally to avoid undefined values
  const hookParams: {
    onSave: (workflow: Workflow) => void;
    workflowId?: string;
    initialWorkflow?: Workflow;
    onTest?: (workflow: Workflow) => void;
    onPublish?: (workflow: Workflow) => void;
  } = {
    onSave,
  };

  // Only include properties if they have values
  if (workflowId !== undefined) {
    hookParams.workflowId = workflowId;
  }

  if (initialWorkflow !== undefined) {
    hookParams.initialWorkflow = initialWorkflow;
  }

  if (onTest !== undefined) {
    hookParams.onTest = onTest;
  }

  if (onPublish !== undefined) {
    hookParams.onPublish = onPublish;
  }

  const {
    state,
    actions,
  } = useWorkflowBuilder(hookParams);

  const [nodes, setNodes, onNodesChange] = useNodesState(state.nodes as Node[]);
  const [edges, setEdges, onEdgesChange] = useEdgesState(state.edges);

  // Handle connection between nodes
  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: WorkflowEdge = {
        ...params,
        id: `edge-${params.source}-${params.target}`,
        type: 'default',
        data: {},
      } as WorkflowEdge;
      
      setEdges((eds) => addEdge(newEdge, eds));
      actions.addEdge(newEdge);
    },
    [setEdges, actions]
  );

  // Handle node selection
  const onNodeClick = useCallback(
    (_event: React.MouseEvent, node: Node) => {
      actions.selectNode(node.id);
    },
    [actions]
  );

  // Handle edge selection
  const onEdgeClick = useCallback(
    (_event: React.MouseEvent, edge: Edge) => {
      actions.selectEdge(edge.id);
    },
    [actions]
  );

  // Handle node deletion
  const onNodesDelete = useCallback(
    (nodesToDelete: Node[]) => {
      nodesToDelete.forEach((node) => {
        actions.deleteNode(node.id);
      });
    },
    [actions]
  );

  // Handle edge deletion
  const onEdgesDelete = useCallback(
    (edgesToDelete: Edge[]) => {
      edgesToDelete.forEach((edge) => {
        actions.deleteEdge(edge.id);
      });
    },
    [actions]
  );

  // Sync nodes and edges with state
  React.useEffect(() => {
    setNodes(state.nodes as Node[]);
  }, [state.nodes, setNodes]);

  React.useEffect(() => {
    setEdges(state.edges);
  }, [state.edges, setEdges]);

  const minimapStyle = useMemo(
    () => ({
      height: 120,
      backgroundColor: 'var(--background)',
      border: '1px solid var(--border)',
    }),
    []
  );

  return (
    <div className="h-full w-full flex bg-background">
      {/* Toolbox */}
      {state.isToolboxOpen && (
        <div className="w-80 border-r border-border bg-card">
          <Toolbox
            onAddNode={actions.addNode}
            onClose={() => actions.toggleToolbox()}
          />
        </div>
      )}

      {/* Main Canvas */}
      <div className="flex-1 relative">
        <ReactFlow
          nodes={nodes}
          edges={edges}
          onNodesChange={onNodesChange}
          onEdgesChange={onEdgesChange}
          onConnect={onConnect}
          onNodeClick={onNodeClick}
          onEdgeClick={onEdgeClick}
          onNodesDelete={onNodesDelete}
          onEdgesDelete={onEdgesDelete}
          nodeTypes={nodeTypes}
          fitView
          attributionPosition="bottom-left"
          className="bg-background"
          deleteKeyCode={readOnly ? null : ['Backspace', 'Delete']}
          multiSelectionKeyCode={readOnly ? null : ['Meta', 'Ctrl']}
          panOnDrag={!readOnly}
          zoomOnScroll={!readOnly}
          nodesDraggable={!readOnly}
          nodesConnectable={!readOnly}
          elementsSelectable={!readOnly}
        >
          <Background
            color="var(--muted-foreground)"
            gap={20}
            size={1}
          />
          <Controls
            position="bottom-right"
            showInteractive={!readOnly}
          />
          <MiniMap
            style={minimapStyle}
            position="bottom-left"
            pannable
            zoomable
          />
        </ReactFlow>

        {/* Workflow Header */}
        <div className="absolute top-4 left-4 right-4 z-10">
          <Card className="p-4 bg-card/95 backdrop-blur-sm">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-lg font-semibold">
                  {state.workflow?.name || 'Untitled Workflow'}
                </h1>
                <p className="text-sm text-muted-foreground">
                  {state.workflow?.description || 'No description'}
                </p>
              </div>
              <div className="flex items-center space-x-2">
                {!readOnly && (
                  <>
                    <button
                      onClick={() => actions.toggleToolbox()}
                      className="px-3 py-1 text-sm bg-primary text-primary-foreground rounded hover:bg-primary/90"
                    >
                      {state.isToolboxOpen ? 'Hide Toolbox' : 'Show Toolbox'}
                    </button>
                    <button
                      onClick={() => actions.togglePropertiesPanel()}
                      className="px-3 py-1 text-sm bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
                    >
                      Properties
                    </button>
                    {state.workflow && (
                      <button
                        onClick={() => onSave(state.workflow!)}
                        className="px-3 py-1 text-sm bg-success text-success-foreground rounded hover:bg-success/90"
                        disabled={!state.isDirty}
                      >
                        Save
                      </button>
                    )}
                  </>
                )}
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Properties Panel */}
      {state.isPropertiesPanelOpen && (
        <div className="w-80 border-l border-border bg-card">
          <PropertiesPanel
            selectedNodeId={state.selectedNodeId}
            selectedEdgeId={state.selectedEdgeId}
            onUpdateNode={actions.updateNode}
            onUpdateEdge={actions.updateEdge}
            onClose={() => actions.togglePropertiesPanel()}
          />
        </div>
      )}

      {/* Execution Panel */}
      {state.isExecutionPanelOpen && workflowId && (
        <div className="w-80 border-l border-border bg-card">
          <ExecutionPanel
            workflowId={workflowId}
            onClose={() => actions.toggleExecutionPanel()}
          />
        </div>
      )}
    </div>
  );
};

/**
 * WorkflowBuilder with ReactFlowProvider wrapper
 */
export const WorkflowBuilder: React.FC<WorkflowBuilderProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowBuilderContent {...props} />
    </ReactFlowProvider>
  );
};

export default WorkflowBuilder;
