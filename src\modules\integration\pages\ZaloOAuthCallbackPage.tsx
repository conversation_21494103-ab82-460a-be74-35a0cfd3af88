import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';

// UI Components
import { Loading } from '@/shared/components/common';

// Hooks
import { useZaloOAuthCallback } from '../hooks/useZaloOAuth';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang xử lý callback OAuth Zalo
 * Route: /integration/zalo/oa
 * Params: ?code=<AUTHORIZATION_CODE>&oa_id=<OA_ID>&state=<STATE>
 */
const ZaloOAuthCallbackPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const callbackMutation = useZaloOAuthCallback();

  useEffect(() => {
    const handleCallback = async () => {
      // Lấy parameters từ URL
      const code = searchParams.get('code');
      const oaId = searchParams.get('oa_id');
      const state = searchParams.get('state');

      console.log(oaId);

      // Kiểm tra required parameters
      if (!code) {
        NotificationUtil.error({
          message: 'Thiếu mã xác thực từ Zalo',
          title: 'Lỗi xác thực',
        });
        navigate('/integrations/social/zalo-oa');
        return;
      }

      if (!state) {
        NotificationUtil.error({
          message: 'Thiếu state parameter từ Zalo',
          title: 'Lỗi xác thực',
        });
        navigate('/integrations/social/zalo-oa');
        return;
      }

      // Gọi API callback
      try {
        await callbackMutation.mutateAsync({
          code,
          state,
        });

        // Thành công - chuyển đến trang accounts
        navigate('/marketing/zalo/accounts');
      } catch {
        // Lỗi - quay lại trang connect
        navigate('/integrations/social/zalo-oa');
      }
    };

    handleCallback();
  }, [searchParams, callbackMutation, navigate]);

  return (
    <div className="w-full h-screen bg-background text-foreground flex items-center justify-center">
      <div className="text-center">
        <Loading size="lg" />
      </div>
    </div>
  );
};

export default ZaloOAuthCallbackPage;
