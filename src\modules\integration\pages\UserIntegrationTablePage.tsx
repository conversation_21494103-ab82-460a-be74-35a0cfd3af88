import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Card,
  Table,
  ConfirmDeleteModal,
  Chip,
  ActionMenu,
  Icon,
} from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import { TableColumn } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { FilterOption } from '@/shared/hooks/table/useFilterOptions';
import { formatDate } from '@/shared/utils/date';
import { NotificationUtil } from '@/shared/utils/notification';

// Import types và services
import {
  UserIntegrationDto,
  UserIntegrationQueryDto,
  UpdateUserIntegrationDto,
  IntegrationType,
  IntegrationStatus,
  getIntegrationTypeLabel,
  getIntegrationStatusLabel,
  getIntegrationTypeIcon,
} from '../types/user-integration.types';
import {
  getUserIntegrations,
  updateUserIntegration,
  deleteUserIntegration,
  toggleUserIntegrationStatus,
  testUserIntegrationConnection,
} from '../services/user-integration.api';
import UserIntegrationForm from '../components/UserIntegrationForm';

/**
 * Trang quản lý tích hợp của người dùng với table
 */
const UserIntegrationTablePage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);
  const navigate = useNavigate();

  // States
  const [integrations, setIntegrations] = useState<UserIntegrationDto[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [integrationToDelete, setIntegrationToDelete] = useState<UserIntegrationDto | null>(null);
  const [integrationToView, setIntegrationToView] = useState<UserIntegrationDto | null>(null);
  const [integrationToEdit, setIntegrationToEdit] = useState<UserIntegrationDto | null>(null);

  // Slide form hooks - chỉ giữ lại view và edit form
  const {
    isVisible: isViewFormVisible,
    showForm: showViewForm,
    hideForm: hideViewForm,
  } = useSlideForm();

  const {
    isVisible: isEditFormVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Định nghĩa columns cho table
  const columns: TableColumn<UserIntegrationDto>[] = useMemo(() => [
    {
      key: 'integrationName',
      title: t('integration:name'),
      dataIndex: 'integrationName',
      sortable: true,
      width: '35%',
      render: (value: unknown, record: UserIntegrationDto) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            <Icon
              name={getIntegrationTypeIcon(record.type)}
              className="w-5 h-5 text-primary"
            />
          </div>
          <div>
            <div className="font-medium text-foreground">{String(value)}</div>
            <div className="text-sm text-muted-foreground">
              {getIntegrationTypeLabel(record.type)}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'type',
      title: t('integration:type'),
      dataIndex: 'type',
      sortable: true,
      width: '18%',
      render: (value: unknown) => (
        <Chip
          variant="secondary"
          size="sm"
          outlined
        >
          {getIntegrationTypeLabel(value as IntegrationType)}
        </Chip>
      ),
    },
    {
      key: 'status',
      title: t('integration:status'),
      dataIndex: 'status',
      sortable: true,
      width: '15%',
      render: (value: unknown) => {
        const status = (value as IntegrationStatus) || IntegrationStatus.INACTIVE;
        const getStatusVariant = (status: IntegrationStatus) => {
          const colorMap: Record<IntegrationStatus, string> = {
            [IntegrationStatus.ACTIVE]: 'success',
            [IntegrationStatus.INACTIVE]: 'secondary',
            [IntegrationStatus.PENDING]: 'warning',
            [IntegrationStatus.ERROR]: 'danger',
          };
          return colorMap[status] || 'default';
        };

        return (
          <Chip
            variant={getStatusVariant(status) as 'success' | 'secondary' | 'warning' | 'danger' | 'default'}
            size="sm"
          >
            {getIntegrationStatusLabel(status)}
          </Chip>
        );
      },
    },

    {
      key: 'createdAt',
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      sortable: true,
      width: '18%',
      render: (value: unknown) => {
        const dateString = String(value || '');
        return (
          <div className="text-sm text-muted-foreground">
            {dateString ? formatDate(new Date(dateString)) : '-'}
          </div>
        );
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '14%',
      render: (_, record: UserIntegrationDto) => (
        <ActionMenu
          items={getActionMenuItems(record)}
          placement="bottom"
        />
      ),
    },
  // eslint-disable-next-line react-hooks/exhaustive-deps
  ], [t]);

  // Định nghĩa filter options
  const filterOptions: FilterOption[] = useMemo(() => [
    {
      id: 'all',
      label: t('common:all'),
      icon: 'list',
      value: 'all',
    },
    // Filter theo loại tích hợp
    {
      id: 'analytics',
      label: getIntegrationTypeLabel(IntegrationType.ANALYTICS),
      icon: getIntegrationTypeIcon(IntegrationType.ANALYTICS),
      value: IntegrationType.ANALYTICS,
    },
    {
      id: 'payment',
      label: getIntegrationTypeLabel(IntegrationType.PAYMENT),
      icon: getIntegrationTypeIcon(IntegrationType.PAYMENT),
      value: IntegrationType.PAYMENT,
    },
    {
      id: 'email',
      label: getIntegrationTypeLabel(IntegrationType.EMAIL),
      icon: getIntegrationTypeIcon(IntegrationType.EMAIL),
      value: IntegrationType.EMAIL,
    },
    {
      id: 'api',
      label: getIntegrationTypeLabel(IntegrationType.API),
      icon: getIntegrationTypeIcon(IntegrationType.API),
      value: IntegrationType.API,
    },
    // Filter theo trạng thái
    {
      id: 'active',
      label: getIntegrationStatusLabel(IntegrationStatus.ACTIVE),
      icon: 'check',
      value: IntegrationStatus.ACTIVE,
    },
    {
      id: 'inactive',
      label: getIntegrationStatusLabel(IntegrationStatus.INACTIVE),
      icon: 'x',
      value: IntegrationStatus.INACTIVE,
    },
    {
      id: 'pending',
      label: getIntegrationStatusLabel(IntegrationStatus.PENDING),
      icon: 'clock',
      value: IntegrationStatus.PENDING,
    },
    {
      id: 'error',
      label: getIntegrationStatusLabel(IntegrationStatus.ERROR),
      icon: 'alert-circle',
      value: IntegrationStatus.ERROR,
    },
  ], [t]);

  // Tạo query params
  const createQueryParams = useCallback((filters: Record<string, unknown>): Partial<UserIntegrationQueryDto> => {
    const params: Partial<UserIntegrationQueryDto> = {
      page: (filters['page'] as number) || 1,
      limit: (filters['pageSize'] as number) || 10,
      sortDirection: filters['sortOrder'] === 'ascend' ? SortDirection.ASC : SortDirection.DESC,
    };

    // Only add search if it's not empty
    if (filters['search'] && typeof filters['search'] === 'string' && filters['search'].trim()) {
      params.search = filters['search'].trim();
    }

    // Only add sortBy if it exists
    if (filters['sortField'] && typeof filters['sortField'] === 'string') {
      params.sortBy = filters['sortField'];
    }

    // Handle filter values for type and status
    if (filters['filterValue'] && filters['filterValue'] !== 'all') {
      // Check if it's a valid IntegrationType
      if (Object.values(IntegrationType).includes(filters['filterValue'] as IntegrationType)) {
        params.type = filters['filterValue'] as IntegrationType;
      }
      // Check if it's a valid IntegrationStatus
      if (Object.values(IntegrationStatus).includes(filters['filterValue'] as IntegrationStatus)) {
        params.status = filters['filterValue'] as IntegrationStatus;
      }
    }

    return params;
  }, []);

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<UserIntegrationDto, Partial<UserIntegrationQueryDto>>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearSort, handleClearAll } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [IntegrationType.ANALYTICS]: getIntegrationTypeLabel(IntegrationType.ANALYTICS),
      [IntegrationType.PAYMENT]: getIntegrationTypeLabel(IntegrationType.PAYMENT),
      [IntegrationType.EMAIL]: getIntegrationTypeLabel(IntegrationType.EMAIL),
      [IntegrationType.API]: getIntegrationTypeLabel(IntegrationType.API),
      [IntegrationStatus.ACTIVE]: getIntegrationStatusLabel(IntegrationStatus.ACTIVE),
      [IntegrationStatus.INACTIVE]: getIntegrationStatusLabel(IntegrationStatus.INACTIVE),
      [IntegrationStatus.PENDING]: getIntegrationStatusLabel(IntegrationStatus.PENDING),
      [IntegrationStatus.ERROR]: getIntegrationStatusLabel(IntegrationStatus.ERROR),
    },
    t,
  });

  // Tạo query params từ dataTable state
  const queryParams = useMemo(() => {
    return createQueryParams({
      page: dataTable.tableData.currentPage,
      pageSize: dataTable.tableData.pageSize,
      search: dataTable.tableData.searchTerm,
      sortField: dataTable.tableData.sortBy,
      sortOrder: dataTable.tableData.sortDirection === SortDirection.ASC ? 'ascend' : 'descend',
      filterValue: dataTable.filter.selectedValue,
    });
  }, [
    dataTable.tableData.currentPage,
    dataTable.tableData.pageSize,
    dataTable.tableData.searchTerm,
    dataTable.tableData.sortBy,
    dataTable.tableData.sortDirection,
    dataTable.filter.selectedValue,
    createQueryParams,
  ]);

  // Load data
  const loadIntegrations = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await getUserIntegrations(queryParams);
      setIntegrations(response.result.items);
      setTotalItems(response.result.meta.totalItems);
    } catch (error) {
      console.error('Error loading integrations:', error);
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: t('integration:loadError'),
      });
    } finally {
      setIsLoading(false);
    }
  }, [queryParams, t]);

  // Load data khi queryParams thay đổi
  useEffect(() => {
    loadIntegrations();
  }, [loadIntegrations]);

  // Event handlers
  const handleViewIntegration = useCallback((integration: UserIntegrationDto) => {
    // Đóng tất cả form khác trước khi mở form view
    hideEditForm();
    setIntegrationToEdit(null);

    setIntegrationToView(integration);
    showViewForm();
  }, [showViewForm, hideEditForm]);

  const handleEditIntegration = useCallback((integration: UserIntegrationDto) => {
    // Đóng tất cả form khác trước khi mở form edit
    hideViewForm();
    setIntegrationToView(null);

    setIntegrationToEdit(integration);
    showEditForm();
  }, [showEditForm, hideViewForm]);

  const handleDeleteIntegration = useCallback((integration: UserIntegrationDto) => {
    setIntegrationToDelete(integration);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!integrationToDelete) return;

    try {
      await deleteUserIntegration(String(integrationToDelete.id));
      NotificationUtil.success({
        title: t('common:success', 'Thành công'),
        message: t('integration:deleteSuccess'),
      });
      setIntegrationToDelete(null);
      loadIntegrations();
    } catch (error) {
      console.error('Error deleting integration:', error);
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: t('integration:deleteError'),
      });
    }
  }, [integrationToDelete, t, loadIntegrations]);

  const handleToggleStatus = useCallback(async (integration: UserIntegrationDto) => {
    try {
      await toggleUserIntegrationStatus(String(integration.id));
      NotificationUtil.success({
        title: t('common:success', 'Thành công'),
        message: t('integration:statusUpdateSuccess'),
      });
      loadIntegrations();
    } catch (error) {
      console.error('Error toggling status:', error);
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: t('integration:statusUpdateError'),
      });
    }
  }, [t, loadIntegrations]);

  const handleTestConnection = useCallback(async (integration: UserIntegrationDto) => {
    try {
      const response = await testUserIntegrationConnection(String(integration.id));
      if (response.result.success) {
        NotificationUtil.success({
          title: t('common:success', 'Thành công'),
          message: t('integration:testConnectionSuccess'),
        });
      } else {
        NotificationUtil.error({
          title: t('common:error', 'Lỗi'),
          message: response.result.message || t('integration:testConnectionError'),
        });
      }
    } catch (error) {
      console.error('Error testing connection:', error);
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: t('integration:testConnectionError'),
      });
    }
  }, [t]);

  // Action menu items
  const getActionMenuItems = useCallback((integration: UserIntegrationDto): ActionMenuItem[] => [
    {
      id: 'view',
      label: t('common:view'),
      icon: 'eye',
      onClick: () => handleViewIntegration(integration),
    },
    {
      id: 'edit',
      label: t('common:edit'),
      icon: 'edit',
      onClick: () => handleEditIntegration(integration),
    },
    {
      id: 'test',
      label: t('integration:testConnection'),
      icon: 'zap',
      onClick: () => handleTestConnection(integration),
    },
    {
      id: 'toggle',
      label: (integration.status || IntegrationStatus.INACTIVE) === IntegrationStatus.ACTIVE
        ? t('integration:deactivate')
        : t('integration:activate'),
      icon: (integration.status || IntegrationStatus.INACTIVE) === IntegrationStatus.ACTIVE ? 'pause' : 'play',
      onClick: () => handleToggleStatus(integration),
    },
    {
      id: 'delete',
      label: t('common:delete'),
      icon: 'trash',
      onClick: () => handleDeleteIntegration(integration),
    },
  ], [t, handleViewIntegration, handleEditIntegration, handleTestConnection, handleToggleStatus, handleDeleteIntegration]);

  // Handler cho nút thêm integration - chuyển hướng đến trang chính
  const handleAddIntegration = useCallback(() => {
    navigate('/integrations');
  }, [navigate]);

  const handleEditSubmit = useCallback(async (data: UpdateUserIntegrationDto) => {
    if (!integrationToEdit) return;

    try {
      await updateUserIntegration(String(integrationToEdit.id), data);
      NotificationUtil.success({
        title: t('common:success', 'Thành công'),
        message: t('integration:updateSuccess'),
      });
      hideEditForm();
      setIntegrationToEdit(null);
      loadIntegrations();
    } catch (error) {
      console.error('Error updating integration:', error);
      NotificationUtil.error({
        title: t('common:error', 'Lỗi'),
        message: t('integration:updateError'),
      });
    }
  }, [integrationToEdit, hideEditForm, loadIntegrations, t]);

  const handleCloseViewForm = useCallback(() => {
    hideViewForm();
    setIntegrationToView(null);
  }, [hideViewForm]);

  const handleCloseEditForm = useCallback(() => {
    hideEditForm();
    setIntegrationToEdit(null);
  }, [hideEditForm]);

  // Column visibility handler wrapper
  const handleColumnVisibilityChange = useCallback((columns: typeof dataTable.columnVisibility.visibleColumns) => {
    dataTable.columnVisibility.setVisibleColumns(columns);
  }, [dataTable]);

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <Typography variant="h4" className="text-foreground">
              {t('integration:myIntegrations')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground mt-1">
              {t('integration:myIntegrationsDescription')}
            </Typography>
          </div>
          <div className="flex items-center space-x-3">
            <Typography variant="body2" className="text-muted-foreground">
              {totalItems > 0 && `${totalItems} ${t('integration:totalIntegrations', 'tích hợp')}`}
            </Typography>
          </div>
        </div>

        {/* MenuIconBar */}
        <MenuIconBar
          onSearch={dataTable.tableData.handleSearch}
          onAdd={handleAddIntegration}
          items={filterOptions.slice(1).map(option => ({
            id: option.id,
            label: option.label,
            icon: option.icon,
            onClick: () => dataTable.filter.setSelectedId(option.id),
          }))}
          showDateFilter={false}
          showColumnFilter={true}
          columns={dataTable.columnVisibility.visibleColumns}
          onColumnVisibilityChange={handleColumnVisibilityChange}
          isLoading={isLoading}
        />

        {/* Active Filters */}
        <ActiveFilters
          searchTerm={dataTable.tableData.searchTerm}
          onClearSearch={handleClearSearch}
          filterValue={dataTable.filter.selectedValue !== 'all' ? dataTable.filter.selectedValue : undefined}
          filterLabel={dataTable.filter.selectedValue !== 'all' ?
            filterOptions.find(f => f.value === dataTable.filter.selectedValue)?.label : undefined}
          onClearFilter={() => dataTable.filter.setSelectedId('all')}
          onClearAll={handleClearAll}
          sortBy={dataTable.tableData.sortBy}
          sortDirection={dataTable.tableData.sortDirection}
          onClearSort={handleClearSort}
        />

        {/* SlideInForm cho form xem chi tiết */}
        <SlideInForm isVisible={isViewFormVisible}>
          {integrationToView && (
            <UserIntegrationForm
              mode="view"
              initialData={integrationToView}
              onSubmit={() => {}} // Không cần xử lý submit vì chỉ xem
              onCancel={handleCloseViewForm}
            />
          )}
        </SlideInForm>

        {/* SlideInForm cho form chỉnh sửa */}
        <SlideInForm isVisible={isEditFormVisible}>
          {integrationToEdit && (
            <UserIntegrationForm
              mode="edit"
              initialData={integrationToEdit}
              onSubmit={handleEditSubmit}
              onCancel={handleCloseEditForm}
              loading={isLoading}
            />
          )}
        </SlideInForm>

        {/* Table */}
        <Card className="overflow-hidden">
          <Table<UserIntegrationDto>
            columns={dataTable.columnVisibility.visibleTableColumns}
            data={integrations}
            rowKey={(record) => String(record.id)}
            loading={isLoading}
            sortable={true}
            onSortChange={dataTable.tableData.handleSortChange}
            defaultSort={{
              column: dataTable.tableData.sortBy || '',
              order: dataTable.tableData.sortDirection === SortDirection.ASC ? 'asc' : 'desc',
            }}
            pagination={{
              current: dataTable.tableData.currentPage,
              pageSize: dataTable.tableData.pageSize,
              total: totalItems,
              onChange: dataTable.tableData.handlePageChange,
              showSizeChanger: true,
              pageSizeOptions: [10, 20, 50, 100],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Delete Confirmation Modal */}
        <ConfirmDeleteModal
          isOpen={!!integrationToDelete}
          onClose={() => setIntegrationToDelete(null)}
          onConfirm={handleConfirmDelete}
          title={t('integration:deleteConfirmTitle')}
          message={t('integration:deleteConfirmMessage', {
            name: integrationToDelete?.integrationName
          })}
        />
      </div>
    </div>
  );
};

export default UserIntegrationTablePage;
