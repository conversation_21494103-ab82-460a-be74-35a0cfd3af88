import { MenuSection } from '../types';

export const DASHBOARD_MENU_SECTIONS: MenuSection[] = [
  {
    id: 'business',
    title: 'Kinh doanh',
    items: [
      {
        id: 'sales-overview',
        title: 'Tổng quan bán hàng',
        icon: 'trending-up',
        path: '/dashboard/business/sales-overview',
        children: [
          {
            id: 'revenue-analysis',
            title: 'Phân tích doanh thu',
            icon: 'bar-chart-3',
            path: '/dashboard/business/revenue-analysis'
          },
          {
            id: 'sales-performance',
            title: '<PERSON><PERSON><PERSON> suất bán hàng',
            icon: 'target',
            path: '/dashboard/business/sales-performance'
          }
        ]
      },
      {
        id: 'revenue',
        title: '<PERSON>anh thu',
        icon: 'dollar-sign',
        path: '/dashboard/business/revenue',
        children: [
          {
            id: 'revenue-by-channel',
            title: '<PERSON><PERSON>h thu theo kênh',
            icon: 'pie-chart',
            path: '/dashboard/business/revenue-by-channel'
          },
          {
            id: 'revenue-trends',
            title: '<PERSON> hướng doanh thu',
            icon: 'trending-up',
            path: '/dashboard/business/revenue-trends'
          }
        ]
      },
      {
        id: 'orders',
        title: 'Đơn hàng',
        icon: 'shopping-cart',
        path: '/dashboard/business/orders',
        children: [
          {
            id: 'order-status',
            title: 'Trạng thái đơn hàng',
            icon: 'package',
            path: '/dashboard/business/order-status'
          },
          {
            id: 'order-analytics',
            title: 'Phân tích đơn hàng',
            icon: 'bar-chart',
            path: '/dashboard/business/order-analytics'
          }
        ]
      }
    ]
  },
  {
    id: 'integration',
    title: 'Tích hợp',
    items: [
      {
        id: 'api-usage',
        title: 'Sử dụng API',
        icon: 'activity',
        path: '/dashboard/integration/api-usage'
      },
      {
        id: 'webhooks',
        title: 'Webhooks',
        icon: 'webhook',
        path: '/dashboard/integration/webhooks'
      },
      {
        id: 'third-party',
        title: 'Tích hợp bên thứ 3',
        icon: 'link',
        path: '/dashboard/integration/third-party'
      }
    ]
  },
  {
    id: 'marketing',
    title: 'Marketing',
    items: [
      {
        id: 'campaigns',
        title: 'Chiến dịch',
        icon: 'megaphone',
        path: '/dashboard/marketing/campaigns',
        children: [
          {
            id: 'email-campaigns',
            title: 'Chiến dịch Email',
            icon: 'mail',
            path: '/dashboard/marketing/email-campaigns'
          },
          {
            id: 'sms-campaigns',
            title: 'Chiến dịch SMS',
            icon: 'message-square',
            path: '/dashboard/marketing/sms-campaigns'
          }
        ]
      },
      {
        id: 'customer-analytics',
        title: 'Phân tích khách hàng',
        icon: 'users',
        path: '/dashboard/marketing/customer-analytics'
      },
      {
        id: 'conversion-funnel',
        title: 'Phễu chuyển đổi',
        icon: 'filter',
        path: '/dashboard/marketing/conversion-funnel'
      }
    ]
  },
  {
    id: 'marketplace',
    title: 'Marketplace',
    items: [
      {
        id: 'product-performance',
        title: 'Hiệu suất sản phẩm',
        icon: 'box',
        path: '/dashboard/marketplace/product-performance'
      },
      {
        id: 'competitor-analysis',
        title: 'Phân tích đối thủ',
        icon: 'eye',
        path: '/dashboard/marketplace/competitor-analysis'
      },
      {
        id: 'market-trends',
        title: 'Xu hướng thị trường',
        icon: 'trending-up',
        path: '/dashboard/marketplace/market-trends'
      }
    ]
  },
  {
    id: 'data',
    title: 'Quản lý dữ liệu',
    items: [
      {
        id: 'data-count',
        title: 'Tổng số lượng dữ liệu',
        icon: 'hash',
        path: '/dashboard/data/count'
      },
      {
        id: 'data-storage',
        title: 'Dung lượng dữ liệu',
        icon: 'hard-drive',
        path: '/dashboard/data/storage'
      }
    ]
  },
];

export const DEFAULT_DASHBOARD_WIDGETS = [];
