/* Dashboard Container Styles */
.dashboard-container {
  min-height: 1500px; /* Force height for scroll test */
  position: relative;
  width: 100%;
}

.dashboard-grid-container {
  min-height: 100%;
  width: 100%;
  overflow: visible;
}

/* Custom scrollbar for main dashboard area */
.dashboard-main-scroll {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) hsl(var(--muted) / 0.3);
}

.dashboard-main-scroll::-webkit-scrollbar {
  width: 8px;
}

.dashboard-main-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 4px;
}

.dashboard-main-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
  transition: background 200ms ease;
}

.dashboard-main-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* Ensure proper flex behavior for scroll */
.dashboard-main-scroll {
  min-height: 0; /* Critical for flex scroll */
  flex: 1;
}

/* Force content to be taller than container */
.dashboard-container {
  padding-bottom: 100px; /* Extra space to ensure scroll */
}

/* Hide scrollbar but keep scroll functionality */
.scrollbar-hide {
  /* Firefox */
  scrollbar-width: none;

  /* Safari and Chrome */
  -ms-overflow-style: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

/* Dashboard Tabs Styles */
.dashboard-tabs {
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--card));
}

.dashboard-tab {
  position: relative;
  transition: all 200ms ease;
  border-radius: 8px 8px 0 0;
}

.dashboard-tab.active {
  background: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

.dashboard-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: hsl(var(--primary));
}

.dashboard-tab:hover:not(.active) {
  background: hsl(var(--muted));
}

.dashboard-tab-input {
  background: transparent;
  border: none;
  outline: none;
  color: inherit;
  font-weight: inherit;
}

/* Tab drag and drop */
.dashboard-tab.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.dashboard-tab.drag-over {
  border-left: 2px solid hsl(var(--primary));
}

/* Navbar hide/show animation */
.navbar-container {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: top;
}

.navbar-hidden {
  transform: translateY(-100%);
  opacity: 0;
  pointer-events: none;
}

.navbar-visible {
  transform: translateY(0);
  opacity: 1;
  pointer-events: auto;
}

/* Floating edit button */
.floating-edit-btn {
  backdrop-filter: blur(8px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  animation: fadeInUp 0.3s ease-out;
}

.floating-edit-btn:hover {
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dashboard Grid Layout Styles */
.dashboard-grid {
  min-height: 100%;
  overflow: visible;
  position: relative;
}

.dashboard-grid .react-grid-layout {
  position: relative;
  min-height: 100%;
  width: 100%;
}

.dashboard-grid .react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top;
}

.dashboard-grid .react-grid-item.cssTransforms {
  transition-property: transform;
}

.dashboard-grid .react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNiIgaGVpZ2h0PSI2IiB2aWV3Qm94PSIwIDAgNiA2IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8ZG90cyBmaWxsPSIjOTk5IiBkPSJtMTUgMTJjMCAuNTUyLS40NDggMS0xIDFzLTEtLjQ0OC0xLTEgLjQ0OC0xIDEtMSAxIC40NDggMSAxem0wIDRjMCAuNTUyLS40NDggMS0xIDFzLTEtLjQ0OC0xLTEgLjQ0OC0xIDEtMSAxIC40NDggMSAxem0wIDRjMCAuNTUyLS40NDggMS0xIDFzLTEtLjQ0OC0xLTEgLjQ0OC0xIDEtMSAxIC40NDggMSAxem0tNS00YzAtLjU1Mi40NDgtMSAxLTFzMSAuNDQ4IDEgMS0uNDQ4IDEtMSAxLTEtLjQ0OC0xLTF6bTAgNGMwLS41NTIuNDQ4LTEgMS0xczEgLjQ0OCAxIDEtLjQ0OCAxLTEgMS0xLS40NDgtMS0xem0wLThjMC0uNTUyLjQ0OC0xIDEtMXMxIC40NDggMSAxLS40NDggMS0xIDEtMS0uNDQ4LTEtMXptNC00YzAtLjU1Mi40NDgtMSAxLTFzMSAuNDQ4IDEgMS0uNDQ4IDEtMSAxLTEtLjQ0OC0xLTF6bTAtNGMwLS41NTIuNDQ4LTEgMS0xczEgLjQ0OCAxIDEtLjQ0OCAxLTEgMS0xLS40NDgtMS0xeiIvPgo8L3N2Zz4K');
  background-position: bottom right;
  padding: 0 3px 3px 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
}

.dashboard-grid .react-grid-item.react-grid-placeholder {
  background: hsl(var(--primary) / 0.1);
  opacity: 0.2;
  transition-duration: 100ms;
  z-index: 2;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
  border: 2px dashed hsl(var(--primary));
  border-radius: 8px;
}

.dashboard-grid .react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 3;
  opacity: 0.8;
}

.dashboard-grid .react-grid-item.resizing {
  opacity: 0.6;
  z-index: 3;
}

/* Custom scrollbar for sidebar */
.dashboard-sidebar::-webkit-scrollbar {
  width: 6px;
}

.dashboard-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.dashboard-sidebar::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 3px;
}

.dashboard-sidebar::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-grid .react-grid-item > .react-resizable-handle {
    width: 15px;
    height: 15px;
  }
}

/* Animation for sidebar collapse */
.sidebar-transition {
  transition: width 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Widget card hover effects */
.widget-card {
  transition: all 200ms ease;
}

.widget-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Empty state styling */
.empty-dashboard {
  background: radial-gradient(circle at 50% 50%, hsl(var(--muted) / 0.1) 0%, transparent 70%);
}

/* Enhanced scroll behavior */
.dashboard-container {
  scroll-behavior: smooth;
}

/* Improved drag experience */
.dashboard-grid .react-grid-item.react-draggable-dragging {
  transition: none;
  z-index: 1000;
  opacity: 0.9;
  transform: rotate(2deg);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dashboard-grid .react-grid-item.resizing {
  opacity: 0.8;
  z-index: 999;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

/* Better placeholder styling */
.dashboard-grid .react-grid-item.react-grid-placeholder {
  background: hsl(var(--primary) / 0.15);
  opacity: 0.6;
  transition-duration: 200ms;
  z-index: 2;
  border: 2px dashed hsl(var(--primary));
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

/* Smooth transitions for grid items */
.dashboard-grid .react-grid-item {
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
  transition-property: transform, opacity;
}

.dashboard-grid .react-grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Enhanced resize handle */
.dashboard-grid .react-grid-item > .react-resizable-handle {
  position: absolute;
  width: 24px;
  height: 24px;
  bottom: 0;
  right: 0;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.8), hsl(var(--primary)));
  border-radius: 8px 0 8px 0;
  cursor: se-resize;
  opacity: 0;
  transition: opacity 200ms ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dashboard-grid .react-grid-item:hover > .react-resizable-handle {
  opacity: 1;
}

.dashboard-grid .react-grid-item > .react-resizable-handle::after {
  content: '';
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 1px;
  opacity: 0.8;
  mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M22 22H2V2h20v20zM4 20h16V4H4v16zm14-6v4h-4v-2h2v-2h2zm-2-8v4h-4V8h2V6h2z'/%3E%3C/svg%3E") no-repeat center;
  mask-size: contain;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .dashboard-container {
    min-height: calc(100vh - 160px);
  }

  .dashboard-grid .react-grid-item > .react-resizable-handle {
    width: 20px;
    height: 20px;
    opacity: 1; /* Always visible on mobile */
  }

  .dashboard-grid .react-grid-item {
    transition: none; /* Disable hover effects on mobile */
  }

  .dashboard-grid .react-grid-item:hover {
    transform: none;
    box-shadow: none;
  }
}

/* Custom scrollbar for dashboard */
.dashboard-container::-webkit-scrollbar {
  width: 8px;
}

.dashboard-container::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3);
  border-radius: 4px;
}

.dashboard-container::-webkit-scrollbar-thumb {
  background: hsl(var(--border));
  border-radius: 4px;
  transition: background 200ms ease;
}

.dashboard-container::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--border) / 0.8);
}
