import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Table } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { usePurchaseHistory } from '../../hooks/usePurchaseHistory';
import { PurchaseHistoryItem, PurchaseHistoryQueryParams } from '../../types/purchase-history.types';
import { TableColumn } from '@/shared/components/common/Table/types';

/**
 * Component hiển thị lịch sử mua hàng
 */
const PurchaseHistorySection: React.FC = () => {
  const { t } = useTranslation(['common', 'admin']);

  // Cấu hình columns cho table
  const columns = useMemo<TableColumn<PurchaseHistoryItem>[]>(() => [
    {
      title: t('admin:profile.purchaseHistory.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      sortable: true,
    },
    {
      title: t('admin:profile.purchaseHistory.date', 'Ngày mua'),
      dataIndex: 'purchaseDate',
      key: 'purchaseDate',
      sortable: true,
    },
    {
      title: t('admin:profile.purchaseHistory.amount', 'Số tiền'),
      dataIndex: 'amount',
      key: 'amount',
      sortable: true,
      render: (value: unknown) => `${(value as number).toLocaleString()} VND`,
    },
    {
      title: t('admin:profile.purchaseHistory.points', 'Điểm'),
      dataIndex: 'points',
      key: 'points',
      sortable: true,
      render: (value: unknown) => (value as number).toLocaleString(),
    },
    {
      title: t('admin:profile.purchaseHistory.trend', 'Xu hướng'),
      dataIndex: 'trend',
      key: 'trend',
      render: (value: unknown) => (
        <span className={(value as string) === 'up' ? 'text-green-600' : 'text-red-600'}>
          {(value as string) === 'up' ? '↑' : '↓'}
        </span>
      ),
    },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig<PurchaseHistoryItem, PurchaseHistoryQueryParams>({
    columns,
    createQueryParams: (params) => {
      const queryParams: PurchaseHistoryQueryParams = {
        page: params.page,
        pageSize: params.pageSize,
      };

      if (params.searchTerm) {
        queryParams.search = params.searchTerm;
      }

      if (params.sortBy) {
        queryParams.sortBy = params.sortBy;
      }

      if (params.sortDirection) {
        queryParams.sortOrder = params.sortDirection.toLowerCase() as 'asc' | 'desc';
      }

      return queryParams;
    },
  }));

  const { data, isLoading } = usePurchaseHistory(dataTable.queryParams as PurchaseHistoryQueryParams);

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('admin:profile.purchaseHistory.title', 'Lịch sử mua hàng')}
        </Typography>
        
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={data?.items || []}
          loading={isLoading}
        />
      </div>
    </Card>
  );
};

export default PurchaseHistorySection;
