import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Form,
  FormItem,
  Input,
  Button,
  PasswordInput,
  PhoneInputWithCountry,
} from '@/shared/components/common';
import RecaptchaModal from './RecaptchaModal';
import { FieldValues } from 'react-hook-form';
import type { FormRef } from '@/shared/components/common/Form/Form';

import { createRegisterSchema, RegisterFormValues } from '../schemas/auth.schema';
import { useAuthCommon } from '@/shared/hooks';
import { useRegister } from '../hooks/useAuthQuery';
import { useFormErrors } from '@/shared/hooks';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { RegisterResponse } from '../types/auth.types';

interface RegisterFormProps {
  onSuccess?: () => void;
}

/**
 * Register form component
 */
const RegisterForm: React.FC<RegisterFormProps> = ({ onSuccess }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { saveVerifyInfo } = useAuthCommon();
  const { mutate: register, isPending } = useRegister();
  const { formRef, setFormErrors } = useFormErrors<RegisterFormValues>();

  // State cho RecaptchaModal
  const [isRecaptchaModalOpen, setIsRecaptchaModalOpen] = useState(false);
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);

  // Xử lý khi reCAPTCHA thành công
  const handleRecaptchaSuccess = (token: string) => {
    setRecaptchaToken(token);
    setIsRecaptchaModalOpen(false);
  };

  // Xử lý lỗi reCAPTCHA
  const handleRecaptchaError = (error: string) => {
    console.error('reCAPTCHA error:', error);
  };

  // Theo dõi xem reCAPTCHA đã được xác thực chưa
  useEffect(() => {
    if (recaptchaToken) {
      // Tự động submit form sau khi có reCAPTCHA token
      formRef.current?.submit();
    }
  }, [recaptchaToken, formRef]);

  // Create register schema with translations
  const registerSchema = createRegisterSchema(t);

  // Function để thực hiện đăng ký
  const submitRegistration = (registerValues: RegisterFormValues, token: string) => {
    console.log('Phone number processing:', {
      phone: registerValues.phone,
    });

    // Call register API using the mutate function from useRegister hook
    register(
      {
        email: registerValues.email,
        password: registerValues.password,
        fullName: registerValues.fullName,
        phone: registerValues.phone || '', // Số điện thoại quốc tế đầy đủ (ví dụ: +84987654321)
        recaptchaToken: token,
      },
      {
        onSuccess: (response: ApiResponseDto<RegisterResponse>) => {
          // Reset states sau khi đăng ký thành công
          setRecaptchaToken(null);

          // Xử lý các mã phản hồi khác nhau
          if (response.code === 200) {
            // Đăng ký thành công
            if (onSuccess) {
              onSuccess();
            }

            // Lưu thông tin xác thực vào Redux
            if (response.result) {
              saveVerifyInfo({
                verifyToken: response.result.otpToken,
                expiresAt: response.result.expiresAt,
                info: response.result.info,
              });

              // Log để debug
              console.log('Đăng ký thành công, lưu thông tin xác thực:', {
                otpToken: response.result.otpToken,
                expiresAt: response.result.expiresAt,
                formattedExpiresAt: new Date(response.result.expiresAt).toLocaleString(),
              });
            }

            // Chuyển hướng đến trang xác thực email
            navigate('/auth/verify-email');
          }
        },
        onError: (error: unknown) => {
          console.error('Register error:', error);

          // Reset states sau khi đăng ký thất bại
          setRecaptchaToken(null);

          // Lấy thông báo lỗi từ response API
          const errorMsg = t('auth.registerError', 'Đăng ký thất bại');

          // Kiểm tra xem error có phải là AxiosError không
          if (error && typeof error === 'object' && 'response' in error && error.response) {
            const axiosError = error as {
              response: {
                data?: {
                  message?: string;
                  code?: number;
                  path?: string;
                  requestId?: string;
                };
              };
            };

            // Kiểm tra mã lỗi 10008 (Email đã được sử dụng)
            if (axiosError.response.data?.code === 10008) {
              // Hiển thị lỗi dưới trường email
              setFormErrors({
                email: t('auth.emailAlreadyUsed', 'Email đã được sử dụng'),
              });
            }
            // Kiểm tra mã lỗi 10009 (Số điện thoại đã được sử dụng)
            else if (axiosError.response.data?.code === 10009) {
              // Hiển thị lỗi dưới trường phone
              setFormErrors({
                phone: t('auth.phoneAlreadyUsed', 'Số điện thoại đã được sử dụng'),
              });
            }
            // Xử lý các lỗi khác bằng cách hiển thị lỗi dưới trường phù hợp
            else if (axiosError.response.data?.message) {
              // Đặt lỗi chung cho trường email (có thể thay đổi thành trường khác nếu cần)
              setFormErrors({
                email: axiosError.response.data.message,
              });
            }
          } else {
            // Hiển thị lỗi chung dưới trường email
            setFormErrors({
              email: errorMsg,
            });
          }
        },
      }
    );
  };

  // Handle form submission
  const handleSubmit = (values: unknown) => {
    // Use type assertion with a specific type instead of 'any'
    const registerValues = values as RegisterFormValues;

    // Reset form errors
    setFormErrors({});

    // Kiểm tra xem reCAPTCHA đã được xác thực chưa
    if (!recaptchaToken) {
      // Hiển thị modal reCAPTCHA nếu chưa có token
      setIsRecaptchaModalOpen(true);
      return;
    }

    // Nếu đã có token, submit ngay
    submitRegistration(registerValues, recaptchaToken);
    // Reset token sau khi sử dụng
    setRecaptchaToken(null);
  };

  // Không cần defaultValues nữa vì PhoneInputWithCountry tự quản lý

  return (
    <Form
      ref={formRef as unknown as React.RefObject<FormRef<FieldValues>>}
      schema={registerSchema}
      onSubmit={handleSubmit}
      className="space-y-4"
      autoComplete="off"
    >
      <FormItem name="fullName" label={t('auth.fullName')} required>
        <Input fullWidth autoComplete="off" />
      </FormItem>

      <FormItem name="email" label={t('auth.email')} required>
        <Input type="email" fullWidth autoComplete="off" />
      </FormItem>

      <FormItem name="phone" label={t('auth.phone')} required>
        <PhoneInputWithCountry
          placeholder={t('auth.phonePlaceholder', 'Nhập số điện thoại')}
          fullWidth
          defaultCountry="VN"
          autoComplete="off"
        />
      </FormItem>

      <FormItem name="password" label={t('auth.password')} required>
        <PasswordInput fullWidth autoComplete="off" />
      </FormItem>

      <Button type="submit" variant="primary" fullWidth isLoading={isPending}>
        {t('auth.signUp')}
      </Button>

      {/* RecaptchaModal */}
      <RecaptchaModal
        isOpen={isRecaptchaModalOpen}
        onClose={() => setIsRecaptchaModalOpen(false)}
        onSuccess={handleRecaptchaSuccess}
        onError={handleRecaptchaError}
      />
    </Form>
  );
};

export default RegisterForm;
