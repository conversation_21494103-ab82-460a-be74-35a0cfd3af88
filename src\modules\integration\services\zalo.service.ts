import { apiClient } from '@/shared/api';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Zalo OAuth Response DTO
 */
export interface ZaloOAuthUrlResponseDto {
  oauthUrl: string;
  state: string;
}

/**
 * Zalo OAuth Request DTO
 */
export interface ZaloOAuthUrlRequestDto {
  redirectUri: string;
}

/**
 * Zalo OAuth Callback Request DTO
 */
export interface ZaloOAuthCallbackRequestDto {
  code: string;
  state: string;
}

/**
 * Service cho Zalo Integration APIs
 */
export class ZaloIntegrationService {
  private static readonly BASE_URL = '/integration/zalo';

  /**
   * Lấy URL OAuth Zalo v4
   */
  static async getOAuthUrl(redirectUri: string): Promise<ApiResponseDto<ZaloOAuthUrlResponseDto>> {
    return apiClient.get(`${this.BASE_URL}/oauth-url`, {
      params: { redirectUri }
    });
  }

  /**
   * <PERSON><PERSON> lý callback OAuth Zalo
   */
  static async handleOAuthCallback(data: ZaloOAuthCallbackRequestDto): Promise<ApiResponseDto<{ success: boolean; message?: string }>> {
    return apiClient.post(`${this.BASE_URL}/oa/callback`, data);
  }
}

export default ZaloIntegrationService;
