/**
 * Types for segment API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';

/**
 * Segment status enum
 */
export enum SegmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Condition operator enum
 */
export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  IN = 'in',
  NOT_IN = 'not_in',
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
}

/**
 * Segment condition
 */
export interface SegmentCondition {
  id: string;
  field: string;
  operator: ConditionOperator;
  value: string | string[] | number | number[];
}

/**
 * Segment group
 */
export interface SegmentGroup {
  id: string;
  conditions: SegmentCondition[];
  logicalOperator: 'AND' | 'OR';
}

/**
 * Segment entity - cập nhật theo SegmentResponseDto từ API
 */
export interface Segment {
  id: number;
  name: string;
  description: string;
  criteria: SegmentCriteria;
  audienceCount: number;
  createdAt: number;
  updatedAt: number;
}

/**
 * Segment criteria structure for API requests (without IDs)
 */
export interface SegmentCriteriaRequest {
  groups: {
    logicalOperator: 'AND' | 'OR';
    conditions: Omit<SegmentCondition, 'id'>[];
  }[];
}

/**
 * Segment criteria structure for responses (with IDs)
 */
export interface SegmentCriteria {
  groups: Omit<SegmentGroup, 'id'>[];
}

/**
 * Create segment request - API yêu cầu name, description (optional), và criteria
 */
export interface CreateSegmentRequest {
  name: string;
  description?: string;
  criteria: SegmentCriteriaRequest;
}

/**
 * Update segment request - API chấp nhận name, description, và criteria
 */
export interface UpdateSegmentRequest {
  name?: string;
  description?: string;
  criteria?: SegmentCriteriaRequest;
}

/**
 * Segment stats
 */
export interface SegmentStats {
  segmentId: string;
  segmentName: string;
  totalAudiences: number;
  percentageOfTotal: number;
  updatedAt: number;
  // Legacy fields for backward compatibility
  totalContacts?: number;
  contactsInSegment?: number;
  percentage?: number;
  recentChanges?: {
    date: string;
    count: number;
  }[];
}

/**
 * Segment response
 */
export type SegmentResponse = Segment;

/**
 * Segment list response
 */
export type SegmentListResponse = ApiResponseDto<PaginatedResult<SegmentResponse>>;

/**
 * Segment detail response
 */
export type SegmentDetailResponse = ApiResponseDto<SegmentResponse>;

/**
 * Segment stats response
 */
export type SegmentStatsResponse = ApiResponseDto<SegmentStats>;

/**
 * Segment query params
 */
export interface SegmentQueryParams {
  search?: string;
  status?: SegmentStatus;
  audienceId?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}

/**
 * Helper function để transform Segment từ API thành SegmentFormValues
 */
export const transformSegmentToFormValues = (segment: Segment): { name: string; description?: string; groups: SegmentGroup[] } => {
  return {
    name: segment.name,
    description: segment.description,
    groups: segment.criteria.groups.map((group, index) => ({
      id: (group as { id?: string }).id || `group-${Date.now()}-${index}`,
      logicalOperator: group.logicalOperator,
      conditions: group.conditions.map((condition, condIndex) => ({
        id: (condition as { id?: string }).id || `condition-${Date.now()}-${condIndex}`,
        field: condition.field,
        operator: condition.operator,
        value: condition.value,
      })),
    })),
  };
};
