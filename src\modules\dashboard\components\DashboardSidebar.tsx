import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Typography, Icon } from '@/shared/components/common';
import { MenuItem } from '../types';
import { DASHBOARD_MENU_SECTIONS } from '../constants/menu-data';

interface DashboardNavbarProps {
  onMenuItemClick?: (menuItem: MenuItem) => void;
  className?: string;
}

interface NavSectionProps {
  section: { id: string; title: string; items: MenuItem[] };
  isActive: boolean;
  onItemClick: (item: MenuItem) => void;
}

const NavSectionComponent: React.FC<NavSectionProps> = ({
  section,
  isActive,
  onItemClick
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const containerRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;

      // Check if click is outside both button and dropdown
      const isOutsideButton = buttonRef.current && !buttonRef.current.contains(target);
      const isOutsideDropdown = dropdownRef.current && !dropdownRef.current.contains(target);

      if (isOutsideButton && isOutsideDropdown) {
        setIsOpen(false);
        setExpandedItems(new Set()); // Reset expanded items when closing
      }
    };

    const handleResize = () => {
      if (isOpen && buttonRef.current) {
        const rect = buttonRef.current.getBoundingClientRect();
        setDropdownPosition({
          top: rect.bottom + 8,
          left: rect.right // Align dropdown's right edge with button's right edge
        });
      }
    };

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsOpen(false);
        setExpandedItems(new Set()); // Reset expanded items when closing
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleKeyDown);
      window.addEventListener('resize', handleResize);
      window.addEventListener('scroll', handleResize);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('scroll', handleResize);
    };
  }, [isOpen]);

  const getSectionIcon = (sectionId: string) => {
    switch (sectionId) {
      case 'business': return 'trending-up';
      case 'integration': return 'link';
      case 'marketing': return 'megaphone';
      case 'marketplace': return 'shopping-bag';
      case 'data': return 'database';
      default: return 'folder';
    }
  };

  const handleToggle = () => {
    if (!isOpen && buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      setDropdownPosition({
        top: rect.bottom + 8,
        left: rect.right // Align dropdown's right edge with button's right edge
      });
    } else if (isOpen) {
      // Reset expanded items when closing dropdown
      setExpandedItems(new Set());
    }
    setIsOpen(!isOpen);
  };

  const handleItemClick = (item: MenuItem) => {
    onItemClick(item);
    setIsOpen(false); // Close dropdown after selection
  };

  const toggleItemExpansion = (itemId: string) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  };

  const handleItemWithChildrenClick = (item: MenuItem, event: React.MouseEvent) => {
    event.stopPropagation();
    if (item.children && item.children.length > 0) {
      toggleItemExpansion(item.id);
    } else {
      handleItemClick(item);
    }
  };

  return (
    <div ref={containerRef} className="relative group">
      <button
        ref={buttonRef}
        onClick={handleToggle}
        className={`
          flex items-center space-x-2 px-4 py-2.5
          hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5
          transition-all duration-300 rounded-xl
          group relative overflow-hidden backdrop-blur-sm
          border border-transparent hover:border-primary/20
          ${isActive
            ? 'bg-gradient-to-r from-primary/15 to-primary/10 text-primary shadow-md border-primary/30'
            : 'text-foreground/70 hover:text-foreground hover:shadow-sm'
          }
        `}
      >
        {/* Background glow effect */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl" />

        <Icon
          name={getSectionIcon(section.id)}
          className={`w-4 h-4 flex-shrink-0 group-hover:scale-110 transition-all duration-300 relative z-10
            ${isActive ? 'text-primary' : 'group-hover:text-primary/80'}
          `}
        />
        <Typography
          variant="body2"
          className="font-medium text-sm whitespace-nowrap relative z-10 group-hover:font-semibold transition-all duration-300"
        >
          {section.title}
        </Typography>
        <Icon
          name="chevron-down"
          className={`w-3 h-3 flex-shrink-0 transition-all duration-300 relative z-10
            ${isOpen ? 'rotate-180' : 'rotate-0'}
            ${isActive ? 'text-primary' : 'group-hover:text-primary/80'}
          `}
        />
      </button>

      {/* Dropdown Menu using Portal */}
      {isOpen && createPortal(
        <div
          ref={dropdownRef}
          className="fixed min-w-[240px] max-w-[280px] bg-card backdrop-blur-xl border border-border rounded-2xl shadow-2xl py-2"
          style={{
            zIndex: 99999,
            top: dropdownPosition.top,
            left: dropdownPosition.left,
            transform: 'translateX(-100%)' // Align right edge of dropdown with button's right edge
          }}
          onClick={(e) => e.stopPropagation()} // Prevent event bubbling
        >
          {/* Dropdown arrow - positioned near right edge */}
          <div className="absolute -top-1 right-6 w-2 h-2 bg-card border-l border-t border-border rotate-45" />

          {section.items.map((item, index) => (
            <div key={item.id}>
              <button
                onClick={(e) => handleItemWithChildrenClick(item, e)}
                className={`
                  w-full flex items-center space-x-3 px-3 py-2 text-left
                  hover:bg-gradient-to-r hover:from-primary/10 hover:to-primary/5
                  transition-all duration-200 group/item relative overflow-hidden
                `}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {/* Item background glow */}
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover/item:opacity-100 transition-opacity duration-200" />

                <div className="w-7 h-7 rounded-lg bg-primary/10 flex items-center justify-center group-hover/item:bg-primary/20 transition-colors duration-200">
                  <Icon
                    name={item.icon}
                    className="w-4 h-4 flex-shrink-0 text-primary/70 group-hover/item:text-primary transition-colors duration-200"
                  />
                </div>
                <div className="flex-1">
                  <Typography
                    variant="body2"
                    className="text-sm font-medium group-hover/item:text-primary transition-colors duration-200 truncate"
                  >
                    {item.title}
                  </Typography>
                </div>
                {item.children && item.children.length > 0 && (
                  <Icon
                    name="chevron-right"
                    className={`w-3 h-3 text-muted-foreground/50 group-hover/item:text-primary/70 transition-all duration-200 ${
                      expandedItems.has(item.id) ? 'rotate-90' : 'rotate-0'
                    }`}
                  />
                )}
              </button>

              {/* Sub-items for items with children - only show when expanded */}
              {item.children && item.children.length > 0 && expandedItems.has(item.id) && (
                <div className="ml-4 pl-4 border-l border-border/20 animate-in slide-in-from-top-2 duration-200">
                  {item.children.map((child, childIndex) => (
                    <button
                      key={child.id}
                      onClick={() => handleItemClick(child)}
                      className="w-full flex items-center space-x-2 px-3 py-1.5 text-left hover:bg-gradient-to-r hover:from-primary/5 hover:to-primary/3 transition-all duration-200 group/child relative overflow-hidden rounded-lg"
                      style={{ animationDelay: `${(index + childIndex + 1) * 50}ms` }}
                    >
                      <div className="w-6 h-6 rounded-md bg-primary/5 flex items-center justify-center group-hover/child:bg-primary/15 transition-colors duration-200">
                        <Icon
                          name={child.icon}
                          className="w-3 h-3 flex-shrink-0 text-primary/60 group-hover/child:text-primary transition-all duration-200"
                        />
                      </div>
                      <Typography
                        variant="body2"
                        className="text-xs font-medium text-muted-foreground group-hover/child:text-primary transition-colors duration-200"
                      >
                        {child.title}
                      </Typography>
                    </button>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>,
        document.body
      )}
    </div>
  );
};

const DashboardNavbar: React.FC<DashboardNavbarProps> = ({
  onMenuItemClick,
  className = ''
}) => {

  const handleMenuItemClick = (item: MenuItem) => {
    // Only call the callback to add widget, don't navigate
    onMenuItemClick?.(item);
  };

  const isSectionActive = (): boolean => {
    // Since we're not navigating, we can remove this logic or keep it simple
    // For now, we'll just return false to keep sections in default state
    return false;
  };

  return (
    <div className={`
      bg-card border-b border-border shadow-lg backdrop-blur-sm
      relative overflow-visible
      ${className}
    `} style={{ zIndex: 1000 }}>
      <div className="flex items-center justify-between px-4 py-3">
        {/* Logo/Brand */}
        <div className="flex items-center space-x-3">
          <Typography variant="h6" className="font-semibold text-foreground">
            Dashboard
          </Typography>
        </div>

        {/* Navigation Menu */}
        <div className="flex items-center space-x-1">
          {DASHBOARD_MENU_SECTIONS.map(section => (
            <NavSectionComponent
              key={section.id}
              section={section}
              isActive={isSectionActive()}
              onItemClick={handleMenuItemClick}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

// Keep the same export name for compatibility
const DashboardSidebar = DashboardNavbar;

export default DashboardSidebar;
