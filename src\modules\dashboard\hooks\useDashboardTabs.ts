import { useState, useCallback } from 'react';
import { DashboardTab, DashboardTabsState, DashboardWidget } from '../types';

const STORAGE_KEY = 'dashboard-tabs-state';

const createDefaultTab = (): DashboardTab => ({
  id: `tab-${Date.now()}`,
  name: 'Trang chính',
  widgets: [],
  mode: 'edit',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

const createNewTab = (name?: string): DashboardTab => ({
  id: `tab-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  name: name || `Trang ${Date.now().toString().slice(-4)}`,
  widgets: [],
  mode: 'edit',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
});

export const useDashboardTabs = () => {
  const [tabsState, setTabsState] = useState<DashboardTabsState>(() => {
    // Load from localStorage on init
    try {
      const saved = localStorage.getItem(STORAGE_KEY);
      if (saved) {
        const parsed = JSON.parse(saved) as DashboardTabsState;
        // Validate that currentTabId exists in tabs
        if (parsed.tabs.length > 0 && parsed.tabs.some(tab => tab.id === parsed.currentTabId)) {
          return parsed;
        }
      }
    } catch (error) {
      console.warn('Failed to load dashboard tabs from localStorage:', error);
    }
    
    // Default state
    const defaultTab = createDefaultTab();
    return {
      currentTabId: defaultTab.id,
      tabs: [defaultTab],
    };
  });

  const currentTab = tabsState.tabs.find(tab => tab.id === tabsState.currentTabId);

  // Save to localStorage
  const saveToStorage = useCallback(() => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(tabsState));
    } catch (error) {
      console.error('Failed to save dashboard tabs to localStorage:', error);
    }
  }, [tabsState]);

  // Switch to a tab
  const switchToTab = useCallback((tabId: string) => {
    setTabsState(prev => ({
      ...prev,
      currentTabId: tabId,
    }));
  }, []);

  // Create new tab
  const createTab = useCallback((name?: string) => {
    const newTab = createNewTab(name);
    setTabsState(prev => ({
      currentTabId: newTab.id,
      tabs: [...prev.tabs, newTab],
    }));
  }, []);

  // Rename tab
  const renameTab = useCallback((tabId: string, newName: string) => {
    setTabsState(prev => ({
      ...prev,
      tabs: prev.tabs.map(tab =>
        tab.id === tabId
          ? { ...tab, name: newName, updatedAt: new Date().toISOString() }
          : tab
      ),
    }));
  }, []);

  // Delete tab
  const deleteTab = useCallback((tabId: string) => {
    setTabsState(prev => {
      const newTabs = prev.tabs.filter(tab => tab.id !== tabId);
      
      // Don't allow deleting the last tab
      if (newTabs.length === 0) {
        return prev;
      }

      // If deleting current tab, switch to first available tab
      const newCurrentTabId = prev.currentTabId === tabId
        ? newTabs[0]?.id || prev.currentTabId
        : prev.currentTabId;

      return {
        currentTabId: newCurrentTabId,
        tabs: newTabs,
      };
    });
  }, []);

  // Reorder tabs
  const reorderTabs = useCallback((fromIndex: number, toIndex: number) => {
    setTabsState(prev => {
      const newTabs = [...prev.tabs];

      // Validate indices
      if (fromIndex < 0 || fromIndex >= newTabs.length || toIndex < 0 || toIndex >= newTabs.length) {
        return prev; // Return unchanged state if indices are invalid
      }

      const [movedTab] = newTabs.splice(fromIndex, 1);

      // Check if movedTab exists (should always be true with valid indices, but for type safety)
      if (movedTab) {
        newTabs.splice(toIndex, 0, movedTab);
      }

      return {
        ...prev,
        tabs: newTabs,
      };
    });
  }, []);

  // Change tab mode
  const changeTabMode = useCallback((mode: 'view' | 'edit') => {
    setTabsState(prev => ({
      ...prev,
      tabs: prev.tabs.map(tab =>
        tab.id === prev.currentTabId
          ? { ...tab, mode, updatedAt: new Date().toISOString() }
          : tab
      ),
    }));
  }, []);

  // Update current tab widgets
  const updateTabWidgets = useCallback((widgets: DashboardWidget[]) => {
    setTabsState(prev => ({
      ...prev,
      tabs: prev.tabs.map(tab =>
        tab.id === prev.currentTabId
          ? { ...tab, widgets, updatedAt: new Date().toISOString() }
          : tab
      ),
    }));
  }, []);

  // Add widget to current tab
  const addWidgetToCurrentTab = useCallback((widget: DashboardWidget) => {
    setTabsState(prev => ({
      ...prev,
      tabs: prev.tabs.map(tab =>
        tab.id === prev.currentTabId
          ? { 
              ...tab, 
              widgets: [...tab.widgets, widget], 
              updatedAt: new Date().toISOString() 
            }
          : tab
      ),
    }));
  }, []);

  // Remove widget from current tab
  const removeWidgetFromCurrentTab = useCallback((widgetId: string) => {
    setTabsState(prev => ({
      ...prev,
      tabs: prev.tabs.map(tab =>
        tab.id === prev.currentTabId
          ? { 
              ...tab, 
              widgets: tab.widgets.filter(w => w.id !== widgetId), 
              updatedAt: new Date().toISOString() 
            }
          : tab
      ),
    }));
  }, []);

  return {
    // State
    tabsState,
    currentTab,
    
    // Actions
    switchToTab,
    createTab,
    renameTab,
    deleteTab,
    reorderTabs,
    changeTabMode,
    updateTabWidgets,
    addWidgetToCurrentTab,
    removeWidgetFromCurrentTab,
    saveToStorage,
  };
};
