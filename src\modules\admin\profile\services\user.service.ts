/**
 * Mock services for admin profile user management
 * In a real application, these would make actual API calls
 */

// Mock user data
const mockUser = {
  id: 1,
  fullName: '<PERSON>uy<PERSON><PERSON>ăn Admin',
  email: '<EMAIL>',
  phoneNumber: '**********',
  gender: 'MALE',
  dateOfBirth: '1990-01-01',
  address: '123 Đường ABC, Quận 1, TP.HCM',
  isVerifyEmail: true,
  isVerifyPhone: true,
  avatar: null,
  coverImage: null,
};

const mockBusinessInfo = {
  companyName: 'Công ty TNHH ABC',
  taxCode: '**********',
  businessAddress: '456 Đường XYZ, Quận 2, TP.HCM',
  businessPhone: '**********',
  businessEmail: '<EMAIL>',
};

const mockBankInfo = {
  bankName: 'Vietcombank',
  accountNumber: '**********',
  accountHolderName: 'NGUYEN VAN ADMIN',
  branchName: '<PERSON> nh<PERSON>h Quận 1',
};

/**
 * <PERSON><PERSON><PERSON> thông tin người dùng hiện tại
 */
export const getCurrentUser = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockUser;
};

/**
 * Cập nhật thông tin cá nhân
 */
export const updatePersonalInfo = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock update personal info:', data);
  return { ...mockUser, ...data };
};

/**
 * Lấy thông tin doanh nghiệp
 */
export const getBusinessInfo = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockBusinessInfo;
};

/**
 * Cập nhật thông tin doanh nghiệp
 */
export const updateBusinessInfo = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock update business info:', data);
  return { ...mockBusinessInfo, ...data };
};

/**
 * Lấy thông tin ngân hàng
 */
export const getBankInfo = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return mockBankInfo;
};

/**
 * Cập nhật thông tin ngân hàng
 */
export const updateBankInfo = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock update bank info:', data);
  return { ...mockBankInfo, ...data };
};

/**
 * Tạo URL upload avatar
 */
export const createAvatarUploadUrl = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock create avatar upload URL:', data);
  return {
    uploadUrl: 'https://example.com/upload',
    fileId: 'mock-file-id',
  };
};

/**
 * Cập nhật avatar
 */
export const updateAvatar = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock update avatar:', data);
  return { success: true };
};

/**
 * Lấy trạng thái xác thực 2 yếu tố
 */
export const getTwoFactorAuthStatus = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    smsEnabled: false,
    emailEnabled: true,
    googleAuthEnabled: false,
  };
};

/**
 * Bật/tắt xác thực SMS
 */
export const toggleSmsAuth = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock toggle SMS auth:', data);
  return { success: true };
};

/**
 * Bật/tắt xác thực Email
 */
export const toggleEmailAuth = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock toggle email auth:', data);
  return { success: true };
};

/**
 * Thiết lập Google Authenticator
 */
export const setupGoogleAuth = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    qrCode: 'data:image/png;base64,mock-qr-code',
    secret: 'MOCK-SECRET-KEY',
  };
};

/**
 * Xác minh Google Authenticator
 */
export const verifyGoogleAuth = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock verify Google auth:', data);
  return { success: true };
};

/**
 * Lấy cài đặt thông báo
 */
export const getNotificationSettings = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingEmails: false,
    securityAlerts: true,
  };
};

/**
 * Cập nhật cài đặt thông báo
 */
export const updateNotificationSettings = async (data: Record<string, unknown>) => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  console.log('Mock update notification settings:', data);
  return { success: true };
};

/**
 * Lấy điểm người dùng
 */
export const getUserPoints = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return {
    totalPoints: 10000,
    availablePoints: 8500,
    usedPoints: 1500,
  };
};

/**
 * Lấy danh sách ngân hàng
 */
export const getBanks = async () => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));
  return [
    { id: 1, name: 'Vietcombank', code: 'VCB' },
    { id: 2, name: 'Techcombank', code: 'TCB' },
    { id: 3, name: 'BIDV', code: 'BIDV' },
  ];
};
