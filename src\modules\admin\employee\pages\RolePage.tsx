/* eslint-disable react-hooks/exhaustive-deps */
/**
 * Trang quản lý vai trò
 */
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Card, Table, ActionMenu } from '@/shared/components/common';
import { TableColumn } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import useNotification from '@/shared/hooks/common/useNotification';
import NotificationContainer from '@/shared/components/layout/chat-panel/NotificationContainer';
import { RoleDto, RoleQueryDto, RoleStatus } from '../types/role.types';
import { useAllRoles } from '../hooks/useRoleQuery';
import AssignPermissionsForm from '../components/forms/AssignPermissionsForm';

/**
 * Trang quản lý vai trò
 */
const RolePage: React.FC = () => {
  const { t } = useTranslation(['employee', 'common']);
  const navigate = useNavigate();
  const { notifications, removeNotification } = useNotification();

  // State cho query và dữ liệu
  const [query, setQuery] = useState<RoleQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    status: undefined,
  });

  // State lưu trữ dữ liệu vai trò
  const [roles, setRoles] = useState<RoleDto[]>([]);
  const [totalItems, setTotalItems] = useState(0);

  // State cho form gán quyền
  const [selectedRole, setSelectedRole] = useState<RoleDto | null>(null);

  // Sử dụng hook animation cho form
  const { isVisible, hideForm } = useSlideForm();
  const {
    isVisible: isPermissionFormVisible,
    showForm: showPermissionForm,
    hideForm: hidePermissionForm,
  } = useSlideForm();

  // Queries
  const rolesQuery = useAllRoles();

  // Effect to update state when API data is available
  useEffect(() => {
    console.log('Component mounted or query changed:', query);
    console.log('Roles query state:', rolesQuery);

    // Update state when API data is available
    if (rolesQuery.data) {
      // Xử lý phân trang và filter ở frontend vì API chưa hỗ trợ
      let filteredRoles = rolesQuery.data;

      // Filter theo search
      if (query.search) {
        filteredRoles = filteredRoles.filter(
          role =>
            role.name.toLowerCase().includes(query.search!.toLowerCase()) ||
            (role.description &&
              role.description.toLowerCase().includes(query.search!.toLowerCase()))
        );
      }

      // Filter theo status
      if (query.status) {
        filteredRoles = filteredRoles.filter(role => role.status === query.status);
      }

      // Phân trang
      const startIndex = (query.page - 1) * query.limit;
      const endIndex = startIndex + query.limit;
      const paginatedRoles = filteredRoles.slice(startIndex, endIndex);

      setRoles(paginatedRoles);
      setTotalItems(filteredRoles.length);
    }
  }, [query, rolesQuery.data]);

  // Xử lý tìm kiếm
  const handleSearch = (search: string) => {
    setQuery(prev => ({ ...prev, search, page: 1 }));
  };

  // Xử lý lọc theo trạng thái
  const handleFilterStatus = (status: RoleStatus | undefined) => {
    setQuery(prev => ({ ...prev, status, page: 1 }));
  };

  // Xử lý phân trang
  const handlePageChange = (page: number) => {
    setQuery(prev => ({ ...prev, page }));
  };

  // Xử lý chỉnh sửa vai trò
  const handleEdit = (role: RoleDto) => {
    navigate(`/admin/employees/roles/edit/${role.id}`);
  };

  // Xử lý khi form tạo vai trò thành công
  const handleFormSuccess = () => {
    // Đóng form
    hideForm();

    // Refresh danh sách vai trò
    void rolesQuery.refetch();
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideForm();
  };

  // Xử lý gán quyền cho vai trò
  const handleAssignPermissions = (role: RoleDto) => {
    setSelectedRole(role);
    showPermissionForm();
  };

  // Xử lý khi form gán quyền thành công
  const handlePermissionFormSuccess = () => {
    hidePermissionForm();
    setSelectedRole(null);
    void rolesQuery.refetch();
  };

  // Xử lý hủy form gán quyền
  const handlePermissionFormCancel = () => {
    hidePermissionForm();
    setSelectedRole(null);
  };

  // Columns cho bảng
  const columns: TableColumn<RoleDto>[] = [
    { key: 'id', title: 'ID', dataIndex: 'id', width: '5%' },
    {
      key: 'name',
      title: t('employee:role.name'),
      dataIndex: 'name',
      width: '20%',
    },
    {
      key: 'description',
      title: t('employee:role.description'),
      dataIndex: 'description',
      width: '30%',
      render: (value: unknown) => (typeof value === 'string' ? value : '-'),
    },
    {
      key: 'status',
      title: t('employee:role.status'),
      dataIndex: 'status',
      width: '10%',
      render: (_: unknown, record: RoleDto) => {
        const status = record.status || RoleStatus.ACTIVE; // Default to ACTIVE if not provided
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              status === RoleStatus.ACTIVE
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
            }`}
          >
            {status === RoleStatus.ACTIVE ? t('common:active') : t('common:inactive')}
          </div>
        );
      },
    },
    {
      key: 'type',
      title: t('employee:role.type'),
      dataIndex: 'isSystem',
      width: '10%',
      render: (_: unknown, record: RoleDto) => {
        const isSystem = record.isSystem || false; // Default to false if not provided
        return (
          <div
            className={`px-2 py-1 rounded-full text-center text-xs font-medium ${
              isSystem
                ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
                : 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
            }`}
          >
            {isSystem ? t('employee:role.system') : t('employee:role.custom')}
          </div>
        );
      },
    },
    {
      key: 'createdAt',
      title: t('common:createdAt'),
      dataIndex: 'createdAt',
      width: '10%',
      render: (_: unknown, record: RoleDto) => {
        if (!record.createdAt) return '-';
        return new Date(record.createdAt).toLocaleDateString('vi-VN');
      },
    },
    {
      key: 'actions',
      title: t('common:actions'),
      width: '15%',
      render: (_: unknown, record: RoleDto) => {
        // Tạo danh sách các action items
        const actionItems = [
          {
            id: 'edit',
            label: t('common:edit'),
            icon: 'edit',
            onClick: () => handleEdit(record),
          },
          {
            id: 'permissions',
            label: t('employee:role.assignPermissions'),
            icon: 'shield',
            onClick: () => handleAssignPermissions(record),
          },
        ];

        return (
          <div className="flex justify-center">
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
              preferTop={true}
            />
          </div>
        );
      },
    },
  ];

  // Debug: Log data
  useEffect(() => {
    console.log('Roles state:', roles);
  }, [roles]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        items={[
          {
            id: 'all',
            label: t('common:all'),
            icon: 'list',
            onClick: () => handleFilterStatus(undefined),
          },
          {
            id: 'active',
            label: t('common:active'),
            icon: 'check',
            onClick: () => handleFilterStatus(RoleStatus.ACTIVE),
          },
          {
            id: 'inactive',
            label: t('common:inactive'),
            icon: 'eye-off',
            onClick: () => handleFilterStatus(RoleStatus.INACTIVE),
          },
        ]}
        columns={[]}
        showColumnFilter={false}
      />

      <SlideInForm isVisible={isVisible}>
        {/* TODO: Tạo RoleForm component */}
        <div className="p-4">
          <h3>Role Form - Coming Soon</h3>
          <button onClick={handleCancel}>Cancel</button>
          <button onClick={handleFormSuccess}>Save (Demo)</button>
        </div>
      </SlideInForm>

      <SlideInForm isVisible={isPermissionFormVisible}>
        {selectedRole && (
          <AssignPermissionsForm
            role={selectedRole}
            onSuccess={handlePermissionFormSuccess}
            onCancel={handlePermissionFormCancel}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={columns}
          data={roles}
          rowKey="id"
          pagination={{
            current: query.page || 1,
            pageSize: query.limit || 10,
            total: totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
          loading={rolesQuery.isLoading}
          sortable={true}
        />
      </Card>

      {/* Notification container */}
      <NotificationContainer notifications={notifications} onRemove={removeNotification} />
    </div>
  );
};

export default RolePage;
