import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Trang tổng quan quản lý User Dataset
 */
const UserDatasetManagementPage: React.FC = () => {
  const { t } = useTranslation(['user-dataset']);

  return (
    <PageWrapper>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Dataset Fine-tune Card */}
        <ModuleCard
          title={t('user-dataset:dataFineTune.title', 'Dataset Fine-tune')}
          description={t(
            'user-dataset:dataFineTune.description',
            'Quản lý dataset để huấn luyện và fine-tune các model AI.'
          )}
          icon="database"
          linkTo="/user-dataset/data-fine-tune"
        />
        <ModuleCard
          title={t('user-dataset:apiIntegration.title', 'API Integration')}
          description={t(
            'user-dataset:apiIntegration.description',
            'Quản lý các API key và model cho các nhà cung cấp AI.'
          )}
          icon="link"
          linkTo="/user-dataset/api-integration"
        />
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default UserDatasetManagementPage;
