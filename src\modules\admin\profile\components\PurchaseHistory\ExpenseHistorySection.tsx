import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Table } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { TableColumn } from '@/shared/components/common/Table/types';

// Type for expense history item
interface ExpenseHistoryItem {
  id: number;
  date: string;
  description: string;
  amount: number;
  category: string;
  status: string;
}

/**
 * Component hiển thị lịch sử chi tiêu
 */
const ExpenseHistorySection: React.FC = () => {
  const { t } = useTranslation(['common', 'admin']);

  // Mock data cho expense history
  const mockExpenseData = useMemo(() => Array.from({ length: 15 }, (_, index) => ({
    id: index + 1,
    date: '20/01/2023',
    description: `Chi tiêu ${index + 1}`,
    amount: 25000 + (index * 5000),
    category: index % 3 === 0 ? 'Marketing' : index % 3 === 1 ? 'Operations' : 'Development',
    status: index % 4 === 0 ? 'Pending' : 'Approved',
  })), []);

  // Cấu hình columns cho table
  const columns = useMemo<TableColumn<ExpenseHistoryItem>[]>(() => [
    {
      title: t('admin:profile.expense.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      sortable: true,
    },
    {
      title: t('admin:profile.expense.date', 'Ngày chi'),
      dataIndex: 'date',
      key: 'date',
      sortable: true,
    },
    {
      title: t('admin:profile.expense.description', 'Mô tả'),
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: t('admin:profile.expense.amount', 'Số tiền'),
      dataIndex: 'amount',
      key: 'amount',
      sortable: true,
      render: (value: unknown) => `${(value as number).toLocaleString()} VND`,
    },
    {
      title: t('admin:profile.expense.category', 'Danh mục'),
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: t('admin:profile.expense.status', 'Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      render: (value: unknown) => (
        <span className={(value as string) === 'Approved' ? 'text-green-600' : 'text-yellow-600'}>
          {(value as string) === 'Approved' ? 'Đã duyệt' : 'Chờ duyệt'}
        </span>
      ),
    },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig<ExpenseHistoryItem, Record<string, unknown>>({
    columns,
    createQueryParams: (params) => ({
      page: params.page,
      pageSize: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortOrder: params.sortDirection || undefined,
    }),
  }));

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('admin:profile.expense.title', 'Lịch sử chi tiêu')}
        </Typography>
        
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mockExpenseData}
          loading={false}
        />
      </div>
    </Card>
  );
};

export default ExpenseHistorySection;
