import { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Loader2, Mail, Users } from 'lucide-react';
import {
  IconCard,
  Input,
  Alert,
  FormItem,
  Typography,
  Card,
  DateTimePicker,
  Select,
} from '@/shared/components/common';
import { AsyncSelectWithPagination } from '@/shared/components/common/Select';
import { useFormErrors } from '@/shared/hooks';
// import { createEmailCampaignSchema, type CreateEmailCampaignFormData } from '../../schemas/email.schema';
import { useCreateEmailCampaign } from '../../hooks/email/useEmailCampaigns';
import type { CreateEmailCampaignDto, EmailTemplateDto } from '../../types/email.types';
import { SegmentService } from '../../services/segment.service';
import { AudienceService } from '../../services/audience.service';
import { EmailServerService } from '@/modules/integration/email/services';
import { EmailTemplateSelector } from './EmailTemplateSelector';

interface CreateEmailCampaignFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

/**
 * Form tạo Email Campaign theo quy tắc RedAI
 */
export function CreateEmailCampaignForm({ onSuccess, onCancel }: CreateEmailCampaignFormProps) {
  const { t } = useTranslation(['marketing', 'common']);
  const createCampaign = useCreateEmailCampaign();

  // Sử dụng useFormErrors theo quy tắc RedAI
  const { setFormErrors } = useFormErrors<CreateEmailCampaignDto>();

  // State cho form data
  const [formData, setFormData] = useState<CreateEmailCampaignDto>({
    name: '',
    templateId: '',
    emailServerId: '',
    audienceIds: [],
    segmentIds: [],
  });

  // State cho target type selection (segments hoặc audiences)
  const [targetType, setTargetType] = useState<'segments' | 'audiences'>('segments');

  // State cho audience count
  const [totalAudienceCount, setTotalAudienceCount] = useState<number>(0);
  const [isCalculatingAudience, setIsCalculatingAudience] = useState<boolean>(false);

  // State cho template data và variables
  const [, setSelectedTemplateData] = useState<EmailTemplateDto | null>(null);
  const [templateVariables, setTemplateVariables] = useState<Record<string, string>>({});

  // Handle form submission
  const handleSubmit = async (data: CreateEmailCampaignDto) => {
    // Validate form data
    const errors: Partial<Record<keyof CreateEmailCampaignDto, string>> = {};

    if (!data.name.trim()) {
      errors.name = t('marketing:email.campaigns.form.validation.nameRequired', 'Tên chiến dịch là bắt buộc');
    }



    if (!data.templateId) {
      errors.templateId = t('marketing:email.campaigns.form.validation.templateRequired', 'Vui lòng chọn template');
    }

    // Validate target selection based on type
    if (targetType === 'segments' && (data.segmentIds?.length || 0) === 0) {
      errors.segmentIds = t('marketing:email.campaigns.form.validation.segmentRequired', 'Vui lòng chọn ít nhất một segment');
    }

    if (targetType === 'audiences' && (data.audienceIds?.length || 0) === 0) {
      errors.audienceIds = t('marketing:email.campaigns.form.validation.audienceRequired', 'Vui lòng chọn ít nhất một audience');
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      // Thêm templateVariables vào data trước khi gửi
      const dataWithVariables = {
        ...data,
        templateVariables: Object.keys(templateVariables).length > 0 ? templateVariables : undefined
      };
      await createCampaign.mutateAsync(dataWithVariables);
      onSuccess?.();
    } catch {
      // Error được handle trong hook
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof CreateEmailCampaignDto, value: string | string[] | Date | undefined) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Handle template selection
  const handleTemplateChange = useCallback((templateId: string, templateData?: EmailTemplateDto) => {
    setFormData(prev => ({ ...prev, templateId }));
    setSelectedTemplateData(templateData || null);
  }, []);

  // Handle template variables change
  const handleTemplateVariablesChange = useCallback((variables: Record<string, string>) => {
    setTemplateVariables(variables);
  }, []);



  // Load segments for AsyncSelectWithPagination
  const loadSegments = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await SegmentService.getSegments({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
      });

      const items = response.result.items.map(segment => ({
        value: segment.id.toString(),
        label: `${segment.name} - ${segment.description || 'Không có mô tả'}`,
        data: { segment }
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems || 0,
        totalPages: response.result.meta.totalPages || 1,
        currentPage: response.result.meta.currentPage || 1,
      };
    } catch {
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
      };
    }
  }, []);

  // Load audiences for AsyncSelectWithPagination
  const loadAudiences = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await AudienceService.getAudiences({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
      });

      const items = response.result.data.map(audience => ({
        value: audience.id.toString(),
        label: `${audience.email || audience.phone || 'N/A'} - ${audience.customFields?.map(f => `${f.name}: ${f.value}`).join(', ') || 'Không có thông tin'}`,
        data: { audience }
      }));

      return {
        items,
        totalItems: response.result.meta?.totalItems || 0,
        totalPages: response.result.meta?.totalPages || 1,
        currentPage: response.result.meta?.currentPage || 1,
      };
    } catch {
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
      };
    }
  }, []);

  // Load email servers for AsyncSelectWithPagination
  const loadEmailServers = useCallback(async (params: {
    search?: string;
    page?: number;
    limit?: number;
  }) => {
    try {
      const response = await EmailServerService.getEmailServers({
        search: params.search || '',
        page: params.page || 1,
        limit: params.limit || 20,
        isActive: true, // Chỉ load các server đang active
      });

      const items = response.result.items.map(server => ({
        value: server.id.toString(),
        label: `${server.serverName} (${server.host}:${server.port})`,
        data: { server }
      }));

      return {
        items,
        totalItems: response.result.meta.totalItems || 0,
        totalPages: response.result.meta.totalPages || 1,
        currentPage: response.result.meta.currentPage || 1,
      };
    } catch {
      return {
        items: [],
        totalItems: 0,
        totalPages: 1,
        currentPage: 1,
      };
    }
  }, []);

  // Calculate total audience count when segments or audiences change
  const calculateAudienceCount = useCallback(async (segmentIds: string[], audienceIds: string[], type: 'segments' | 'audiences') => {
    if (type === 'segments' && segmentIds.length === 0) {
      setTotalAudienceCount(0);
      return;
    }

    if (type === 'audiences' && audienceIds.length === 0) {
      setTotalAudienceCount(0);
      return;
    }

    setIsCalculatingAudience(true);
    try {
      let totalCount = 0;

      if (type === 'segments') {
        // Sử dụng API GET /segments/{id}/stats cho từng segment
        for (const segmentId of segmentIds) {
          const stats = await SegmentService.getSegmentStats(parseInt(segmentId));
          totalCount += stats.result.totalAudiences || 0;
        }
      } else {
        // Đối với audiences, chỉ đếm số lượng đã chọn
        totalCount = audienceIds.length;
      }

      setTotalAudienceCount(totalCount);
    } catch {
      setTotalAudienceCount(0);
    } finally {
      setIsCalculatingAudience(false);
    }
  }, []);

  // Effect để tính toán audience count khi segments hoặc audiences thay đổi
  useEffect(() => {
    calculateAudienceCount(formData.segmentIds || [], formData.audienceIds || [], targetType);
  }, [formData.segmentIds, formData.audienceIds, targetType, calculateAudienceCount]);



  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Instructions */}
      <Alert
        type="info"
        message={t('marketing:email.campaigns.form.instructions.title', 'Hướng dẫn tạo Email Campaign')}
        description={t('marketing:email.campaigns.form.instructions.description', 'Tạo chiến dịch email marketing để gửi đến audience hoặc segment cụ thể. Bạn có thể lên lịch gửi hoặc gửi ngay lập tức.')}
      />

      <form onSubmit={(e) => { e.preventDefault(); handleSubmit(formData); }}>
        <div className="space-y-6">
          {/* Basic Info */}
          <Card title={t('marketing:email.campaigns.form.basicInfo.title', 'Thông tin chiến dịch')}>
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.campaigns.form.name.label', 'Tên chiến dịch')}
                name="name"
                required
              >
                <Input
                  placeholder={t('marketing:email.campaigns.form.name.placeholder', 'Ví dụ: Khuyến mãi Black Friday 2024')}
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  leftIcon={<Mail className="h-4 w-4" />}
                  className="w-full"
                />
              </FormItem>


              <FormItem
                label={t('marketing:email.campaigns.form.emailServer.label', 'Máy chủ email')}
                name="emailServerId"
              >
                <AsyncSelectWithPagination
                  value={formData.emailServerId || ''}
                  onChange={(value: string | string[] | number | number[] | undefined) => handleInputChange('emailServerId', value as string | undefined)}
                  loadOptions={loadEmailServers}
                  placeholder={t('marketing:email.campaigns.form.emailServer.placeholder', 'Chọn máy chủ email...')}
                  debounceTime={300}
                  itemsPerPage={20}
                  autoLoadInitial={true}
                  searchOnEnter={true}
                  noOptionsMessage={t('marketing:email.campaigns.form.emailServer.noOptions', 'Không tìm thấy máy chủ email')}
                  loadingMessage={t('marketing:email.campaigns.form.emailServer.loading', 'Đang tải máy chủ email...')}
                  helperText={t('marketing:email.campaigns.form.emailServer.helper', 'Chọn máy chủ email để gửi chiến dịch. Để trống để sử dụng máy chủ mặc định.')}
                  fullWidth
                />
              </FormItem>
            </div>
          </Card>

          {/* Email Template Selection */}
          <EmailTemplateSelector
            value={formData.templateId}
            onChange={handleTemplateChange}
            onVariablesChange={handleTemplateVariablesChange}
            disabled={createCampaign.isPending}
          />

          {/* Target Audience */}
          <Card
            title={t('marketing:email.campaigns.form.audience.title', 'Đối tượng nhận email')}
            subtitle={t('marketing:email.campaigns.form.audience.description', 'Chọn segments hoặc audiences để gửi email')}
          >
            <div className="space-y-4">
              {/* Target Type Selection */}
              <FormItem
                label={t('marketing:email.campaigns.form.targetType.label', 'Loại đối tượng')}
                name="targetType"
                required
              >
                <Select
                  value={targetType}
                  onChange={(value) => {
                    setTargetType(value as 'segments' | 'audiences');
                    // Clear the other type when switching
                    if (value === 'segments') {
                      handleInputChange('audienceIds', []);
                    } else {
                      handleInputChange('segmentIds', []);
                    }
                  }}
                  options={[
                    { value: 'segments', label: t('marketing:email.campaigns.form.targetType.segments', 'Segments') },
                    { value: 'audiences', label: t('marketing:email.campaigns.form.targetType.audiences', 'Audiences') },
                  ]}
                  placeholder={t('marketing:email.campaigns.form.targetType.placeholder', 'Chọn loại đối tượng')}
                  fullWidth
                />
              </FormItem>

              {/* Segments Selection */}
              {targetType === 'segments' && (
                <FormItem
                  label={t('marketing:email.campaigns.form.segments.label', 'Segments')}
                  name="segmentIds"
                  required
                >
                  <AsyncSelectWithPagination
                    value={formData.segmentIds || []}
                    onChange={(value: string | string[] | number | number[] | undefined) => {
                      if (value === undefined) {
                        handleInputChange('segmentIds', []);
                      } else {
                        handleInputChange('segmentIds', Array.isArray(value) ? value as string[] : [value as string]);
                      }
                    }}
                    loadOptions={loadSegments}
                    placeholder={t('marketing:email.campaigns.form.segments.placeholder', 'Tìm kiếm segments...')}
                    debounceTime={300}
                    itemsPerPage={20}
                    autoLoadInitial={true}
                    searchOnEnter={true}
                    multiple
                    noOptionsMessage={t('marketing:email.campaigns.form.segments.noOptions', 'Không tìm thấy segment')}
                    loadingMessage={t('marketing:email.campaigns.form.segments.loading', 'Đang tải segments...')}
                    fullWidth
                  />
                </FormItem>
              )}

              {/* Audiences Selection */}
              {targetType === 'audiences' && (
                <FormItem
                  label={t('marketing:email.campaigns.form.audiences.label', 'Audiences')}
                  name="audienceIds"
                  required
                >
                  <AsyncSelectWithPagination
                    value={formData.audienceIds || []}
                    onChange={(value: string | string[] | number | number[] | undefined) => {
                      if (value === undefined) {
                        handleInputChange('audienceIds', []);
                      } else {
                        handleInputChange('audienceIds', Array.isArray(value) ? value as string[] : [value as string]);
                      }
                    }}
                    loadOptions={loadAudiences}
                    placeholder={t('marketing:email.campaigns.form.audiences.placeholder', 'Tìm kiếm audiences...')}
                    debounceTime={300}
                    itemsPerPage={20}
                    autoLoadInitial={true}
                    searchOnEnter={true}
                    multiple
                    noOptionsMessage={t('marketing:email.campaigns.form.audiences.noOptions', 'Không tìm thấy audience')}
                    loadingMessage={t('marketing:email.campaigns.form.audiences.loading', 'Đang tải audiences...')}
                    fullWidth
                  />
                </FormItem>
              )}

              {/* Audience Count Display */}
              {((targetType === 'segments' && formData.segmentIds && formData.segmentIds.length > 0) ||
                (targetType === 'audiences' && formData.audienceIds && formData.audienceIds.length > 0)) && (
                <div className="mt-3 p-3 bg-muted/50 rounded-md">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-primary" />
                    <Typography variant="body2" weight="medium">
                      {t('marketing:email.campaigns.form.audience.totalCount', 'Tổng số người nhận:')}
                    </Typography>
                    {isCalculatingAudience ? (
                      <div className="flex items-center gap-1">
                        <Loader2 className="h-3 w-3 animate-spin" />
                        <Typography variant="caption" color="muted">
                          {t('marketing:email.campaigns.form.audience.calculating', 'Đang tính toán...')}
                        </Typography>
                      </div>
                    ) : (
                      <Typography variant="body2" weight="bold" className="text-primary">
                        {totalAudienceCount.toLocaleString()}
                      </Typography>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Card>

          {/* Schedule */}
          <Card title={t('marketing:email.campaigns.form.schedule.title', 'Lên lịch gửi')}>
            <div className="space-y-4">
              <FormItem
                label={t('marketing:email.campaigns.form.scheduledAt.label', 'Thời gian gửi (tùy chọn)')}
                name="scheduledAt"
              >
                <DateTimePicker
                  value={formData.scheduledAt ? new Date(formData.scheduledAt) : null}
                  onChange={(dateTime) => handleInputChange('scheduledAt', dateTime || undefined)}
                  placeholder="Chọn ngày và giờ"
                  minDate={new Date()}
                  fullWidth
                  size="md"
                  format="dd/MM/yyyy HH:mm"
                  timeFormat="24h"
                  noBorder={false}
                />
              </FormItem>
            </div>

            <Typography variant="caption" color="muted">
              {t('marketing:email.campaigns.form.schedule.note', 'Để trống để gửi ngay lập tức sau khi tạo chiến dịch')}
            </Typography>
          </Card>

          {/* Submit Actions */}
          <div className="flex justify-end space-x-3">
            <IconCard
              icon="x"
              variant="secondary"
              onClick={() => onCancel?.()}
              disabled={createCampaign.isPending}
              title={t('common:cancel', 'Hủy')}
            />
            <IconCard
              icon={"check"}
              variant="primary"
              onClick={() => handleSubmit(formData)}
              disabled={createCampaign.isPending}
              title={t('marketing:email.campaigns.form.submitButton', 'Tạo chiến dịch')}
              isLoading={createCampaign.isPending}
            />
          </div>
        </div>
      </form>
    </div>
  );
}

export default CreateEmailCampaignForm;
