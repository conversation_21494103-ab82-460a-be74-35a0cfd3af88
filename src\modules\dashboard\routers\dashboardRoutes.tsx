import { Suspense } from 'react';
import { RouteObject } from 'react-router-dom';
import { Loading } from '@/shared/components/common';
import {
  DashboardPage
} from '../pages';
import MainLayout from '@/shared/layouts/MainLayout';

export const dashboardRoutes: RouteObject[] = [
  {
    path: '/dashboard',
    element: (
      <MainLayout title="Dashboard">
        <Suspense fallback={<Loading />}>
          <DashboardPage />
        </Suspense>
      </MainLayout>
    )
  }
];

export default dashboardRoutes;
