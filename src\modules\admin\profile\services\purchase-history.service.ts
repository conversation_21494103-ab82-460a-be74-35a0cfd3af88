import {
  PaginatedPurchaseHistory,
  PurchaseHistoryQueryParams,
} from '../types/purchase-history.types';

/**
 * L<PERSON><PERSON> lịch sử mua hàng của người dùng (mock service cho admin)
 * @param params Tham số truy vấn
 * @returns Danh sách lịch sử mua hàng đã phân trang
 */
export const getPurchaseHistory = async (
  params: PurchaseHistoryQueryParams = {}
): Promise<PaginatedPurchaseHistory> => {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 500));

  // Tạo dữ liệu mẫu
  const mockData: PaginatedPurchaseHistory = {
    items: Array.from({ length: 20 }, (_, index) => ({
      id: index + 1,
      purchaseDate: '20/01/2023',
      amount: 10000 + (index * 1000),
      points: 1000000 + (index * 10000),
      trend: index % 2 === 0 ? 'up' : 'down',
    })),
    total: 20,
    page: params.page || 1,
    pageSize: params.pageSize || 5,
  };

  // Simulate filtering and pagination
  const startIndex = ((params.page || 1) - 1) * (params.pageSize || 5);
  const endIndex = startIndex + (params.pageSize || 5);
  
  return {
    ...mockData,
    items: mockData.items.slice(startIndex, endIndex),
  };
};
