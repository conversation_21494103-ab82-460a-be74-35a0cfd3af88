import React from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { Card, Typography, Icon } from '@/shared/components/common';
import { DashboardWidget } from '../types';
import { renderWidgetContent } from './DashboardCard.utils';
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

// Layout type for react-grid-layout
interface Layout {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
}

interface DashboardCardProps {
  widgets: DashboardWidget[];
  onLayoutChange?: (layout: Layout[], layouts: { [key: string]: Layout[] }) => void;
  onRemoveWidget?: (widgetId: string) => void;
  isDraggable?: boolean;
  isResizable?: boolean;
  className?: string;
  enablePanZoom?: boolean;
  mode?: 'view' | 'edit'; // Current dashboard mode
}

interface WidgetCardProps {
  widget: DashboardWidget;
  children?: React.ReactNode;
  autoHeight?: boolean; // Cho phép card tự động điều chỉnh chiều cao
  onRemove?: (widgetId: string) => void; // Callback để remove widget
  mode?: 'view' | 'edit'; // Current dashboard mode
}

const WidgetCard: React.FC<WidgetCardProps> = ({ widget, children, autoHeight = false, onRemove, mode = 'edit' }) => {
  return (
    <Card className={`${autoHeight ? 'h-auto' : 'h-full'} flex flex-col`}>
      <div className="flex items-center justify-between p-4 border-b border-border">
        <Typography variant="h6" className="font-medium">
          {widget.title}
        </Typography>
        <div className="flex items-center gap-2">
          {/* Only show remove button in edit mode */}
          {onRemove && mode === 'edit' && (
            <button
              onClick={() => onRemove(widget.id)}
              className="w-6 h-6 flex items-center justify-center rounded-full text-muted-foreground hover:text-destructive hover:bg-destructive/10 cursor-pointer transition-all duration-200"
              title="Xóa widget"
              type="button"
            >
              <Icon name="x" className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>
      <div className={`${autoHeight ? 'p-4' : 'flex-1 p-4'}`}>
        {widget.isEmpty ? (
          <div className={`${autoHeight ? 'py-8' : 'h-full'} flex flex-col items-center justify-center text-center`}>
            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4">
              <Icon name="bar-chart-3" className="w-8 h-8 text-muted-foreground" />
            </div>
            <Typography variant="body2" className="text-muted-foreground mb-2">
              Không có dữ liệu
            </Typography>
            <Typography variant="body2" className="text-xs text-muted-foreground">
              Dữ liệu sẽ hiển thị khi có thông tin
            </Typography>
          </div>
        ) : (
          children || (
            <div className={`${autoHeight ? 'py-8' : 'h-full'} flex items-center justify-center`}>
              <Typography variant="body2" className="text-muted-foreground">
                Nội dung widget
              </Typography>
            </div>
          )
        )}
      </div>
    </Card>
  );
};

const DashboardCard: React.FC<DashboardCardProps> = ({
  widgets,
  onLayoutChange,
  onRemoveWidget,
  isDraggable = true,
  isResizable = true,
  className = '',
  mode = 'edit',
}) => {
  // Enable drag/resize in both view and edit modes
  const canDrag = isDraggable;
  const canResize = isResizable;
  // Convert widgets to layouts for react-grid-layout
  const layouts = {
    lg: widgets.map(widget => ({
      i: widget.id,
      x: widget.x,
      y: widget.y,
      w: widget.w,
      h: widget.h,
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    })),
    md: widgets.map(widget => ({
      i: widget.id,
      x: widget.x,
      y: widget.y,
      w: Math.min(widget.w, 8),
      h: widget.h,
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    })),
    sm: widgets.map(widget => ({
      i: widget.id,
      x: 0,
      y: widget.y,
      w: 6,
      h: widget.h,
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    })),
    xs: widgets.map(widget => ({
      i: widget.id,
      x: 0,
      y: widget.y,
      w: 4,
      h: widget.h,
      minW: widget.minW,
      minH: widget.minH,
      maxW: widget.maxW,
      maxH: widget.maxH
    }))
  };

  const breakpoints = { lg: 1200, md: 996, sm: 768, xs: 480 };
  const cols = { lg: 12, md: 8, sm: 6, xs: 4 };

  return (
    <div className={`dashboard-grid ${className}`}>
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        breakpoints={breakpoints}
        cols={cols}
        rowHeight={60}
        isDraggable={canDrag}
        isResizable={canResize}
        onLayoutChange={onLayoutChange}
        margin={[16, 16]}
        containerPadding={[0, 0]}
        autoSize={true}
        useCSSTransforms={true}
        preventCollision={false}
        compactType="vertical"
        verticalCompact={true}
      >
        {widgets.map(widget => (
          <div key={widget.id}>
            <WidgetCard
              widget={widget}
              autoHeight={false}
              mode={mode}
              {...(onRemoveWidget ? { onRemove: onRemoveWidget } : {})}
            >
              {/* Render specific widgets based on type */}
              {renderWidgetContent(widget)}
            </WidgetCard>
          </div>
        ))}
      </ResponsiveGridLayout>
    </div>
  );
};

export default DashboardCard;
export { WidgetCard };
