import React from 'react';
import { type WidgetType } from '../constants';

export interface DashboardData {
  totalRevenue: number;
  totalOrders: number;
  totalCustomers: number;
  conversionRate: number;
}

export interface DashboardCard {
  id: string;
  title: string;
  value: string | number;
  change: number;
  changeType: 'increase' | 'decrease';
  icon: string;
}

export interface DashboardChart {
  id: string;
  title: string;
  type: 'line' | 'bar' | 'pie' | 'area';
  data: unknown[];
}

// New types for the analytics dashboard
export interface MenuItem {
  id: string;
  title: string;
  icon: string;
  path: string;
  children?: MenuItem[];
}

export interface MenuSection {
  id: string;
  title: string;
  items: MenuItem[];
}

export interface DashboardWidget {
  id: string;
  title: string;
  type: WidgetType;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  content?: React.ReactNode;
  isEmpty?: boolean;
}

export interface DashboardLayout {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  isDefault?: boolean;
}

export interface DashboardState {
  selectedMenuItem: string | null;
  sidebarCollapsed: boolean;
  currentLayout: DashboardLayout | null;
  searchQuery: string;
  selectedView: string;
}

// New tab system types
export interface DashboardTab {
  id: string;
  name: string;
  widgets: DashboardWidget[];
  mode: 'view' | 'edit';
  createdAt: string;
  updatedAt: string;
}

export interface DashboardTabsState {
  currentTabId: string;
  tabs: DashboardTab[];
}
