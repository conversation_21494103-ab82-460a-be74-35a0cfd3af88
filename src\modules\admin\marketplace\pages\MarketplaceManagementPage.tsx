import React from 'react';
import { useTranslation } from 'react-i18next';

import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import PageWrapper from '@/shared/components/common/PageWrapper';

/**
 * Trang tổng quan quản lý Marketplace
 */
const MarketplaceManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  return (
    <PageWrapper>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Products Card */}
        <ModuleCard
          title={t('admin:marketplace.products', 'Sản phẩm')}
          description={t(
            'admin:marketplace.productsDescription',
            'Quản lý các sản phẩm trong marketplace, bao gồm thêm, sử<PERSON>, xóa và phê duyệt sản phẩm.'
          )}
          icon="box"
          linkTo="/admin/marketplace/products"
        />

        {/* Orders Card */}
        <ModuleCard
          title={t('admin:marketplace.orders', 'Đơn hàng')}
          description={t(
            'admin:marketplace.ordersDescription',
            'Quản lý các đơn hàng của khách hàng, theo dõi trạng thái và xử lý đơn hàng.'
          )}
          icon="shopping-cart"
          linkTo="/admin/marketplace/orders"
        />

        {/* Cart Card */}
        <ModuleCard
          title={t('admin:marketplace.cart.cart', 'Giỏ hàng')}
          description={t(
            'admin:marketplace.cartDescription',
            'Xem và quản lý giỏ hàng của khách hàng trong hệ thống.'
          )}
          icon="shopping-bag"
          linkTo="/admin/marketplace/cart"
        />
      </ResponsiveGrid>
    </PageWrapper>
  );
};

export default MarketplaceManagementPage;
