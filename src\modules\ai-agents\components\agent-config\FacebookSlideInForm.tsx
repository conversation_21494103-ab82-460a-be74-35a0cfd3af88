import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useGetFacebookPages } from '@/modules/integration/facebook/hooks/useFacebook';
import { FacebookPageDto, FacebookPageQueryDto, FacebookPageSortBy, SortOrder } from '@/modules/integration/facebook/types/facebook.types';
import {
  Button,
  Card,
  Icon,
  Table,
  Typography
} from '@/shared/components/common';
import SlideInForm from '@/shared/components/common/SlideInForm';
import { TableColumn } from '@/shared/components/common/Table/types';
import { useAgentFacebookPages, useAddFacebookPages } from '@/modules/ai-agents/hooks/useAgentIntegration';
import { NotificationUtil } from '@/shared/utils/notification';
import { useTranslation } from 'react-i18next';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

/**
 * Interface cho item Facebook Page - mapped từ FacebookPageDto
 */
interface FacebookPage {
  id: string;
  name: string;
  imageUrl?: string;
  category?: string;
  followers?: number;
  isConnected?: boolean;
}

/**
 * Props cho component FacebookSlideInForm
 */
interface FacebookSlideInFormProps {
  /**
   * Trạng thái hiển thị của form
   */
  isVisible: boolean;

  /**
   * Callback khi đóng form
   */
  onClose: () => void;

  /**
   * Callback khi lưu dữ liệu (chỉ dùng trong create mode)
   */
  onSave?: (selectedIds: string[]) => void;

  /**
   * ID của agent
   */
  agentId?: string;

  /**
   * Mode: create hoặc edit
   */
  mode: 'create' | 'edit';
}

/**
 * Component form trượt để chọn các trang Facebook để tích hợp
 */
const FacebookSlideInForm: React.FC<FacebookSlideInFormProps> = ({
  isVisible,
  onClose,
  onSave,
  agentId,
  mode,
}) => {
  const { t } = useTranslation(['aiAgents', 'common']);

  // State cho UI
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // API hooks
  const { data: agentFacebookResponse } = useAgentFacebookPages(agentId && mode === 'edit' ? agentId : '');
  const addFacebookPagesMutation = useAddFacebookPages();

  // Lấy danh sách Facebook pages đã chọn từ agent - sử dụng useMemo để tránh re-render
  const selectedPageIds = useMemo(() => {
    return agentFacebookResponse?.facebookPages?.map(page => page.id) || [];
  }, [agentFacebookResponse?.facebookPages]);

  // State cho query parameters
  const [queryParams, setQueryParams] = useState<FacebookPageQueryDto>({
    page: 1,
    limit: 10,
    search: '',
    sortBy: FacebookPageSortBy.PAGE_NAME,
  });

  // Khởi tạo selectedIds từ agent Facebook pages khi component mount
  useEffect(() => {
    if (mode === 'edit' && selectedPageIds.length > 0 && selectedIds.length === 0) {
      setSelectedIds(selectedPageIds);
    }
  }, [selectedPageIds, mode, selectedIds.length]);

  // API hook để lấy dữ liệu Facebook Pages
  const { data: pagesData, isLoading } = useGetFacebookPages(queryParams);

  // Transform API data to component format
  const pages: FacebookPage[] = useMemo(() => {
    if (!pagesData?.result?.items) return [];

    return pagesData.result.items.map((page: FacebookPageDto): FacebookPage => ({
      id: page.facebookPageId,
      name: page.pageName,
      imageUrl: page.avatarPage || "",
      category: "", // API không có category
      followers: 0, // API không có followers
      isConnected: page.isActive && !page.isError,
    }));
  }, [pagesData]);

  // Pagination data
  const totalItems = pagesData?.result?.meta?.totalItems || 0;
  const currentPage = queryParams.page || 1;
  const itemsPerPage = queryParams.limit || 10;

  // Cấu hình cột cho bảng
  const columns: TableColumn<FacebookPage>[] = [
    {
      key: 'selection',
      title: '',
      width: 50,
    },
    {
      key: 'page',
      title: t('aiAgents:facebookSlideInForm.facebookPage'),
      dataIndex: 'name',
      width: '40%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.imageUrl ? (
            <img
              src={record.imageUrl}
              alt={record.name}
              className="w-10 h-10 rounded-full mr-3 object-cover"
            />
          ) : (
            <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
              <Icon name="facebook" size="md" className="text-blue-600" />
            </div>
          )}
          <div>
            <Typography variant="subtitle1">{record.name}</Typography>
            <Typography variant="caption" className="text-gray-500">
              ID: {record.id}
            </Typography>
          </div>
        </div>
      ),
    },
    {
      key: 'category',
      title: t('aiAgents:facebookSlideInForm.category'),
      dataIndex: 'category',
      width: '20%',
    },
    {
      key: 'followers',
      title: t('aiAgents:facebookSlideInForm.followers'),
      dataIndex: 'followers',
      width: '20%',
      render: (followers) => followers?.toLocaleString() || '0',
    },
    {
      key: 'status',
      title: t('aiAgents:facebookSlideInForm.status'),
      width: '20%',
      render: (_, record) => (
        <div className="flex items-center">
          {record.isConnected ? (
            <span className="text-green-500 text-sm flex items-center">
              <Icon name="check-circle" size="sm" className="mr-1" />
              {t('aiAgents:facebookSlideInForm.connected')}
            </span>
          ) : (
            <span className="text-gray-500 text-sm flex items-center">
              <Icon name="circle" size="sm" className="mr-1" />
              {t('aiAgents:facebookSlideInForm.notConnected')}
            </span>
          )}
        </div>
      ),
    },
  ];





  // Xử lý tìm kiếm
  const handleSearch = (term: string) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      search: term,
      page: 1,
    }));
  };

  // Xử lý thay đổi trang
  const handlePageChange = (page: number) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      page,
    }));
  };

  // Xử lý thay đổi số lượng item trên trang
  const handleItemsPerPageChange = (value: number) => {
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      limit: value,
      page: 1,
    }));
  };

  // Xử lý thay đổi sắp xếp
  const handleSortChange = (column: string, direction?: 'ASC' | 'DESC') => {
    const sortBy = column === 'pageName' ? FacebookPageSortBy.PAGE_NAME : FacebookPageSortBy.CREATED_AT;
    const sortOrder = direction === 'ASC' ? SortOrder.ASC : direction === 'DESC' ? SortOrder.DESC : undefined;

    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      sortBy,
      ...(sortOrder && { sortOrder }),
    }));
  };

  // Xử lý lưu - phân biệt theo mode
  const handleSave = async () => {
    if (mode === 'edit') {
      // Edit mode: gọi API
      if (!agentId) {
        NotificationUtil.error({
          message: t('aiAgents:facebookSlideInForm.cannotSaveInThisMode'),
        });
        return;
      }

      setIsSubmitting(true);
      try {
        // Gọi API để cập nhật Facebook pages cho agent
        const response = await addFacebookPagesMutation.mutateAsync({
          agentId,
          data: { facebookPageIds: selectedIds }
        });

        // Xử lý response chi tiết
        if (response.integratedCount > 0) {
          NotificationUtil.success({
            message: t('aiAgents:facebookSlideInForm.integratedSuccessfully', { count: response.integratedCount }),
          });
        }

        if (response.skippedCount > 0) {
          const errorDetails = response.details
            .filter(detail => detail.status === 'error' || detail.status === 'skipped')
            .map(detail => detail.error || 'Facebook Page đã được tích hợp với agent khác')
            .join(', ');

          NotificationUtil.warning({
            message: t('aiAgents:facebookSlideInForm.cannotIntegrate', { count: response.skippedCount, details: errorDetails }),
            duration: 8000,
          });
        }

        if (response.integratedCount === 0 && response.skippedCount > 0) {
          NotificationUtil.error({
            message: t('aiAgents:facebookSlideInForm.noPageIntegratedSuccessfully'),
          });
        }

        // Gọi callback để parent component biết có thay đổi
        onSave?.(selectedIds);
        onClose();
      } catch (error) {
        console.error('Facebook integration error:', error);
        NotificationUtil.error({
          message: t('aiAgents:facebookSlideInForm.updateIntegrationError'),
        });
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Create mode: gọi callback để cập nhật parent component
      if (onSave) {
        onSave(selectedIds);
      }
      NotificationUtil.success({
        message: t('aiAgents:facebookSlideInForm.selectedFacebookIntegration'),
      });
      onClose();
    }
  };

  // Xử lý đóng form - không cần confirm
  const handleClose = useCallback(() => {
    // Reset search khi đóng
    setQueryParams((prev: FacebookPageQueryDto) => ({
      ...prev,
      search: '',
      page: 1,
    }));
    onClose();
  }, [onClose]);

  // Các menu items cho MenuIconBar
  const menuItems = [
    {
      id: 'sort',
      label: t('aiAgents:facebookSlideInForm.sortBy'),
      icon: 'sort',
      onClick: () => { },
    },
    {
      id: 'sort-name',
      label: t('aiAgents:facebookSlideInForm.name'),
      onClick: () => handleSortChange('pageName', 'ASC'),
    },
    {
      id: 'divider',
      divider: true,
    },
    {
      id: 'filter',
      label: t('aiAgents:facebookSlideInForm.filterBy'),
      icon: 'filter',
      onClick: () => { },
    },
    {
      id: 'filter-all',
      label: t('aiAgents:facebookSlideInForm.all'),
      onClick: () => setQueryParams((prev: FacebookPageQueryDto) => {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { facebookPersonalId, ...rest } = prev;
        return rest;
      }),
    },
    {
      id: 'filter-connected',
      label: t('aiAgents:facebookSlideInForm.connectedPages'),
      onClick: () => { },
    },
    {
      id: 'filter-not-connected',
      label: t('aiAgents:facebookSlideInForm.notConnectedPages'),
      onClick: () => { },
    },
  ];

  return (
    <SlideInForm isVisible={isVisible}>
      <Card className="w-full max-w-6xl">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h5">{t('aiAgents:facebookSlideInForm.title')}</Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            leftIcon={<Icon name="x" size="sm" />}
          >
            {t('aiAgents:facebookSlideInForm.close')}
          </Button>
        </div>

        {/* Thanh tìm kiếm và lọc */}
        <div className="mb-4">
          <MenuIconBar
            onSearch={handleSearch}
            items={menuItems}
            showDateFilter={false}
            showColumnFilter={false}
          />
        </div>

        {/* Bảng dữ liệu */}
        <Card className="overflow-hidden mb-4">
          <Table<FacebookPage>
            columns={columns}
            data={pages}
            rowKey="id"
            loading={isLoading}
            sortable={true}
            onSortChange={(column, order) => {
              if (column) {
                handleSortChange(column, order === 'asc' ? 'ASC' : 'DESC');
              }
            }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (keys) => setSelectedIds(keys as string[]),
            }}
            pagination={{
              current: currentPage,
              pageSize: itemsPerPage,
              total: totalItems,
              onChange: (page: number, pageSize: number) => {
                handlePageChange(page);
                if (pageSize !== itemsPerPage) {
                  handleItemsPerPageChange(pageSize);
                }
              },
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 20, 50],
              showFirstLastButtons: true,
              showPageInfo: true,
            }}
          />
        </Card>

        {/* Nút lưu */}
        <div className="flex justify-end">
          <Button
            variant="outline"
            onClick={handleClose}
            className="mr-2"
            disabled={isSubmitting}
          >
            {t('aiAgents:facebookSlideInForm.cancel')}
          </Button>
          <Button
            variant="primary"
            onClick={handleSave}
            isLoading={isSubmitting}
            disabled={isLoading || isSubmitting}
          >
            {t('aiAgents:facebookSlideInForm.save')}
          </Button>
        </div>
      </Card>
    </SlideInForm>
  );
};

export default FacebookSlideInForm;
