import React, { useState, useRef } from 'react';
import { Icon, Typography } from '@/shared/components/common';
import { DashboardTab } from '../types';

interface DashboardTabsProps {
  tabs: DashboardTab[];
  currentTabId: string;
  onTabChange: (tabId: string) => void;
  onTabCreate: () => void;
  onTabRename: (tabId: string, newName: string) => void;
  onTabDelete: (tabId: string) => void;
  onTabReorder: (fromIndex: number, toIndex: number) => void;
  onModeChange: (mode: 'view' | 'edit') => void;
  onSave: () => void;
  className?: string;
}

const DashboardTabs: React.FC<DashboardTabsProps> = ({
  tabs,
  currentTabId,
  onTabChange,
  onTabCreate,
  onTabRename,
  onTabDelete,
  onTabReorder,
  onModeChange,
  onSave,
  className = ''
}) => {
  const [editingTabId, setEditingTabId] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [draggedTabIndex, setDraggedTabIndex] = useState<number | null>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const currentTab = tabs.find(tab => tab.id === currentTabId);
  const currentMode = currentTab?.mode || 'view';

  const handleTabDoubleClick = (tab: DashboardTab) => {
    setEditingTabId(tab.id);
    setEditingName(tab.name);
    setTimeout(() => inputRef.current?.focus(), 0);
  };

  const handleNameSubmit = () => {
    if (editingTabId && editingName.trim()) {
      onTabRename(editingTabId, editingName.trim());
    }
    setEditingTabId(null);
    setEditingName('');
  };

  const handleNameCancel = () => {
    setEditingTabId(null);
    setEditingName('');
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleNameSubmit();
    } else if (e.key === 'Escape') {
      handleNameCancel();
    }
  };

  const handleDragStart = (e: React.DragEvent, index: number) => {
    setDraggedTabIndex(index);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = (e: React.DragEvent, dropIndex: number) => {
    e.preventDefault();
    if (draggedTabIndex !== null && draggedTabIndex !== dropIndex) {
      onTabReorder(draggedTabIndex, dropIndex);
    }
    setDraggedTabIndex(null);
  };

  const handleTabContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    // TODO: Implement context menu for rename/delete
  };

  return (
    <div className={`flex items-center gap-2 bg-card border-b border-border p-2 ${className}`}>
      {/* Tabs Container */}
      <div className="flex items-center gap-1 flex-1 overflow-x-auto scrollbar-hide">
        {tabs.map((tab, index) => {
          try {
            return (
            <div
              key={tab.id}
              draggable
              onDragStart={(e) => handleDragStart(e, index)}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, index)}
              onDoubleClick={() => handleTabDoubleClick(tab)}
              onContextMenu={handleTabContextMenu}
              className={`
                group relative flex items-center gap-2 px-3 py-2 rounded-lg cursor-pointer
                transition-all duration-200 min-w-[120px] max-w-[200px]
                ${tab.id === currentTabId
                  ? 'bg-primary text-primary-foreground shadow-sm'
                  : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
                }
                ${draggedTabIndex === index ? 'opacity-50' : ''}
              `}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                onTabChange(tab.id);
              }}
            >
            {editingTabId === tab.id ? (
              <input
                ref={inputRef}
                type="text"
                value={editingName}
                onChange={(e) => setEditingName(e.target.value)}
                onBlur={handleNameSubmit}
                onKeyDown={handleKeyDown}
                className="bg-transparent border-none outline-none text-sm font-medium w-full"
                autoFocus
              />
            ) : (
              <Typography variant="body2" className="font-medium truncate">
                {typeof tab.name === 'string' ? tab.name : 'Invalid Name'}
              </Typography>
            )}
            
            {/* Delete button - only show if more than 1 tab and on hover */}
            {tabs.length > 1 && (
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  onTabDelete(tab.id);
                }}
                className={`
                  flex items-center justify-center w-5 h-5 rounded-full
                  transition-all duration-200 hover:bg-destructive hover:text-destructive-foreground
                  ${tab.id === currentTabId
                    ? 'opacity-0 group-hover:opacity-100 bg-primary-foreground/20 text-primary-foreground hover:bg-destructive'
                    : 'opacity-0 group-hover:opacity-100 bg-muted-foreground/20 text-muted-foreground hover:bg-destructive hover:text-destructive-foreground'
                  }
                `}
                type="button"
                title="Xóa tab"
              >
                <Icon name="x" className="w-3 h-3" />
              </button>
            )}
          </div>
          );
          } catch (error) {
            console.error('Error rendering tab:', error, tab);
            return (
              <div key={tab.id || `error-${index}`} className="px-3 py-2 bg-red-100 text-red-800 rounded">
                Error: {typeof tab?.name === 'string' ? tab.name : 'Unknown'}
              </div>
            );
          }
        })}
        
        {/* Add Tab Button */}
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onTabCreate();
          }}
          className="flex items-center justify-center w-8 h-8 rounded-lg bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground transition-all duration-200"
          title="Tạo trang mới"
        >
          <Icon name="plus" className="w-4 h-4" />
        </button>
      </div>

      {/* Controls */}
      <div className="flex items-center gap-2">
        {/* View/Edit Mode Toggle */}
        <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onModeChange('view');
            }}
            className={`flex items-center gap-1 px-3 py-1 rounded text-xs font-medium transition-all ${
              currentMode === 'view'
                ? 'bg-primary text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            title="Chế độ xem"
          >
            <Icon name="eye" className="w-3 h-3" />
            Xem
          </button>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onModeChange('edit');
            }}
            className={`flex items-center gap-1 px-3 py-1 rounded text-xs font-medium transition-all ${
              currentMode === 'edit'
                ? 'bg-primary text-foreground shadow-sm'
                : 'text-muted-foreground hover:text-foreground'
            }`}
            title="Chế độ chỉnh sửa"
          >
            <Icon name="edit" className="w-3 h-3" />
            Chỉnh sửa
          </button>
        </div>

        {/* Save Button */}
        <button
          type="button"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onSave();
          }}
          className="flex items-center gap-2 px-3 py-1.5 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all text-sm font-medium"
        >
          <Icon name="save" className="w-4 h-4" />
          Lưu
        </button>
      </div>
    </div>
  );
};

export default DashboardTabs;
