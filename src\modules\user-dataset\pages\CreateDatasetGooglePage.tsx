import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { NotificationUtil } from '@/shared/utils/notification';

import DatasetFormGoogle from '../components/DatasetFormGoogle';

/**
 * Page để tạo dataset cho Google
 * Sử dụng DatasetFormGoogle (không có ConversationSidebar)
 */
const CreateDatasetGooglePage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleSuccess = () => {
    // Show success notification
    NotificationUtil.success({
      title: t('user-dataset:createDataset.notification.success.title', 'Thành công'),
      message: t(
        'user-dataset:createDataset.notification.success.messageGoogle',
        'Dataset Google đã được tạo thành công!'
      ),
      duration: 5000,
    });

    // Navigate back to dataset list
    navigate('/user-dataset/data-fine-tune');
  };
  const handleErrors = () => {
    // Show error notification
    NotificationUtil.error({
      title: t('user-dataset:createDataset.notification.error.title', 'Lỗi'),
      message: t(
        'user-dataset:createDataset.notification.error.message',
        'Không thể tạo dataset. Vui lòng kiểm tra lại tên dataset.'
      ),
      duration: 5000,
    });
  };

  return (
    <div className="h-full">
      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        <DatasetFormGoogle onSuccess={handleSuccess} onErrors={handleErrors} />
      </div>
    </div>
  );
};

export default CreateDatasetGooglePage;
