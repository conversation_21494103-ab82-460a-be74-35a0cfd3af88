import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthCommon, AuthType } from '@/shared/hooks/useAuthCommon';

/**
 * Component bảo vệ route - kiểm tra token và điều hướng nếu cần
 */
interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredAuthType?: AuthType;
  redirectTo: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredAuthType = AuthType.USER,
  redirectTo
}) => {
  const { isTokenValid, authType, isAuthenticated, clearAuth } = useAuthCommon();
  const location = useLocation();

  useEffect(() => {
    // Log thông tin debug
    console.log('ProtectedRoute check:', {
      currentPath: location.pathname,
      requiredAuthType,
      currentAuthType: authType,
      isAuthenticated,
      isTokenValid: isTokenValid(),
      redirectTo
    });
  }, [location.pathname, requiredAuthType, authType, isAuthenticated, isTokenValid, redirectTo]);

  // Kiểm tra token có hợp lệ không
  const tokenValid = isTokenValid();

  if (!tokenValid || !isAuthenticated) {
    // Xóa auth data nếu token không hợp lệ
    if (!tokenValid) {
      clearAuth();
    }

    // Điều hướng dựa trên loại auth yêu cầu
    const defaultRedirect = requiredAuthType === AuthType.ADMIN ? '/admin/auth' : '/auth';
    console.log('Token invalid or not authenticated, redirecting to:', redirectTo || defaultRedirect);
    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  // Kiểm tra loại auth có đúng không
  if (requiredAuthType !== AuthType.NONE && authType !== requiredAuthType) {
    const defaultRedirect = requiredAuthType === AuthType.ADMIN ? '/admin/auth' : '/auth';
    console.log('Auth type mismatch, redirecting to:', redirectTo || defaultRedirect);
    return <Navigate to={redirectTo || defaultRedirect} replace />;
  }

  return <>{children}</>;
};



export default ProtectedRoute;
