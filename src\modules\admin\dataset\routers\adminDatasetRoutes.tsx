import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import UserDatasetManagementPage from '../pages/UserDatasetManagementPage';
import { CreateDatasetGooglePage, CreateDatasetOpenAIPage } from '../pages';

// Import User Dataset module pages
const DataFineTunePage = lazy(() => import('../pages/DataFineTunePage'));
const TrashDataFineTunePage = lazy(() => import('../pages/TrashDataFineTunePage'));

/**
 * User Dataset module routes
 */
const adminDatasetRoutes: RouteObject[] = [
  {
    path: '/admin/dataset',
    element: (
      <AdminLayout title="admin-dataset:title">
        <Suspense fallback={<Loading />}>
          <UserDatasetManagementPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/dataset/data-fine-tune',
    element: (
      <AdminLayout title="admin-dataset:dataFineTune.title">
        <Suspense fallback={<Loading />}>
          <DataFineTunePage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/dataset/data-fine-tune/trash',
    element: (
      <AdminLayout title="admin-dataset:trash.title">
        <Suspense fallback={<Loading />}>
          <TrashDataFineTunePage />
        </Suspense>
      </AdminLayout>
    ),
  },

  {
    path: '/admin/dataset/create-openai',
    element: (
      <AdminLayout title="admin-dataset:createDataset.openai.title">
        <Suspense fallback={<Loading />}>
          <CreateDatasetOpenAIPage />
        </Suspense>
      </AdminLayout>
    ),
  },
  {
    path: '/admin/dataset/create-google',
    element: (
      <AdminLayout title="admin-dataset:createDataset.google.title">
        <Suspense fallback={<Loading />}>
          <CreateDatasetGooglePage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default adminDatasetRoutes;
