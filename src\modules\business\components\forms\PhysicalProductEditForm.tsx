import React, { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormMultiWrapper,
  IconCard,
  Typography,
} from '@/shared/components/common';

import { z } from 'zod';
import {
  HasPriceDto,
  StringPriceDto,
  UpdateProductDto,
} from '../../types/product.types';
import { useCustomFields } from '../../hooks/useCustomFieldQuery';

import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import { useProduct, useUpdateProduct } from '../../hooks/useProductQuery';
import {
  GeneralInfoSection,
  PricingSection,
  MediaSection,
  InventorySection,
  ShippingSection,
  ClassificationsSection,
  CustomFieldsSection,
  ProductInventory,
  ExtendedFileWithMetadata,
  ProductVariant,
  SelectedCustomField,
} from './sections';

// Import types từ physical edit form
import { PhysicalProductEditFormValues } from './sections/physical-product-edit-form-types';

// Note: Response interfaces are now defined in product.types.ts

interface PhysicalProductEditFormProps {
  productId: number;
  onCancel: () => void;
  onSuccess?: () => void;
  key?: string | number; // Thêm key prop để force re-render khi cần
}

/**
 * Wrapper component để xử lý loading và error states trước khi render form chính
 */
const PhysicalProductEditFormWrapper: React.FC<PhysicalProductEditFormProps> = ({
  productId,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Gọi API lấy chi tiết sản phẩm với staleTime ngắn để đảm bảo data luôn fresh
  const { data: product, isLoading: isLoadingProduct, error: productError, refetch: refetchProduct } = useProduct(productId, {
    staleTime: 0, // Luôn fetch data mới khi component mount
    gcTime: 5 * 60 * 1000, // 5 minutes cache
  });

  // Hiển thị loading state
  if (isLoadingProduct) {
    return (
      <div className="w-full bg-background text-foreground">
        <div className="flex items-center justify-center p-8">
          <Typography variant="body1">
            {t('business:product.loading', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </div>
    );
  }

  // Hiển thị error state
  if (productError) {
    console.error('❌ [PhysicalProductEditForm] Error loading product:', productError);
    return (
      <div className="w-full bg-background text-foreground">
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <Typography variant="h6" className="text-red-600 dark:text-red-400">
            {t('business:product.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400 text-center">
            {productError instanceof Error ? productError.message : 'Đã xảy ra lỗi khi tải dữ liệu sản phẩm'}
          </Typography>
          <div className="flex gap-2">
            <IconCard
              icon="refresh"
              variant="primary"
              size="sm"
              title={t('common:retry', 'Thử lại')}
              onClick={() => refetchProduct()}
            />
            <IconCard
              icon="x"
              variant="secondary"
              size="sm"
              title={t('common:cancel', 'Hủy')}
              onClick={onCancel}
            />
          </div>
        </div>
      </div>
    );
  }

  // Kiểm tra nếu không có product data
  if (!product) {
    return (
      <div className="w-full bg-background text-foreground">
        <div className="flex flex-col items-center justify-center p-8 space-y-4">
          <Typography variant="h6" className="text-yellow-600 dark:text-yellow-400">
            {t('business:product.notFound', 'Không tìm thấy sản phẩm')}
          </Typography>
          <Typography variant="body2" className="text-gray-600 dark:text-gray-400 text-center">
            {t('business:product.notFoundDescription', 'Sản phẩm có thể đã bị xóa hoặc không tồn tại')}
          </Typography>
          <IconCard
            icon="x"
            variant="secondary"
            size="sm"
            title={t('common:close', 'Đóng')}
            onClick={onCancel}
          />
        </div>
      </div>
    );
  }

  // Render form chính với product data
  return (
    <PhysicalProductEditForm
      product={product as unknown as ExtendedProductData}
      onCancel={onCancel}
      onSuccess={onSuccess}
    />
  );
};

/**
 * Extended product type with additional properties that might come from API
 */
interface ExtendedProductData {
  id: number;
  name: string;
  price?: HasPriceDto | StringPriceDto | null;
  images?: Array<{ key: string; url: string; alt?: string; position?: number }>;
  metadata?: {
    customFields?: Array<{
      id: number;
      tags: string[];
      type: string;
      label: string;
      value: Record<string, unknown>;
      configId: string;
      required: boolean;
      configJson: Record<string, unknown>;
    }>;
  };
  classifications?: Array<{
    id: number;
    name?: string;
    type: string;
    description?: string;
    price?: {
      listPrice?: number;
      salePrice?: number;
      currency?: string;
    };
    customFields?: Array<Record<string, unknown>>;
    imagesMediaTypes?: Array<Record<string, unknown>>;
    images?: Array<{ url: string; alt?: string }>;
  }>;
  inventory?: ProductInventory[] | Record<string, unknown>;
  description?: string;
  tags?: string[];
  shipmentConfig?: {
    lengthCm?: number;
    widthCm?: number;
    heightCm?: number;
    weightGram?: number;
  };
  // Add index signature to match ProductDto
  [key: string]: unknown;
}

/**
 * Form chỉnh sửa sản phẩm vật lý - Component chính
 */
interface PhysicalProductEditFormMainProps {
  product: ExtendedProductData;
  onCancel: () => void;
  onSuccess?: (() => void) | undefined;
}

const PhysicalProductEditForm: React.FC<PhysicalProductEditFormMainProps> = ({
  product,
  onCancel,
  onSuccess
}) => {
  const { t } = useTranslation(['business', 'common']);

  // Hook để cập nhật sản phẩm
  const updateProductMutation = useUpdateProduct();

  // Schema validation với giá cố định
  const productSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      listPrice: z.union([z.string(), z.number()]),
      salePrice: z.union([z.string(), z.number()]),
      currency: z.string(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      shipmentConfig: z
        .object({
          lengthCm: z.union([z.string(), z.number()]).optional(),
          widthCm: z.union([z.string(), z.number()]).optional(),
          heightCm: z.union([z.string(), z.number()]).optional(),
          weightGram: z.union([z.string(), z.number()]).optional(),
        })
        .optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      classifications: z.any().optional(),
      inventory: z
        .array(
          z.object({
            id: z.number(),
            warehouseId: z.union([z.string(), z.number()]).optional(),
            availableQuantity: z.union([z.string(), z.number()]).optional(),
            sku: z.string().optional(),
            barcode: z.string().optional(),
          })
        )
        .optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra listPrice
      if (!data.listPrice || data.listPrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá niêm yết',
          path: ['listPrice'],
        });
      } else {
        const listPrice = Number(data.listPrice);
        if (isNaN(listPrice) || listPrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá niêm yết phải là số >= 0',
            path: ['listPrice'],
          });
        }
      }

      // Kiểm tra salePrice
      if (!data.salePrice || data.salePrice === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng nhập giá bán',
          path: ['salePrice'],
        });
      } else {
        const salePrice = Number(data.salePrice);
        if (isNaN(salePrice) || salePrice < 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Giá bán phải là số >= 0',
            path: ['salePrice'],
          });
        }
      }

      // Kiểm tra currency
      if (!data.currency || data.currency.trim() === '') {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: 'Vui lòng chọn đơn vị tiền tệ',
          path: ['currency'],
        });
      }

      // Kiểm tra giá niêm yết phải lớn hơn giá bán
      if (data.listPrice && data.salePrice && data.listPrice !== '' && data.salePrice !== '') {
        const listPrice = Number(data.listPrice);
        const salePrice = Number(data.salePrice);

        if (!isNaN(listPrice) && !isNaN(salePrice) && listPrice > 0 && salePrice > 0) {
          if (listPrice <= salePrice) {
            ctx.addIssue({
              code: z.ZodIssueCode.custom,
              message: 'Giá niêm yết phải lớn hơn giá bán',
              path: ['listPrice'],
            });
          }
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<ExtendedFileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State để track ảnh bị xóa
  const [deletedImages, setDeletedImages] = useState<Array<{ key: string; url: string }>>([]);

  // State để track ảnh classification bị xóa
  const [deletedClassificationImages, setDeletedClassificationImages] = useState<Array<{
    classificationId: number;
    key: string;
    url: string;
  }>>([]);

  // State để track inventory bị xóa
  const [deletedInventories, setDeletedInventories] = useState<Array<{
    inventoryId: string;
    warehouseId: number;
  }>>([]);

  // State để track classification bị xóa
  const [deletedClassifications, setDeletedClassifications] = useState<Array<{
    classificationId: number;
    type: string;
  }>>([]);

  // State cho phân loại sản phẩm
  const [productClassifications, setProductClassifications] = useState<ProductVariant[]>([]);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho inventory
  const [productInventories, setProductInventories] = useState<ProductInventory[]>([]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Note: Cache invalidation is handled by useUpdateProduct hook

  // Reset state khi product thay đổi để đảm bảo form clean
  useEffect(() => {
    console.log('🔄 [PhysicalProductEditForm] Product changed, resetting form state:', product?.id);
    setMediaFiles([]);
    setProductClassifications([]);
    setProductCustomFields([]);
    setProductInventories([]);
    setDeletedImages([]);
    setDeletedClassificationImages([]);
    setDeletedInventories([]);
    setDeletedClassifications([]);
    setTempTags([]);
  }, [product?.id]);

  // Khởi tạo giá trị mặc định từ product data
  const defaultValues = useMemo((): PhysicalProductEditFormValues => {
    if (!product) return {
      name: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      description: '',
      tags: [],
      shipmentConfig: {
        lengthCm: '',
        widthCm: '',
        heightCm: '',
        weightGram: '',
      },
      inventory: [],
      customFields: [],
      media: [],
      classifications: [],
    };

    console.log('🔍 [PhysicalProductEditForm] Product data:', product);
    console.log('🏪 [PhysicalProductEditForm] Inventory data:', product.inventory);
    console.log('💰 [PhysicalProductEditForm] Price data:', product.price);
    console.log('📦 [PhysicalProductEditForm] Classifications data:', product.classifications);
    console.log('🏷️ [PhysicalProductEditForm] Custom fields data:', product.metadata?.customFields);

    // Xử lý giá sản phẩm từ API response
    let listPrice = '';
    let salePrice = '';
    let currency = 'VND';

    if (product.price && typeof product.price === 'object') {
      const priceData = product.price as HasPriceDto;
      listPrice = priceData.listPrice?.toString() || '';
      salePrice = priceData.salePrice?.toString() || '';
      currency = priceData.currency || 'VND';
    }

    return {
      name: product.name || '',
      listPrice,
      salePrice,
      currency,
      description: product.description || '',
      tags: product.tags || [],
      shipmentConfig: {
        lengthCm: product.shipmentConfig?.lengthCm?.toString() || '',
        widthCm: product.shipmentConfig?.widthCm?.toString() || '',
        heightCm: product.shipmentConfig?.heightCm?.toString() || '',
        weightGram: product.shipmentConfig?.weightGram?.toString() || '',
      },
      inventory: [],
      customFields: [],
      media: [],
      classifications: [],
    };
  }, [product]);

  // Sync tags với state khi product thay đổi
  useEffect(() => {
    if (product.tags) {
      setTempTags(product.tags);
    }
  }, [product]);

  // Sync existing images với state khi product thay đổi
  useEffect(() => {
    if (product?.images && product.images.length > 0) {
      const productImages = product.images;
      console.log('🔄 [PhysicalProductEditForm] Syncing existing images:', productImages);

      // Convert existing images to ExtendedFileWithMetadata format for display
      const existingImages: ExtendedFileWithMetadata[] = productImages.map((img: { key: string; url: string; position?: number }, index: number) => {
        // Extract file name from URL or use a default name
        const urlParts = img.url.split('/');
        const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `image-${index + 1}`;

        console.log(`📷 [PhysicalProductEditForm] Processing image ${index}:`, {
          key: img.key,
          url: img.url,
          fileName,
          position: img.position,
        });

        const existingImageFile = {
          id: `existing-${img.key}`,
          file: new File([], fileName, { type: 'image/jpeg' }), // Placeholder file with proper name
          preview: img.url, // This is the key - set preview to the image URL for display
          // Thêm metadata để track key và url cho việc xóa
          metadata: {
            key: img.key,
            url: img.url,
            isExisting: true,
          },
        };

        console.log(`📷 [PhysicalProductEditForm] Created existing image file:`, {
          id: existingImageFile.id,
          fileName,
          hasMetadata: !!existingImageFile.metadata,
          key: img.key,
        });

        return existingImageFile;
      });

      // Cải thiện logic sync để tránh race condition và đảm bảo preview URL được cập nhật đúng
      setMediaFiles(prev => {
        console.log('🔄 [PhysicalProductEditForm] Syncing existing images:', {
          existingImagesFromAPI: existingImages.length,
          currentExistingInState: prev.filter(file => file.id.startsWith('existing-')).length,
          currentNewInState: prev.filter(file => !file.id.startsWith('existing-')).length,
          totalInState: prev.length,
        });

        // Nếu chưa có ảnh existing nào, set toàn bộ
        const currentExistingCount = prev.filter(file => file.id.startsWith('existing-')).length;

        if (currentExistingCount === 0) {
          console.log('📷 [PhysicalProductEditForm] No existing images in state, setting all from API');
          return existingImages;
        }

        // Luôn cập nhật preview URL từ API để đảm bảo hiển thị đúng sau khi upload
        // Tạo map để tra cứu nhanh existing images từ API
        const apiImageMap = new Map(existingImages.map(img => [img.id, img]));

        const result: ExtendedFileWithMetadata[] = prev.map(file => {
          if (file.id.startsWith('existing-')) {
            const apiImage = apiImageMap.get(file.id);
            if (apiImage) {
              // Luôn cập nhật preview URL từ API để đảm bảo hiển thị đúng
              return {
                ...file,
                preview: apiImage.preview, // Luôn dùng URL mới từ API
                metadata: {
                  ...file.metadata, 
                  url: apiImage.metadata?.url || file.metadata?.url, // Cập nhật URL mới
                  isExisting: true,
                },
              } as ExtendedFileWithMetadata;
            }
          }
          return file;
        });

        // Thêm các ảnh mới từ API mà chưa có trong state
        const existingIds = new Set(prev.filter(f => f.id.startsWith('existing-')).map(f => f.id));
        const newApiImages = existingImages.filter(img => !existingIds.has(img.id));

        if (newApiImages.length > 0) {
          console.log('📷 [PhysicalProductEditForm] Adding new images from API:', newApiImages.length);
          result.push(...newApiImages);
        }

        // Giữ lại ảnh mới (chưa upload)
        const newFiles = prev.filter(file => !file.id.startsWith('existing-'));
        result.push(...newFiles);

        console.log('📷 [PhysicalProductEditForm] Updated existing images with fresh URLs from API');
        return result;
      });
    } else {
      // Nếu không có ảnh từ API, chỉ giữ lại ảnh mới
      setMediaFiles(prev => prev.filter(file => !file.id.startsWith('existing-')));
    }
  }, [product]);

  // Sync existing custom fields với state khi product thay đổi
  useEffect(() => {
    if (product?.metadata?.customFields && product.metadata.customFields.length > 0) {
      const existingCustomFields: SelectedCustomField[] = product.metadata.customFields.map((field: {
        id: number;
        tags: string[];
        type: string;
        label: string;
        value: Record<string, unknown>;
        configId: string;
        required: boolean;
        configJson: Record<string, unknown>;
      }) => {
        return {
          id: field.id,
          fieldId: field.id, // Sử dụng id làm fieldId
          configId: field.configId || '',
          label: field.label,
          component: field.type, // component = type từ API
          type: field.type,
          required: field.required || false,
          configJson: field.configJson || {},
          value: field.value?.['value'] ? { value: field.value['value'] } : { value: '' },
        };
      });

      setProductCustomFields(existingCustomFields);
    } else {
      setProductCustomFields([]);
    }
  }, [product?.metadata?.customFields]);

  // Sync existing inventory với state khi product thay đổi
  useEffect(() => {
    if (product.inventory) {
      console.log('🔄 [PhysicalProductEditForm] Syncing inventory from API:', product.inventory);

      try {
        // Kiểm tra xem inventory là array hay object
        if (Array.isArray(product.inventory)) {
          // Nếu là array, convert từng item
          const existingInventories: ProductInventory[] = (product.inventory as unknown as Record<string, unknown>[]).map((inv: Record<string, unknown>, index: number) => {
            // Lấy thông tin warehouse từ API response
            const warehouseInfo = inv['warehouse'] as Record<string, unknown> | undefined;

            return {
              id: inv['id'] ? inv['id'].toString() : (Date.now() + index).toString(), // Sử dụng ID thật từ backend nếu có
              warehouseId: inv['warehouseId']?.toString() || '',
              availableQuantity: inv['availableQuantity']?.toString() || '',
              sku: inv['sku'] as string || '',
              barcode: inv['barcode'] as string || '',
              // Thêm thông tin warehouse để hiển thị tên kho
              warehouse: warehouseInfo ? {
                id: inv['warehouseId'] as number,
                name: warehouseInfo['name'] as string,
                description: warehouseInfo['description'] as string,
                type: warehouseInfo['type'] as string,
                address: warehouseInfo['address'] as string,
                capacity: warehouseInfo['capacity'] as number,
              } : undefined,
            };
          });

          setProductInventories(existingInventories);
        } else {
          // Nếu là object, convert thành array với 1 item
          const inventory = product.inventory as Record<string, unknown>;
          const warehouseInfo = inventory['warehouse'] as Record<string, unknown> | undefined;

          const existingInventory: ProductInventory = {
            id: inventory['id'] ? inventory['id'].toString() : Date.now().toString(), // Sử dụng ID thật từ backend nếu có
            warehouseId: inventory['warehouseId']?.toString() || '',
            availableQuantity: inventory['availableQuantity']?.toString() || '',
            sku: inventory['sku'] as string || '',
            barcode: inventory['barcode'] as string || '',
            // Thêm thông tin warehouse để hiển thị tên kho
            warehouse: warehouseInfo ? {
              id: inventory['warehouseId'] as number,
              name: warehouseInfo['name'] as string,
              description: warehouseInfo['description'] as string,
              type: warehouseInfo['type'] as string,
              address: warehouseInfo['address'] as string,
              capacity: warehouseInfo['capacity'] as number,
            } : undefined,
          };

          setProductInventories([existingInventory]);
        }
      } catch (error) {
        console.error('❌ [PhysicalProductEditForm] Error syncing inventory:', error);
        setProductInventories([]);
      }
    } else {
      setProductInventories([]);
    }
  }, [product.inventory]);

  // Sync existing classifications với state khi product thay đổi
  useEffect(() => {
    if (product.classifications && product.classifications.length > 0) {
      console.log('🔄 [PhysicalProductEditForm] Syncing classifications from API:', product.classifications);

      const existingClassifications: ProductVariant[] = product.classifications.map((classification: {
        id: number;
        type: string;
        description?: string;
        price?: {
          listPrice?: number;
          salePrice?: number;
          currency?: string;
        };
        customFields?: Array<Record<string, unknown>>;
      }) => {
        // Convert classification images từ imagesMediaTypes array
        const classificationData = classification as unknown as Record<string, unknown>;
        const imagesMediaTypes = classificationData['imagesMediaTypes'] as Array<Record<string, unknown>> | undefined;
        const classificationImages: ExtendedFileWithMetadata[] = imagesMediaTypes?.map((img: Record<string, unknown>, index: number) => {
          // Extract file name from URL or use a default name
          const imgUrl = img['url'] as string;
          const imgKey = img['key'] as string;
          const urlParts = imgUrl.split('/');
          const fileName = urlParts[urlParts.length - 1]?.split('?')[0] || `classification-image-${index + 1}`;

          const classificationImageFile = {
            id: `classification-${classification.id}-existing-${imgKey}`,
            file: new File([], fileName, { type: 'image/jpeg' }),
            preview: imgUrl, // Set preview to the image URL for display
            metadata: {
              key: imgKey,
              url: imgUrl,
              isExisting: true,
            },
          };

          return classificationImageFile;
        }) || [];

        // Convert classification custom fields
        const classificationCustomFields: SelectedCustomField[] = classification.customFields?.map((field: Record<string, unknown>) => {
          const customFieldId = field['customFieldId'] as number;
          const fieldValue = field['value'];
          return {
            id: Date.now() + Math.random(), // Generate temporary ID
            fieldId: customFieldId,
            label: `Custom Field ${customFieldId}`, // We might need to fetch field details
            component: 'text', // Default component type
            type: 'text', // Default type
            required: false,
            configJson: {},
            value: typeof fieldValue === 'object' && fieldValue !== null ? fieldValue as Record<string, unknown> : { value: fieldValue },
          };
        }) || [];

        return {
          id: classification.id,
          name: classification.type,
          description: classification.description || '',
          listPrice: classification.price?.listPrice?.toString() || '',
          salePrice: classification.price?.salePrice?.toString() || '',
          currency: classification.price?.currency || 'VND',
          sku: '', // Not available in API response for physical products
          availableQuantity: '', // Not available in API response for physical products
          images: classificationImages,
          customFields: classificationCustomFields,
        };
      });

      // Cải thiện logic sync classifications để đảm bảo ảnh được cập nhật đúng
      setProductClassifications(prev => {
        if (prev.length === 0) {
          console.log('📦 [PhysicalProductEditForm] No existing classifications in state, setting all from API');
          return existingClassifications;
        }

        if (prev.length !== existingClassifications.length) {
          console.log('📦 [PhysicalProductEditForm] Classification count changed, updating all from API');
          return existingClassifications;
        }

        // Cập nhật từng classification, luôn ưu tiên ảnh từ API để đảm bảo hiển thị đúng
        return prev.map(prevVariant => {
          const apiVariant = existingClassifications.find(api => api.id === prevVariant.id);
          if (apiVariant) {
            // Luôn cập nhật ảnh từ API để đảm bảo hiển thị đúng sau upload
            // Chỉ giữ lại ảnh mới (chưa upload) từ state
            const newImagesFromState = prevVariant.images?.filter(img =>
              !img.id.startsWith('classification-') && !img.metadata?.isExisting
            ) || [];

            const allImages = [...(apiVariant.images || []), ...newImagesFromState];

            console.log(`📦 [PhysicalProductEditForm] Updating classification ${prevVariant.id}:`, {
              apiImages: apiVariant.images?.length || 0,
              newImages: newImagesFromState.length,
              totalImages: allImages.length,
            });

            return {
              ...apiVariant,
              images: allImages,
            } as ProductVariant;
          }
          return prevVariant;
        });
      });
    } else {
      setProductClassifications([]);
    }
  }, [product]);



  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    try {
      const formValues = values as PhysicalProductEditFormValues;
      setIsUploading(true);

      // Validate giá trước khi tạo request
      try {
        getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo request body theo format mới
      const productData: UpdateProductDto = {
        name: formValues.name,
        price: {
          listPrice: Number(formValues.listPrice),
          salePrice: Number(formValues.salePrice),
          currency: formValues.currency,
        },
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (tempTags && tempTags.length > 0) {
        productData.tags = tempTags;
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
          const fieldValue = field.value?.['value'];

          // Nếu là undefined hoặc null thì loại bỏ
          if (fieldValue === undefined || fieldValue === null) {
            return false;
          }

          // Nếu là string rỗng thì loại bỏ
          if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
            return false;
          }

          // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
          return true;
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'],
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Xử lý ảnh theo format imageOperations: DELETE existing images + ADD new images
      const imageOperations: Array<{
        operation: 'DELETE' | 'ADD';
        key?: string;
        mimeType?: string;
      }> = [];

      // Thêm operations DELETE cho ảnh bị xóa
      if (deletedImages.length > 0) {
        deletedImages.forEach(deletedImage => {
          imageOperations.push({
            operation: 'DELETE',
            key: deletedImage.key,
          });
        });
      }

      // Thêm operations ADD cho ảnh mới
      const newMediaFiles = mediaFiles.filter(file => !file.id.startsWith('existing-'));
      if (newMediaFiles.length > 0) {
        newMediaFiles.forEach((file) => {
          imageOperations.push({
            operation: 'ADD',
            mimeType: file.file.type || 'image/jpeg',
          });
        });
      }

      // Chỉ thêm imageOperations vào request nếu có operations
      if (imageOperations.length > 0) {
        (productData as Record<string, unknown>)['imageOperations'] = imageOperations;
      }

      // Thêm shipmentConfig nếu có
      const shipmentConfig = getShipmentConfig(formValues);
      if (shipmentConfig && Object.keys(shipmentConfig).length > 0) {
        (productData as Record<string, unknown>)['shipmentConfig'] = shipmentConfig;
      }

      // Xử lý inventory operations: ADD, UPDATE, DELETE
      const inventoryOperations: Array<{
        inventoryId?: string;
        operation: 'ADD' | 'UPDATE' | 'DELETE';
        warehouseId: number;
        availableQuantity?: number;
        sku?: string;
        barcode?: string;
      }> = [];

      // 1. Thêm operations cho inventory hiện tại (ADD/UPDATE)
      const validInventories = productInventories
        .filter(inventory => inventory.warehouseId)
        .map(inventory => {
          const inventoryData: {
            inventoryId?: string;
            operation: 'ADD' | 'UPDATE' | 'DELETE';
            warehouseId: number;
            availableQuantity?: number;
            sku?: string;
            barcode?: string;
          } = {
            warehouseId: Number(inventory.warehouseId),
            operation: 'ADD', // Default operation
          };

          // Determine operation based on inventoryId existence
          // inventoryId từ backend (có sẵn) vs không có inventoryId (mới tạo)
          const hasInventoryId = inventory.id && inventory.id.toString().trim() !== '';
          const isExistingInventory = hasInventoryId && !inventory.id.toString().startsWith('temp-');

          if (isExistingInventory) {
            // Inventory có sẵn từ backend
            inventoryData.inventoryId = inventory.id.toString();
            inventoryData.operation = 'UPDATE';
            console.log(`🔄 [PhysicalProductEditForm] Existing inventory ${inventory.id} - operation: UPDATE`);
          } else {
            // Inventory mới được thêm bởi user (không gửi inventoryId tạm thời lên backend)
            inventoryData.operation = 'ADD';
            console.log(`➕ [PhysicalProductEditForm] New inventory for warehouse ${inventory.warehouseId} - operation: ADD`);
          }

          if (inventory.availableQuantity) {
            const quantity = Number(inventory.availableQuantity);
            if (!isNaN(quantity) && quantity >= 0) {
              inventoryData.availableQuantity = quantity;
            }
          }

          if (inventory.sku && inventory.sku.trim()) {
            inventoryData.sku = inventory.sku.trim();
          }

          if (inventory.barcode && inventory.barcode.trim()) {
            inventoryData.barcode = inventory.barcode.trim();
          }

          return inventoryData;
        });

      // Thêm valid inventories vào operations
      inventoryOperations.push(...validInventories);

      // 2. Thêm operations DELETE cho inventory bị xóa
      if (deletedInventories.length > 0) {
        console.log('🗑️ [PhysicalProductEditForm] Processing deleted inventories:', deletedInventories);

        deletedInventories.forEach(deletedInventory => {
          inventoryOperations.push({
            inventoryId: deletedInventory.inventoryId,
            operation: 'DELETE',
            warehouseId: deletedInventory.warehouseId,
          });
          console.log(`🗑️ [PhysicalProductEditForm] Deleted inventory ${deletedInventory.inventoryId} - operation: DELETE`);
        });
      }

      // Chỉ thêm inventory operations nếu có operations
      if (inventoryOperations.length > 0) {
        (productData as Record<string, unknown>)['inventory'] = inventoryOperations;
        console.log('📦 [PhysicalProductEditForm] Total inventory operations:', inventoryOperations.length);
      }

      // Xử lý classification operations: ADD, UPDATE, DELETE
      const classificationOperations: Array<{
        id?: number;
        type: string;
        description?: string;
        operation: 'ADD' | 'UPDATE' | 'DELETE';
        price?: {
          listPrice?: number;
          salePrice?: number;
          currency?: string;
        };
        customFields?: Array<{
          customFieldId: number;
          value: {
            value: unknown;
          };
        }>;
        imageOperations?: Array<{
          operation: 'DELETE' | 'ADD';
          key?: string;
          mimeType?: string;
        }>;
      }> = [];

      // 1. Thêm operations cho classification hiện tại (ADD/UPDATE)
      if (productClassifications.length > 0) {
        console.log('📦 [PhysicalProductEditForm] Processing classifications:', productClassifications);

        const validClassifications = productClassifications.map(variant => {
          const classification: {
            id?: number;
            type: string;
            description?: string;
            operation: 'ADD' | 'UPDATE' | 'DELETE';
            price?: {
              listPrice?: number;
              salePrice?: number;
              currency?: string;
            };
            customFields?: Array<{
              customFieldId: number;
              value: {
                value: unknown;
              };
            }>;
            imageOperations?: Array<{
              operation: 'DELETE' | 'ADD';
              key?: string;
              mimeType?: string;
            }>;
          } = {
            type: variant.name,
            operation: 'ADD', // Default operation
          };

          // Determine operation based on ID type
          // ID từ backend (số thật từ database) vs ID tạm thời (từ Date.now())
          const isExistingClassification = variant.id && typeof variant.id === 'number' && variant.id < 1000000000000; // ID < 1 trillion (before 2001) = real DB ID

          if (isExistingClassification) {
            // Biến thể có sẵn từ backend
            classification.id = variant.id;
            classification.operation = 'UPDATE';
            console.log(`🔄 [PhysicalProductEditForm] Existing classification ${variant.id} - operation: UPDATE`);
          } else {
            // Biến thể mới được thêm bởi user (không gửi ID tạm thời lên backend)
            classification.operation = 'ADD';
            console.log(`➕ [PhysicalProductEditForm] New classification "${variant.name}" - operation: ADD`);
          }

          // Add description if it exists
          if (variant.description && variant.description.trim()) {
            classification.description = variant.description.trim();
          }

          // Add price information
          if (variant.listPrice || variant.salePrice || variant.currency) {
            classification.price = {};

            // Only add properties if they have valid values
            const listPriceNum = Number(variant.listPrice);
            if (!isNaN(listPriceNum) && listPriceNum > 0) {
              classification.price.listPrice = listPriceNum;
            }

            const salePriceNum = Number(variant.salePrice);
            if (!isNaN(salePriceNum) && salePriceNum > 0) {
              classification.price.salePrice = salePriceNum;
            }

            if (variant.currency && variant.currency.trim()) {
              classification.price.currency = variant.currency.trim();
            }
          }

          // Add custom fields
          const filteredCustomFields = variant.customFields
            .filter(field => {
              // Filter out fields with empty values, nhưng giữ lại số 0 và boolean false
              const fieldValue = field.value?.['value'];

              // Nếu là undefined hoặc null thì loại bỏ
              if (fieldValue === undefined || fieldValue === null) {
                return false;
              }

              // Nếu là string rỗng thì loại bỏ
              if (typeof fieldValue === 'string' && fieldValue.trim() === '') {
                return false;
              }

              // Giữ lại số 0 và boolean false vì chúng là giá trị hợp lệ
              return true;
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: {
                value: field.value?.['value'],
              },
            }));

          if (filteredCustomFields.length > 0) {
            classification.customFields = filteredCustomFields;
          }

          // Xử lý imageOperations cho classification images
          const classificationImageOperations: Array<{
            operation: 'DELETE' | 'ADD';
            key?: string;
            mimeType?: string;
          }> = [];

          // Thêm operations DELETE cho ảnh classification bị xóa
          const deletedImagesForThisClassification = deletedClassificationImages.filter(
            img => img.classificationId === variant.id
          );

          if (deletedImagesForThisClassification.length > 0) {
            console.log(`🗑️ [PhysicalProductEditForm] Processing deleted classification images for variant ${variant.id}:`, deletedImagesForThisClassification);
            deletedImagesForThisClassification.forEach(deletedImage => {
              classificationImageOperations.push({
                operation: 'DELETE',
                key: deletedImage.key,
              });
            });
          }

          // Thêm operations ADD cho ảnh mới của classification
          if (variant.images && variant.images.length > 0) {
            const newVariantImages = variant.images.filter(file =>
              !file.id.startsWith('existing-') &&
              !file.id.startsWith('classification-') &&
              !file.metadata?.isExisting  
            );

            if (newVariantImages.length > 0) {
              console.log(`📷 [PhysicalProductEditForm] Processing new classification images for variant ${variant.id}:`, newVariantImages);
              newVariantImages.forEach((file) => {
                classificationImageOperations.push({
                  operation: 'ADD',
                  mimeType: file.file.type || 'image/jpeg',
                });
              });
            }
          }

          // Chỉ thêm imageOperations nếu có operations
          if (classificationImageOperations.length > 0) {
            classification.imageOperations = classificationImageOperations;
          }

          return classification;
        });

        // Thêm valid classifications vào operations
        classificationOperations.push(...validClassifications);
      }

      // 2. Thêm operations DELETE cho classification bị xóa
      if (deletedClassifications.length > 0) {
        console.log('🗑️ [PhysicalProductEditForm] Processing deleted classifications:', deletedClassifications);

        deletedClassifications.forEach(deletedClassification => {
          classificationOperations.push({
            id: deletedClassification.classificationId,
            operation: 'DELETE',
            type: deletedClassification.type,
          });
          console.log(`🗑️ [PhysicalProductEditForm] Deleted classification ${deletedClassification.classificationId} - operation: DELETE`);
        });
      }

      // Chỉ thêm classification operations nếu có operations
      if (classificationOperations.length > 0) {
        (productData as Record<string, unknown>)['classifications'] = classificationOperations;
        console.log('📦 [PhysicalProductEditForm] Total classification operations:', classificationOperations.length);
      }

      // Gọi API cập nhật sản phẩm
      const response = await updateProductMutation.mutateAsync({
        id: product.id,
        data: productData,
      });

      const newMediaFilesForUpload = mediaFiles.filter(file => !file.id.startsWith('existing-'));

      if (newMediaFilesForUpload.length > 0) {
        try {
          // Kiểm tra response structure - có thể có wrapper 'result' hoặc trực tiếp
          let responseData: Record<string, unknown> = response as unknown as Record<string, unknown>;
          if (response && typeof response === 'object' && 'result' in response) {
            responseData = response.result as Record<string, unknown>;
          }

          // Kiểm tra xem response có imagesUploadUrls không (trực tiếp trong result)
          const hasUploadUrls =
            responseData &&
            typeof responseData === 'object' &&
            'imagesUploadUrls' in responseData &&
            Array.isArray(responseData['imagesUploadUrls']);

          if (hasUploadUrls) {
            const uploadUrls = responseData['imagesUploadUrls'] as Array<Record<string, unknown>>;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa new media files và upload URLs từ backend
              const uploadTasks = newMediaFilesForUpload.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                };
              });
              // Tạo array các file và URLs để upload cùng lúc
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh mới cùng lúc, skip cache invalidation trong hook
              // Cache invalidation đã được xử lý trong useUpdateProduct hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Cập nhật state để reflect ảnh sản phẩm chính đã được upload thành công
              // Chuyển ảnh từ trạng thái "uploading" sang "existing" với key từ API
              // Không cập nhật state ngay lập tức để tránh race condition với cache invalidation
              // Cache invalidation sẽ trigger useEffect để sync lại data từ API
              console.log('✅ [PhysicalProductEditForm] Images uploaded successfully, cache will be invalidated to refresh data');

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            } else {
              NotificationUtil.warning({
                message: t(
                  'business:product.mediaUploadWarning',
                  'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'
                ),
                duration: 5000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được cập nhật nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // 2. Upload ảnh cho các phân loại (classifications)
      // Xử lý response structure mới với classificationUploadUrls
      let responseData: Record<string, unknown> = response as unknown as Record<string, unknown>;
      if (response && typeof response === 'object' && 'result' in response) {
        responseData = response.result as Record<string, unknown>;
      }

      // Kiểm tra xem có classificationUploadUrls không (trực tiếp trong result)
      const hasClassificationUploadUrls =
        responseData &&
        typeof responseData === 'object' &&
        'classificationUploadUrls' in responseData &&
        Array.isArray(responseData['classificationUploadUrls']);

      let classificationUploadUrls: Array<Record<string, unknown>> | null = null;

      if (hasClassificationUploadUrls) {
        classificationUploadUrls = responseData['classificationUploadUrls'] as Array<Record<string, unknown>>;
      }

      if (classificationUploadUrls && classificationUploadUrls.length > 0) {
        console.log('📷 [PhysicalProductEditForm] Processing classification upload URLs:', classificationUploadUrls);

        // Xử lý từng classification upload URL
        for (const classificationUpload of classificationUploadUrls) {
          const classificationId = classificationUpload['classificationId'] as string | number;
          const imagesUploadUrls = classificationUpload['imagesUploadUrls'] as Array<Record<string, unknown>>;

          console.log('🔍 [PhysicalProductEditForm] Processing classification upload:', {
            classificationId,
            classificationIdType: typeof classificationId,
            imagesUploadUrlsLength: imagesUploadUrls?.length,
            availableVariants: productClassifications.map(v => ({ id: v.id, idType: typeof v.id, name: v.name }))
          });

          if (!classificationId || !imagesUploadUrls || !Array.isArray(imagesUploadUrls)) {
            console.error('❌ [PhysicalProductEditForm] Invalid classification upload structure:', classificationUpload);
            continue;
          }

          // Tìm variant tương ứng với classificationId (so sánh cả string và number)
          const targetVariant = productClassifications.find(variant =>
            variant.id == classificationId || variant.id.toString() === classificationId.toString()
          );

          if (!targetVariant) {
            console.error(`❌ [PhysicalProductEditForm] Variant not found for classificationId ${classificationId}`);
            console.log('Available variants:', productClassifications.map(v => ({ id: v.id, name: v.name })));
            continue;
          }

          console.log('✅ [PhysicalProductEditForm] Found target variant:', {
            variantId: targetVariant.id,
            variantName: targetVariant.name,
            imagesCount: targetVariant.images?.length || 0,
            images: targetVariant.images?.map(img => ({ id: img.id, isExisting: img.metadata?.isExisting }))
          });

          // Lấy ảnh mới cần upload cho variant này
          const newVariantImages = targetVariant.images?.filter(file => {
            const isNew = !file.id.startsWith('existing-') &&
              !file.id.startsWith('classification-') &&
              !file.metadata?.isExisting;

            console.log(`🖼️ [PhysicalProductEditForm] Image ${file.id}: isNew=${isNew}`, {
              startsWithExisting: file.id.startsWith('existing-'),
              startsWithClassification: file.id.startsWith('classification-'),
              isExisting: file.metadata?.isExisting
            });

            return isNew;
          }) || [];

          console.log(`📷 [PhysicalProductEditForm] Found ${newVariantImages.length} new images to upload for classification ${classificationId}`);

          if (newVariantImages.length === 0) {
            console.log(`📷 [PhysicalProductEditForm] No new images to upload for classification ${classificationId}`);
            continue;
          }

          // Upload từng ảnh với URL tương ứng
          console.log(`🚀 [PhysicalProductEditForm] Starting upload for ${newVariantImages.length} images with ${imagesUploadUrls.length} URLs`);

          for (let i = 0; i < Math.min(newVariantImages.length, imagesUploadUrls.length); i++) {
            const file = newVariantImages[i];
            const uploadInfo = imagesUploadUrls[i];

            console.log(`📤 [PhysicalProductEditForm] Processing image ${i + 1}/${newVariantImages.length}:`, {
              fileId: file?.id,
              fileName: file?.file?.name,
              fileSize: file?.file?.size,
              uploadUrl: uploadInfo?.['url'] ? 'Present' : 'Missing',
              uploadKey: uploadInfo?.['key'],
              uploadIndex: uploadInfo?.['index']
            });

            if (!file || !uploadInfo || !uploadInfo['url'] || !uploadInfo['key']) {
              console.error(`❌ [PhysicalProductEditForm] Invalid file or upload info for classification ${classificationId}, image ${i}:`, {
                file: file ? { id: file.id, hasFile: !!file.file } : null,
                uploadInfo
              });
              continue;
            }

            try {
              console.log(`📷 [PhysicalProductEditForm] Starting upload for classification image:`, {
                classificationId,
                fileName: file.file.name,
                fileSize: file.file.size,
                uploadUrl: uploadInfo['url'].toString().substring(0, 100) + '...',
                key: uploadInfo['key'],
                index: uploadInfo['index'],
              });

              // Upload ảnh vào TaskQueue
              await uploadProductImages([{
                file: file.file,
                id: `classification_${classificationId}_${Date.now()}_${i}`,
              }], [uploadInfo['url'] as string], {
                skipCacheInvalidation: true,
              });

              console.log(`✅ [PhysicalProductEditForm] Classification image uploaded successfully for classification ${classificationId}, image ${i + 1}`);

            } catch (uploadError) {
              console.error(`❌ [PhysicalProductEditForm] Error uploading classification image ${i + 1}:`, uploadError);
              NotificationUtil.warning({
                message: t(
                  'business:product.variantImageUploadError',
                  `Có lỗi xảy ra khi tải lên ảnh phân loại ${classificationId} - ảnh ${i + 1}`
                ),
                duration: 5000,
              });
            }
          }

          console.log(`🏁 [PhysicalProductEditForm] Finished processing classification ${classificationId}`);
        }

        // Hiển thị thông báo thành công nếu có ảnh được upload
        const totalNewImages = productClassifications.reduce((total, variant) => {
          const newImages = variant.images?.filter(file =>
            !file.id.startsWith('existing-') &&
            !file.id.startsWith('classification-') &&
            !file.metadata?.isExisting
          ) || [];
          return total + newImages.length;
        }, 0);

        if (totalNewImages > 0) {
          NotificationUtil.success({
            message: t(
              'business:product.variantImageUploadSuccess',
              'Tải lên ảnh phân loại thành công'
            ),
            duration: 3000,
          });
        }
      }

      setIsUploading(false);

      // Reset deletedImages, deletedClassificationImages, deletedInventories và deletedClassifications sau khi update thành công
      setDeletedImages([]);
      setDeletedClassificationImages([]);
      setDeletedInventories([]);
      setDeletedClassifications([]);

      // Force refresh product data để đảm bảo ảnh hiển thị đúng
      // Thêm delay nhỏ để đảm bảo backend đã xử lý xong
      setTimeout(async () => {
        try {
          // Product data will be refreshed automatically by React Query
          console.log('🔄 [PhysicalProductEditForm] Product data refreshed after upload');
        } catch (error) {
          console.error('❌ [PhysicalProductEditForm] Error refreshing product data:', error);
        }
      }, 1000);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('business:product.updateSuccess', 'Cập nhật sản phẩm vật lý thành công'),
        duration: 3000,
      });

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      setIsUploading(false);
      // Kiểm tra nếu là lỗi validation
      if (error && typeof error === 'object' && 'issues' in error) {
        NotificationUtil.error({
          message: t('business:product.validationError', 'Lỗi validation dữ liệu'),
          duration: 3000,
        });
      } else {
        NotificationUtil.error({
          message: t('business:product.updateError', 'Có lỗi xảy ra khi cập nhật sản phẩm vật lý'),
          duration: 3000,
        });
      }
    }
  };

  // Hàm lấy dữ liệu giá cố định
  const getPriceData = (values: PhysicalProductEditFormValues): HasPriceDto => {
    // Kiểm tra đầy đủ các trường bắt buộc
    if (!values.listPrice || values.listPrice === '') {
      throw new Error('Vui lòng nhập giá niêm yết');
    }

    if (!values.salePrice || values.salePrice === '') {
      throw new Error('Vui lòng nhập giá bán');
    }

    if (!values.currency || values.currency.trim() === '') {
      throw new Error('Vui lòng chọn đơn vị tiền tệ');
    }

    const listPrice = Number(values.listPrice);
    const salePrice = Number(values.salePrice);

    if (isNaN(listPrice) || listPrice < 0) {
      throw new Error('Giá niêm yết phải là số >= 0');
    }

    if (isNaN(salePrice) || salePrice < 0) {
      throw new Error('Giá bán phải là số >= 0');
    }

    // Kiểm tra giá niêm yết phải lớn hơn giá bán
    if (listPrice <= salePrice) {
      throw new Error('Giá niêm yết phải lớn hơn giá bán');
    }

    return {
      listPrice,
      salePrice,
      currency: values.currency.trim(),
    };
  };

  // Hàm lấy dữ liệu cấu hình vận chuyển
  const getShipmentConfig = (values: PhysicalProductEditFormValues) => {
    if (!values.shipmentConfig) return undefined;

    const config = values.shipmentConfig;
    const hasAnyValue = config.lengthCm || config.widthCm || config.heightCm || config.weightGram;

    if (!hasAnyValue) return undefined;

    const result: {
      lengthCm?: number;
      widthCm?: number;
      heightCm?: number;
      weightGram?: number;
    } = {};

    if (config.lengthCm) {
      const lengthCm = Number(config.lengthCm);
      if (!isNaN(lengthCm) && lengthCm > 0) {
        result.lengthCm = lengthCm;
      }
    }

    if (config.widthCm) {
      const widthCm = Number(config.widthCm);
      if (!isNaN(widthCm) && widthCm > 0) {
        result.widthCm = widthCm;
      }
    }

    if (config.heightCm) {
      const heightCm = Number(config.heightCm);
      if (!isNaN(heightCm) && heightCm > 0) {
        result.heightCm = heightCm;
      }
    }

    if (config.weightGram) {
      const weightGram = Number(config.weightGram);
      if (!isNaN(weightGram) && weightGram > 0) {
        result.weightGram = weightGram;
      }
    }

    return result;
  };

  // Handler để xử lý khi ảnh media bị xóa hoặc thêm mới
  const handleMediaFilesChange = useCallback((files: ExtendedFileWithMetadata[]) => {
    // Tìm những ảnh existing đã bị xóa
    const currentExistingFiles = mediaFiles.filter(file => file.id.startsWith('existing-'));
    const newExistingFiles = files.filter(file => file.id.startsWith('existing-'));

    const removedFiles = currentExistingFiles.filter(
      currentFile => !newExistingFiles.find(newFile => newFile.id === currentFile.id)
    );

    // Thêm vào danh sách ảnh bị xóa
    if (removedFiles.length > 0) {
      const removedImageInfo = removedFiles
        .filter(file => file.metadata?.key && file.metadata?.url)
        .map(file => ({
          key: file.metadata!.key as string,
          url: file.metadata!.url as string,
        }));

      setDeletedImages(prev => [...prev, ...removedImageInfo]);
    }

    // Preserve metadata for existing files khi merge với files mới
    const preservedFiles: ExtendedFileWithMetadata[] = files.map(file => {
      // Nếu là existing file, tìm và preserve metadata từ state hiện tại
      if (file.id.startsWith('existing-')) {
        const existingFile = mediaFiles.find(f => f.id === file.id);
        if (existingFile && existingFile.metadata) {
          return {
            ...file,
            metadata: existingFile.metadata, // Preserve existing metadata
            preview: existingFile.preview || file.preview || '', // Preserve preview URL with fallback
          } as ExtendedFileWithMetadata;
        }
      }
      return file;
    });

    setMediaFiles(preservedFiles);
  }, [mediaFiles]);

  // Handler functions for sections (giống ProductForm)

  // Handlers cho inventory
  const handleAddInventory = useCallback(() => {
    const newInventory: ProductInventory = {
      id: `temp-${Date.now()}`, // Temporary ID for new inventory
      warehouseId: '',
      availableQuantity: '',
      sku: '',
      barcode: '',
    };
    setProductInventories(prev => [...prev, newInventory]);
  }, []);

  const handleRemoveInventory = useCallback((inventoryId: number | string) => {
    // Tìm inventory bị xóa để track nếu là existing inventory
    const inventoryToRemove = productInventories.find(inventory => inventory.id === inventoryId);

    if (inventoryToRemove) {
      // Kiểm tra xem có phải là existing inventory từ backend không
      const isExistingInventory = inventoryToRemove.id &&
        !inventoryToRemove.id.toString().startsWith('temp-') &&
        inventoryToRemove.id.toString().trim() !== '';

      if (isExistingInventory && inventoryToRemove.warehouseId) {
        // Thêm vào danh sách inventory bị xóa để gửi DELETE operation
        const deletedInventory = {
          inventoryId: inventoryToRemove.id.toString(),
          warehouseId: Number(inventoryToRemove.warehouseId),
        };

        setDeletedInventories(prev => [...prev, deletedInventory]);
        console.log('🗑️ [PhysicalProductEditForm] Tracking deleted inventory:', deletedInventory);
      }
    }

    // Xóa inventory khỏi state
    setProductInventories(prev => prev.filter(inventory => inventory.id !== inventoryId));
  }, [productInventories]);

  const handleUpdateInventory = useCallback((inventoryId: number | string, field: string | number | symbol, value: string | number) => {
    // Validation: Không cho duplicate warehouseId
    if (field === 'warehouseId' && value) {
      const isDuplicate = productInventories.some(
        inventory => inventory.id !== inventoryId && inventory.warehouseId === value
      );

      if (isDuplicate) {
        NotificationUtil.warning({
          message: t('business:product.inventory.duplicateWarehouse', 'Kho này đã được chọn cho inventory khác'),
          duration: 3000,
        });
        return;
      }
    }

    setProductInventories(prev =>
      prev.map(inventory => {
        if (inventory.id === inventoryId) {
          const updatedInventory = { ...inventory, [field]: value };

          // Nếu đang cập nhật warehouseId, xóa thông tin warehouse cũ
          // vì thông tin warehouse sẽ được load lại từ API khi user chọn
          if (field === 'warehouseId') {
            delete updatedInventory.warehouse;
          }

          return updatedInventory;
        }
        return inventory;
      })
    );
  }, [productInventories, t]);

  // Handlers cho classifications
  const handleAddVariant = useCallback(() => {
    const newVariant: ProductVariant = {
      id: Date.now(),
      name: '',
      description: '',
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      sku: '',
      availableQuantity: '',
      images: [],
      customFields: [],
    };
    setProductClassifications(prev => [...prev, newVariant]);
  }, []);

  const handleRemoveVariant = useCallback((variantId: number) => {
    // Tìm classification bị xóa để track nếu là existing classification
    const classificationToRemove = productClassifications.find(variant => variant.id === variantId);

    if (classificationToRemove) {
      // Kiểm tra xem có phải là existing classification từ backend không
      const isExistingClassification = classificationToRemove.id &&
        typeof classificationToRemove.id === 'number' &&
        classificationToRemove.id < 1000000000000; // ID < 1 trillion (before 2001) = real DB ID

      if (isExistingClassification) {
        // Thêm vào danh sách classification bị xóa để gửi DELETE operation
        const deletedClassification = {
          classificationId: classificationToRemove.id,
          type: classificationToRemove.name,
        };

        setDeletedClassifications(prev => [...prev, deletedClassification]);
        console.log('🗑️ [PhysicalProductEditForm] Tracking deleted classification:', deletedClassification);
      }
    }

    // Xóa classification khỏi state
    setProductClassifications(prev => prev.filter(variant => variant.id !== variantId));
  }, [productClassifications]);

  const handleUpdateVariant = useCallback(
    (variantId: number, field: string | number | symbol, value: string | number) => {
      let validatedValue = value;
      if (field === 'availableQuantity') {
        const numValue = Number(value);
        if (numValue < 0) {
          validatedValue = 0;
        }
      }

      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, [field]: validatedValue };
          }
          return variant;
        })
      );
    },
    []
  );

  const handleVariantImagesChange = useCallback(
    (variantId: number, files: ExtendedFileWithMetadata[]) => {
      // Tìm variant hiện tại để so sánh ảnh bị xóa
      const currentVariant = productClassifications.find(v => v.id === variantId);
      if (currentVariant && currentVariant.images) {
        const currentExistingFiles = currentVariant.images.filter(file =>
          file.id.startsWith('classification-') && file.metadata?.isExisting
        );
        const newExistingFiles = files.filter(file =>
          file.id.startsWith('classification-') && file.metadata?.isExisting
        );

        const removedFiles = currentExistingFiles.filter(
          currentFile => !newExistingFiles.find(newFile => newFile.id === currentFile.id)
        );

        if (removedFiles.length > 0) {
          const removedImageInfo = removedFiles
            .filter(file => file.metadata?.key && file.metadata?.url)
            .map(file => ({
              classificationId: variantId,
              key: file.metadata!.key as string,
              url: file.metadata!.url as string,
            }));

          setDeletedClassificationImages(prev => [...prev, ...removedImageInfo]);
        }
      }

      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return { ...variant, images: files };
          }
          return variant;
        })
      );
    },
    [productClassifications]
  );

  // Handlers cho custom fields
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const fieldType = (fieldData?.['type'] as string) || 'text';
        const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

        let defaultValue: string | number | boolean = '';
        if (fieldType === 'number' || fieldComponent === 'number') {
          defaultValue = 0;
        } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
          defaultValue = false;
        } else {
          defaultValue = '';
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component: fieldComponent,
          type: fieldType,
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: defaultValue },
        };

        return [...prev, newField];
      });
    },
    []
  );

  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string | number | boolean) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Handlers cho variant custom fields
  const handleToggleCustomFieldToVariant = useCallback(
    (variantId: number, fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            const existingFieldIndex = variant.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              return {
                ...variant,
                customFields: variant.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            const fieldType = (fieldData?.['type'] as string) || 'text';
            const fieldComponent = (fieldData?.['component'] as string) || fieldType || 'text';

            let defaultValue: string | number | boolean = '';
            if (fieldType === 'number' || fieldComponent === 'number') {
              defaultValue = 0;
            } else if (fieldType === 'boolean' || fieldComponent === 'checkbox' || fieldComponent === 'switch') {
              defaultValue = false;
            } else {
              defaultValue = '';
            }

            return {
              ...variant,
              customFields: [
                ...variant.customFields,
                {
                  id: Date.now(),
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component: fieldComponent,
                  type: fieldType,
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: defaultValue },
                },
              ],
            };
          }
          return variant;
        })
      );
    },
    []
  );

  const handleUpdateCustomFieldInVariant = useCallback(
    (variantId: number, customFieldId: number, value: string | number | boolean) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return variant;
        })
      );
    },
    []
  );

  const handleRemoveCustomFieldFromVariant = useCallback(
    (variantId: number, customFieldId: number) => {
      setProductClassifications(prev =>
        prev.map(variant => {
          if (variant.id === variantId) {
            return {
              ...variant,
              customFields: variant.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return variant;
        })
      );
    },
    []
  );





  // Hiển thị loading khi đang cập nhật sản phẩm
  if (updateProductMutation.isPending) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="flex justify-center items-center py-8">
          <Typography variant="body1">
            {t('business:product.form.loadingProduct', 'Đang tải thông tin sản phẩm...')}
          </Typography>
        </div>
      </FormMultiWrapper>
    );
  }

  // Hiển thị lỗi nếu không có product data
  if (!product) {
    return (
      <FormMultiWrapper title={t('business:product.form.editTitle')}>
        <div className="text-center py-8">
          <Typography variant="body1" className="text-red-500">
            {t('business:product.form.loadError', 'Không thể tải thông tin sản phẩm')}
          </Typography>
          <button
            onClick={onCancel}
            className="mt-4 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            {t('common:back', 'Quay lại')}
          </button>
        </div>
      </FormMultiWrapper>
    );
  }



  return (
    <FormMultiWrapper title={t('business:product.form.editPhysicalTitle', 'Chỉnh sửa sản phẩm vật lý')}>
      <Form
        ref={formRef}
        schema={productSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          // Log chi tiết từng field error
          Object.keys(errors).forEach(field => {
            if (errors[field]?.message) {
              console.error(`   Message: ${errors[field].message}`);
            }
            if (errors[field] && typeof errors[field] === 'object' && 'type' in errors[field]) {
              console.error(`   Type: ${(errors[field] as { type: string }).type}`);
            }
          });
          // Hiển thị error đầu tiên để user biết
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';

          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <GeneralInfoSection
          tempTags={tempTags}
          setTempTags={setTempTags}
        />

        {/* 2. Giá sản phẩm */}
        <PricingSection />

        {/* 3. Hình ảnh sản phẩm */}
        <MediaSection
          mediaFiles={mediaFiles}
          setMediaFiles={setMediaFiles}
          tempTags={tempTags}
          setTempTags={setTempTags}
          onMediaFilesChange={handleMediaFilesChange}
        />

        {/* 4. Vận chuyển */}
        <ShippingSection />

        {/* 5. Quản lý tồn kho */}
        <InventorySection
          productInventories={productInventories}
          setProductInventories={setProductInventories}
          handleAddInventory={handleAddInventory}
          handleRemoveInventory={handleRemoveInventory}
          handleUpdateInventory={handleUpdateInventory}
        />

        {/* 6. Phân loại sản phẩm */}
        <ClassificationsSection
          productClassifications={productClassifications}
          setProductClassifications={setProductClassifications}
          handleAddVariant={handleAddVariant}
          handleRemoveVariant={handleRemoveVariant}
          handleUpdateVariant={handleUpdateVariant}
          handleVariantImagesChange={handleVariantImagesChange}
          handleToggleCustomFieldToVariant={handleToggleCustomFieldToVariant}
          handleUpdateCustomFieldInVariant={handleUpdateCustomFieldInVariant}
          handleRemoveCustomFieldFromVariant={handleRemoveCustomFieldFromVariant}
        />

        {/* 7. Trường tùy chỉnh */}
        <CustomFieldsSection
          productCustomFields={productCustomFields}
          setProductCustomFields={setProductCustomFields}
          handleToggleCustomFieldToProduct={handleToggleCustomFieldToProduct}
          handleUpdateCustomFieldInProduct={handleUpdateCustomFieldInProduct}
          handleRemoveCustomFieldFromProduct={handleRemoveCustomFieldFromProduct}
        />

        <div className="flex flex-row justify-end gap-2">
          <IconCard
            icon="x"
            variant="secondary"
            size="md"
            title={t('common:cancel')}
            onClick={onCancel}
            disabled={updateProductMutation.isPending || isUploading}
          />
          <IconCard
            icon="check"
            variant="primary"
            size="md"
            title={isUploading ? t('business:product.uploading') : t('common:save')}
            onClick={() => {
              // Trigger form submit programmatically
              formRef.current?.submit();
            }}
            disabled={updateProductMutation.isPending || isUploading}
            isLoading={updateProductMutation.isPending || isUploading}
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default PhysicalProductEditFormWrapper;
