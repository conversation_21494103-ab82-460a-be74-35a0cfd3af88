import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { NotificationUtil } from '@/shared/utils/notification';
import {
  createUserDataFineTune,
  updateUploadStatus,
  updateDatasetStatus,
  getUserDataFineTuneList,
  getUserDataFineTuneDetail,
  updateUserDataFineTune,
  deleteUserDataFineTune,
  getUploadUrl,
  uploadFile,
  uploadJsonlData,
  getDeletedUserDataFineTuneList,
  restoreUserDataFineTune,
} from '../services/user-data-fine-tune.service';
import {
  CreateUserDataFineTuneDto,
  PaginatedResult,
  UpdateUserDataFineTuneDto,
  UserDataFineTuneQueryDto,
  UserDataFineTuneResponseDto,
} from '../types/user-data-fine-tune.types';

/**
 * Query keys cho User Data Fine Tune
 */
export const USER_DATA_FINE_TUNE_QUERY_KEYS = {
  all: ['user-data-fine-tune'] as const,
  lists: () => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'list'] as const,
  list: (params: UserDataFineTuneQueryDto) =>
    [...USER_DATA_FINE_TUNE_QUERY_KEYS.lists(), params] as const,
  details: () => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...USER_DATA_FINE_TUNE_QUERY_KEYS.details(), id] as const,
  uploadUrl: (mime: string) => [...USER_DATA_FINE_TUNE_QUERY_KEYS.all, 'upload-url', mime] as const,
};

/**
 * Hook để lấy danh sách dataset fine tune
 */
export const useUserDataFineTuneList = (queryDto?: UserDataFineTuneQueryDto) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.list(queryDto || {}),
    queryFn: () => getUserDataFineTuneList(queryDto),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy chi tiết dataset fine tune
 */
export const useUserDataFineTuneDetail = (id: string) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(id),
    queryFn: () => getUserDataFineTuneDetail(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để lấy upload URL
 */
export const useUploadUrl = (mime: string) => {
  return useQuery({
    queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.uploadUrl(mime),
    queryFn: () => getUploadUrl(mime),
    enabled: !!mime,
    staleTime: 0, // Always fresh
  });
};

/**
 * Hook để tạo dataset fine tune
 */
export const useCreateUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserDataFineTuneDto) => createUserDataFineTune(data),
    onSuccess: () => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật dataset fine tune
 */
export const useUpdateUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateUserDataFineTuneDto }) =>
      updateUserDataFineTune(id, data),
    onSuccess: (data, variables) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });

      // Update cache cho detail
      queryClient.setQueryData(USER_DATA_FINE_TUNE_QUERY_KEYS.detail(variables.id), data);
    },
  });
};

/**
 * Hook để xóa dataset fine tune
 */
export const useDeleteUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => deleteUserDataFineTune(id),
    onSuccess: (_, id) => {
      // Invalidate danh sách
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });

      // Remove từ cache
      queryClient.removeQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(id),
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái upload
 */
export const useUpdateUploadStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, status }: { id: string; status: boolean }) => updateUploadStatus(id, status),
    onSuccess: (_, variables) => {
      // Invalidate danh sách và detail
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(variables.id),
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái dataset
 */
export const useUpdateDatasetStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id }: { id: string }) => updateDatasetStatus(id),
    onSuccess: (_, variables) => {
      // Invalidate danh sách và detail
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.detail(variables.id),
      });
    },
  });
};

/**
 * Hook để upload file
 */
export const useUploadFile = () => {
  return useMutation({
    mutationFn: ({ uploadUrl, file }: { uploadUrl: string; file: File }) =>
      uploadFile(uploadUrl, file),
  });
};

/**
 * Hook để upload JSONL data
 */
export const useUploadJsonlData = () => {
  return useMutation({
    mutationFn: ({ uploadUrl, jsonlData }: { uploadUrl: string; jsonlData: string }) =>
      uploadJsonlData(uploadUrl, jsonlData),
  });
};

/**
 * Hook để validate dataset name (check duplicate)
 */
export const useValidateDatasetName = () => {
  return useMutation({
    mutationFn: async (name: string) => {
      if (!name.trim()) {
        return { isValid: true, error: null };
      }

      try {
        // Gọi API để lấy danh sách dataset với search name
        const response = await getUserDataFineTuneList({
          search: name.trim(),
          limit: 100,
        });

        // Check xem có dataset nào có tên trùng khớp không (case-insensitive)
        const duplicateDataset = response.items.find(
          dataset => dataset.name.toLowerCase() === name.trim().toLowerCase()
        );

        if (duplicateDataset) {
          // Hiển thị notification thay vì return error
          NotificationUtil.error({
            title: 'Tên dataset đã tồn tại',
            message: `Dataset với tên "${name}" đã tồn tại. Vui lòng chọn tên khác.`,
            duration: 5000,
          });

          return {
            isValid: false,
            error: `Dataset với tên "${name}" đã tồn tại. Vui lòng chọn tên khác.`,
          };
        }

        return { isValid: true, error: null };
      } catch (error) {
        console.error('Error validating dataset name:', error);
        // Nếu có lỗi khi validate, cho phép tiếp tục (fail-safe)
        return { isValid: true, error: null };
      }
    },
  });
};

/**
 * Hook tổng hợp cho việc tạo và upload dataset
 */
export const useCreateAndUploadDataset = () => {
  const createMutation = useCreateUserDataFineTune();
  const uploadJsonlMutation = useUploadJsonlData();

  const createAndUpload = async ({
    datasetInfo,
    trainJsonlData,
    validJsonlData,
  }: {
    datasetInfo: CreateUserDataFineTuneDto;
    trainJsonlData: string;
    validJsonlData?: string;
  }) => {
    // 1. Tạo dataset và lấy upload URLs
    const createResponse = await createMutation.mutateAsync(datasetInfo);

    // 2. Upload training data
    await uploadJsonlMutation.mutateAsync({
      uploadUrl: createResponse.trainUploadUrl,
      jsonlData: trainJsonlData,
    });

    // 3. Upload validation data nếu có
    if (validJsonlData && createResponse.validUploadUrl) {
      await uploadJsonlMutation.mutateAsync({
        uploadUrl: createResponse.validUploadUrl,
        jsonlData: validJsonlData,
      });
    }

    return createResponse;
  };

  return {
    createAndUpload,
    isLoading: createMutation.isPending || uploadJsonlMutation.isPending,
    error: createMutation.error || uploadJsonlMutation.error,
  };
};

/**
 * Hook để lấy danh sách dataset fine tune đã xóa mềm
 */
export const useDeletedUserDataFineTuneList = (queryDto?: UserDataFineTuneQueryDto) => {
  return useQuery<PaginatedResult<UserDataFineTuneResponseDto>>({
    queryKey: [...USER_DATA_FINE_TUNE_QUERY_KEYS.lists(), 'deleted', queryDto || {}],
    queryFn: () => getDeletedUserDataFineTuneList(queryDto),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook để khôi phục dataset fine tune đã xóa mềm
 */
export const useRestoreUserDataFineTune = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => restoreUserDataFineTune(id),
    onSuccess: () => {
      // Invalidate danh sách deleted và danh sách chính
      queryClient.invalidateQueries({
        queryKey: USER_DATA_FINE_TUNE_QUERY_KEYS.lists(),
      });
    },
  });
};
