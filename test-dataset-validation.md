# Test Dataset Name Validation

## Tính năng đã implement:

### 1. Client-side validation cho dataset name duplicate
- **Location**: 
  - `src/modules/admin/dataset/user-data-fine-tune/hooks/useUserDataFineTune.ts`
  - `src/modules/user-dataset/user-data-fine-tune/hooks/useUserDataFineTune.ts`

### 2. Hook `useValidateDatasetName`
- Gọi API để lấy danh sách dataset với search name
- Check case-insensitive duplicate
- Hiển thị NotificationUtil.error khi có duplicate
- Return validation result

### 3. Real-time validation trong form
- **Location**:
  - `src/modules/admin/dataset/pages/CreateDatasetOpenAIPage.tsx`
  - `src/modules/user-dataset/pages/CreateDatasetOpenAIPage.tsx`
- Debounced validation (500ms delay)
- Clear error khi user typing
- Prevent submit khi có duplicate

## Cách test:

### Test Case 1: Tạo dataset với tên mới
1. Mở trang tạo dataset OpenAI
2. Nhập tên dataset chưa tồn tại
3. Không có error message
4. <PERSON><PERSON> thể submit thành công

### Test Case 2: Tạo dataset với tên đã tồn tại
1. Mở trang tạo dataset OpenAI
2. Nhập tên dataset đã tồn tại trong hệ thống
3. Sau 500ms sẽ hiện notification error
4. Form input sẽ có error state
5. Không thể submit form

### Test Case 3: Clear error khi sửa tên
1. Nhập tên duplicate (có error)
2. Sửa tên thành tên khác
3. Error sẽ được clear ngay lập tức
4. Validation sẽ chạy lại sau 500ms

## Technical Details:

### API Response Structure:
- **Admin**: `response.items` (PaginatedResult with items array)
- **User**: `response.data` (PaginatedResult with data array)

### Validation Logic:
```typescript
const duplicateDataset = response.items.find(
  dataset => dataset.name.toLowerCase() === name.trim().toLowerCase()
);
```

### Notification:
```typescript
NotificationUtil.error({
  title: 'Tên dataset đã tồn tại',
  message: `Dataset với tên "${name}" đã tồn tại. Vui lòng chọn tên khác.`,
  duration: 5000,
});
```

## Files Modified:
1. `src/modules/admin/dataset/user-data-fine-tune/hooks/useUserDataFineTune.ts`
2. `src/modules/user-dataset/user-data-fine-tune/hooks/useUserDataFineTune.ts`
3. `src/modules/admin/dataset/pages/CreateDatasetOpenAIPage.tsx`
4. `src/modules/user-dataset/pages/CreateDatasetOpenAIPage.tsx`
