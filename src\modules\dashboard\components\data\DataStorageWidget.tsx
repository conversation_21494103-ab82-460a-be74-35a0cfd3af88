import React from 'react';
import { useStorageInfo } from '@/modules/data/hooks/useDataOverview';
import { Loading } from '@/shared/components/common';
import StorageUsageCard from '@/modules/data/components/StorageUsageCard';

/**
 * Widget hiển thị thông tin dung lượng dữ liệu cho dashboard
 */
const DataStorageWidget: React.FC = () => {
  const { data: storageData, isLoading, error } = useStorageInfo();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-32">
        <Loading size="sm" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-32 text-muted-foreground">
        <p>Không thể tải thông tin dung lượng</p>
      </div>
    );
  }

  return (
    <StorageUsageCard
      usedFormatted={storageData?.usedFormatted || '0 GB'}
      totalFormatted={storageData?.totalFormatted || '0 GB'}
      percentage={storageData?.percentage || 0}
    />
  );
};

export default DataStorageWidget;
