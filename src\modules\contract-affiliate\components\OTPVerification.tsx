/**
 * Wrapper component cho OTPVerification từ contract module
 */
import React from 'react';
import { OTPVerification as OriginalOTPVerification } from '@/modules/contract/components';
import { ContractAffiliateStepProps, ContractAffiliateType, ContractAffiliateData } from '../types';
import { ContractType, ContractData } from '@/modules/contract/types';

const OTPVerification: React.FC<ContractAffiliateStepProps> = (props) => {
  // Convert ContractAffiliateData to ContractData
  const convertedData: ContractData = {
    type: props.data.type === ContractAffiliateType.BUSINESS ? ContractType.BUSINESS : ContractType.PERSONAL,
    termsAccepted: props.data.termsAccepted,
    businessInfo: props.data.businessInfo ? {
      companyName: props.data.businessInfo.companyName,
      taxCode: props.data.businessInfo.taxCode,
      companyEmail: props.data.businessInfo.companyEmail,
      companyAddress: props.data.businessInfo.companyAddress,
      companyPhone: props.data.businessInfo.companyPhone,
      representative: props.data.businessInfo.representative,
      position: props.data.businessInfo.position,
    } : undefined,
    ...(props.data.personalInfo && {
      personalInfo: {
        fullName: props.data.personalInfo.fullName,
        dateOfBirth: props.data.personalInfo.dateOfBirth,
        idNumber: props.data.personalInfo.idNumber,
        idIssuedDate: props.data.personalInfo.idIssuedDate,
        idIssuedPlace: props.data.personalInfo.idIssuedPlace,
        phone: props.data.personalInfo.phone,
        address: props.data.personalInfo.address,
        ...(props.data.personalInfo.taxCode && { taxCode: props.data.personalInfo.taxCode }),
      }
    }),
    ...(props.data.contractUrl && { contractUrl: props.data.contractUrl }),
    ...(props.data.contractBase64 && { contractBase64: props.data.contractBase64 }),
    ...(props.data.handSignature && { handSignature: props.data.handSignature }),
    ...(props.data.otpCode && { otpCode: props.data.otpCode }),
    isCompleted: props.data.isCompleted,
  };

  const handleNext = (updatedData: Partial<ContractData>) => {
    // Convert back to ContractAffiliateData
    const updateData: Partial<ContractAffiliateData> = {};
    if (updatedData.contractUrl !== undefined) updateData.contractUrl = updatedData.contractUrl;
    if (updatedData.contractBase64 !== undefined) updateData.contractBase64 = updatedData.contractBase64;
    if (updatedData.handSignature !== undefined) updateData.handSignature = updatedData.handSignature;
    if (updatedData.otpCode !== undefined) updateData.otpCode = updatedData.otpCode;
    if (updatedData.isCompleted !== undefined) updateData.isCompleted = updatedData.isCompleted;

    props.onNext(updateData);
  };

  return (
    <OriginalOTPVerification
      data={convertedData}
      onNext={handleNext}
      onPrevious={props.onPrevious}
      isLoading={props.isLoading || false}
    />
  );
};

export default OTPVerification;
