import { RouteObject } from 'react-router-dom';
import ProtectedRoute from './routeProtection';
import { AuthType } from '@/shared/hooks';

/**
 * Hàm wrap routes với protection
 */
export const wrapRoutesWithAuth = (
  routes: RouteObject[],
  requiredAuthType: AuthType = AuthType.USER,
  redirectTo: string
): RouteObject[] => {
  return routes.map(route => {
    const wrappedRoute: RouteObject = {
      ...route,
      element: route.element ? (
        <ProtectedRoute requiredAuthType={requiredAuthType} redirectTo={redirectTo}>
          {route.element}
        </ProtectedRoute>
      ) : route.element,
    };

    // Only add children if they exist
    if (route.children) {
      wrappedRoute.children = wrapRoutesWithAuth(route.children, requiredAuthType, redirectTo);
    }

    return wrappedRoute;
  });
};

/**
 * Hàm kiểm tra xem route có phải là public route không (không cần authentication)
 */
const isPublicRoute = (path: string): boolean => {
  const publicPaths = [
    '/auth',
    '/admin/auth',
    // C<PERSON> thể thêm các route public khác ở đây nếu cần
  ];

  return publicPaths.some(publicPath => path.startsWith(publicPath));
};

/**
 * Hàm phân loại routes thành user routes và admin routes
 */
export const categorizeRoutes = (routes: RouteObject[]) => {
  const userRoutes: RouteObject[] = [];
  const adminRoutes: RouteObject[] = [];
  const publicRoutes: RouteObject[] = [];

  routes.forEach(route => {
    if (!route.path) {
      // Routes không có path (như error routes) được coi là public
      publicRoutes.push(route);
      return;
    }

    if (isPublicRoute(route.path)) {
      publicRoutes.push(route);
    } else if (route.path.startsWith('/admin')) {
      adminRoutes.push(route);
    } else {
      userRoutes.push(route);
    }
  });

  console.log('Route categorization:', {
    publicRoutes: publicRoutes.length,
    userRoutes: userRoutes.length,
    adminRoutes: adminRoutes.length,
    publicPaths: publicRoutes.map(r => r.path).filter(Boolean),
    userPaths: userRoutes.map(r => r.path).filter(Boolean).slice(0, 5), // Show first 5
    adminPaths: adminRoutes.map(r => r.path).filter(Boolean).slice(0, 5), // Show first 5
  });

  return { userRoutes, adminRoutes, publicRoutes };
};
