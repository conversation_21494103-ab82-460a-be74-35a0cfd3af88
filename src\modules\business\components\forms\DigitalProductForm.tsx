import React, { useState, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Form, FormMultiWrapper, IconCard } from '@/shared/components/common';
import { z } from 'zod';
import {
  PriceTypeEnum,
  HasPriceDto,
  StringPriceDto,
  CreateProductDto,
  ProductTypeEnum,
} from '../../types/product.types';

// Interfaces theo backend API mới






import { useCustomFields } from '../../hooks/useCustomFieldQuery';
import { NotificationUtil } from '@/shared/utils/notification';
import { FieldValues } from 'react-hook-form';
import { FormRef } from '@/shared/components/common/Form/Form';
import { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { useProductImageUpload } from '@/modules/business/hooks/useProductImageUpload';
import { useQueryClient } from '@tanstack/react-query';
import { PRODUCT_QUERY_KEYS } from '../../hooks/useProductQuery';
import {
  GeneralInfoSection,
  DigitalPricingSection,
  DigitalProcessingSection,
  DigitalOutputSection,
  DigitalVersionsSection,
  CustomFieldsSection,
  MediaSection,
} from './sections';
import {
  SelectedCustomField,
  ExtendedFileWithMetadata,
  FormDigitalProductVersion,
  DigitalProductFormValues,
} from './sections/digital-product-form-types';
import { useCreateDigitalProduct } from '../../hooks/useCreateDigitalProduct';

interface DigitalProductFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
}

// Mapping functions to convert form values to API values
const mapDeliveryMethod = (method: string): 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation' => {
  const mapping: Record<string, 'email' | 'dashboard_download' | 'sms' | 'direct_message' | 'zalo' | 'course_activation'> = {
    'EMAIL': 'email',
    'DASHBOARD': 'dashboard_download',
    'SMS': 'sms',
    'DIRECT_MESSAGE': 'direct_message',
    'ZALO': 'zalo',
    'AUTO_ACTIVE': 'course_activation',
  };
  return mapping[method] || 'dashboard_download';
};

const mapDeliveryTiming = (timing: string): 'immediate' | 'delayed' => {
  const mapping: Record<string, 'immediate' | 'delayed'> = {
    'IMMEDIATE': 'immediate',
    'DELAYED': 'delayed',
  };
  return mapping[timing] || 'immediate';
};

const mapOutputType = (type: string): 'online_course' | 'file_download' | 'license_key' | 'ebook' => {
  const mapping: Record<string, 'online_course' | 'file_download' | 'license_key' | 'ebook'> = {
    'DOWNLOAD_LINK': 'file_download',
    'ACCESS_CODE': 'license_key',
    'ACCOUNT_INFO': 'online_course',
    'CONTENT': 'ebook',
  };
  return mapping[type] || 'file_download';
};

/**
 * Form tạo sản phẩm số
 */
const DigitalProductForm: React.FC<DigitalProductFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['business', 'common']);

  // Hook để tạo sản phẩm số
  const createDigitalProductMutation = useCreateDigitalProduct();

  // Schema validation cho sản phẩm số
  const digitalProductSchema = z
    .object({
      name: z.string().min(1, 'Tên sản phẩm không được để trống'),
      typePrice: z.nativeEnum(PriceTypeEnum, {
        errorMap: () => ({ message: 'Vui lòng chọn loại giá' }),
      }),
      listPrice: z.union([z.string(), z.number()]).optional(),
      salePrice: z.union([z.string(), z.number()]).optional(),
      currency: z.string().optional(),
      priceDescription: z.string().optional(),
      description: z.string().optional(),
      tags: z.array(z.string()).optional(),
      media: z.any().optional(),
      customFields: z.any().optional(),
      // Digital product specific validations (theo API structure mới)
      deliveryMethod: z.enum([
        'DASHBOARD',
        'EMAIL',
        'SMS',
        'DIRECT_MESSAGE',
        'ZALO',
        'AUTO_ACTIVE',
      ]),
      deliveryTiming: z.enum(['IMMEDIATE', 'DELAYED']),
      digitalProductType: z.enum(['DOWNLOAD_LINK', 'ACCESS_CODE', 'ACCOUNT_INFO', 'CONTENT']),
      accessLink: z.string().optional(),
      usageInstructions: z.string().optional(),
      // Validation cho phiên bản
      versions: z
        .array(
          z.object({
            id: z.string().optional(),
            name: z.string().min(1, 'Tên phiên bản không được để trống'),
            price: z.number().min(0, 'Giá phiên bản phải >= 0'),
            currency: z.string().min(1, 'Đơn vị tiền tệ không được để trống'),
            description: z.string().optional(),
            quantity: z.number().min(1, 'Số lượng phải >= 1').optional(),
            sku: z.string().optional(),
            minQuantityPerPurchase: z.number().min(1, 'Số lượng tối thiểu phải >= 1').optional(),
            maxQuantityPerPurchase: z.number().min(1, 'Số lượng tối đa phải >= 1').optional(),
          })
        )
        .optional(),
    })
    .superRefine((data, ctx) => {
      // Kiểm tra giá phù hợp với loại giá
      if (data.typePrice === PriceTypeEnum.HAS_PRICE) {
        if (!data.listPrice || data.listPrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá niêm yết',
            path: ['listPrice'],
          });
        }
        if (!data.salePrice || data.salePrice === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập giá bán',
            path: ['salePrice'],
          });
        }
        if (!data.currency || data.currency.trim() === '') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng chọn đơn vị tiền tệ',
            path: ['currency'],
          });
        }
      } else if (data.typePrice === PriceTypeEnum.STRING_PRICE) {
        if (!data.priceDescription || !data.priceDescription.trim()) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: 'Vui lòng nhập mô tả giá',
            path: ['priceDescription'],
          });
        }
      }
    });

  // State cho tags
  const [tempTags, setTempTags] = useState<string[]>([]);

  // State cho media
  const [mediaFiles, setMediaFiles] = useState<FileWithMetadata[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // State cho trường tùy chỉnh của sản phẩm chính
  const [productCustomFields, setProductCustomFields] = useState<SelectedCustomField[]>([]);

  // State cho phiên bản sản phẩm số
  const [versions, setVersions] = useState<FormDigitalProductVersion[]>([
    {
      id: `version-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      description: '',
      quantity: 1,
      minQuantityPerPurchase: 1,
      maxQuantityPerPurchase: 10,
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [], // Khởi tạo mảng trường tùy chỉnh rỗng
    },
  ]);

  // Form ref
  const formRef = useRef<FormRef<Record<string, unknown>>>(null);

  // Query lấy danh sách trường tùy chỉnh
  useCustomFields();

  // Hook để upload ảnh sản phẩm theo pattern MediaPage
  const { uploadProductImages } = useProductImageUpload();

  // Query client để invalidate cache sau khi upload xong
  const queryClient = useQueryClient();

  // Xử lý khi submit form
  const handleSubmit = async (values: FieldValues) => {
    if (!values['name'] || !values['typePrice']) {
      NotificationUtil.error({
        message: 'Vui lòng nhập tên sản phẩm và chọn loại giá',
        duration: 3000,
      });
      return;
    }

    try {
      const formValues = values as DigitalProductFormValues;
      setIsUploading(true);

      // Chuyển đổi giá trị form thành dữ liệu API
      let priceData;
      try {
        priceData = getPriceData(formValues);
      } catch (priceError) {
        NotificationUtil.error({
          message: priceError instanceof Error ? priceError.message : 'Lỗi validation giá',
          duration: 3000,
        });
        setIsUploading(false);
        return;
      }

      // Tạo payload theo backend API structure mới
      const productData: CreateProductDto = {
        productType: ProductTypeEnum.DIGITAL,
        name: formValues.name,
        typePrice: formValues.typePrice,
        price: priceData as HasPriceDto,
        advancedInfo: {
          purchaseCount: 0,
          digitalFulfillmentFlow: {
            deliveryMethod: mapDeliveryMethod(formValues.deliveryMethod) || 'dashboard_download',
            deliveryTiming: mapDeliveryTiming(formValues.deliveryTiming) || 'immediate',
            deliveryDelayMinutes: 0,
            accessStatus: 'pending',
          },
          digitalOutput: {
            outputType: mapOutputType(formValues.digitalProductType) || 'file_download',
            accessLink: formValues.accessLink || '',
            usageInstructions: formValues.usageInstructions || '',
          },
        },
      };

      // Chỉ thêm các thuộc tính optional khi có giá trị
      if (formValues.description && formValues.description.trim()) {
        productData.description = formValues.description.trim();
      }

      if (formValues.tags && formValues.tags.length > 0) {
        productData.tags = formValues.tags;
      }

      if (mediaFiles.length > 0) {
        productData.imagesMediaTypes = mediaFiles.map(file => file.file.type);
      }

      const filteredCustomFields = productCustomFields
        .filter(field => {
          // Lọc ra những field có giá trị không rỗng
          const fieldValue = field.value?.['value'];
          return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
        })
        .map(field => ({
          customFieldId: field.fieldId,
          value: {
            value: field.value?.['value'] as string | number | boolean | object,
          },
        }));

      if (filteredCustomFields.length > 0) {
        productData.customFields = filteredCustomFields;
      }

      // Thêm classifications cho versions nếu có
      if (versions.length > 0) {
        productData.classifications = versions.map(version => ({
          type: 'variant', // Required field
          price: {
            listPrice: version.price || 0,
            salePrice: Math.round((version.price || 0) * 0.8), // Giảm 20%
            currency: version.currency || 'VND',
          },
          imagesMediaTypes:
            version.images && version.images.length > 0
              ? version.images.map(img => img.file?.type || 'image/jpeg')
              : [],
          customFields: version.customFields
            .filter(field => {
              const fieldValue = field.value?.['value'];
              return fieldValue !== undefined && fieldValue !== null && fieldValue !== '';
            })
            .map(field => ({
              customFieldId: field.fieldId,
              value: {
                value: field.value?.['value'] || '',
              },
            })),
        }));
      }

      // Gọi hook để tạo sản phẩm số
      const response = await createDigitalProductMutation.mutateAsync(productData);

      // Upload media nếu có và API trả về images với upload URLs
      if (mediaFiles.length > 0) {
        try {
          // Kiểm tra xem response có uploadUrls.imagesUploadUrls không
          const hasUploadUrls =
            response &&
            typeof response === 'object' &&
            'uploadUrls' in response &&
            response.uploadUrls &&
            typeof response.uploadUrls === 'object' &&
            'imagesUploadUrls' in response.uploadUrls &&
            Array.isArray(response.uploadUrls.imagesUploadUrls);

          if (hasUploadUrls && response.uploadUrls) {
            const uploadUrls = response.uploadUrls.imagesUploadUrls;

            if (uploadUrls.length > 0) {
              // Tạo mapping giữa media files và upload URLs từ backend
              const uploadTasks = mediaFiles.slice(0, uploadUrls.length).map((fileData, index) => {
                const uploadInfo = uploadUrls[index] as Record<string, unknown>;
                if (!uploadInfo) {
                  throw new Error(`Upload info not found for index ${index}`);
                }
                return {
                  file: fileData.file,
                  uploadUrl: uploadInfo['url'] as string,
                  key: uploadInfo['key'] as string,
                  index: uploadInfo['index'] as number,
                };
              });

              // Upload tất cả ảnh cùng lúc với Promise.all
              const filesToUpload = uploadTasks.map((task, index) => ({
                file: task.file,
                id: `${Date.now()}_${index}`,
              }));
              const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

              // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
              await uploadProductImages(filesToUpload, urlsToUpload, {
                skipCacheInvalidation: true,
              });

              // Invalidate cache để refresh danh sách sản phẩm một lần duy nhất
              queryClient.invalidateQueries({ queryKey: PRODUCT_QUERY_KEYS.lists() });

              NotificationUtil.success({
                message: t(
                  'business:product.mediaUploadSuccess',
                  'Tải lên ảnh sản phẩm thành công'
                ),
                duration: 3000,
              });
            }
          } else {
            NotificationUtil.warning({
              message: t(
                'business:product.mediaUploadWarning',
                'Sản phẩm đã được tạo nhưng không thể tải lên ảnh'
              ),
              duration: 5000,
            });
          }
        } catch {
          NotificationUtil.warning({
            message: t(
              'business:product.mediaUploadError',
              'Có lỗi xảy ra khi tải lên ảnh sản phẩm'
            ),
            duration: 5000,
          });
        }
      }

      // Upload ảnh cho versions (variants) nếu có
      const responseData = response as unknown as Record<string, unknown>;
      console.log('🔍 Full response structure:', JSON.stringify(response, null, 2));

      // Kiểm tra xem có advancedImagesUploadUrls không (cấu trúc mới từ backend)
      const hasAdvancedUploadUrls =
        responseData &&
        responseData['uploadUrls'] &&
        typeof responseData['uploadUrls'] === 'object' &&
        (responseData['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls'] &&
        Array.isArray(
          (responseData['uploadUrls'] as Record<string, unknown>)['advancedImagesUploadUrls']
        );

      if (hasAdvancedUploadUrls) {
        try {
          // Lọc các versions có ảnh cần upload
          const versionsWithImages = versions.filter(
            version => version.images && version.images.length > 0
          );

          if (versionsWithImages.length > 0) {
            console.log('🔍 Uploading images for versions:', versionsWithImages.length);
            console.log(
              '🔍 Versions with images:',
              versionsWithImages.map(v => ({ name: v.name, imageCount: v.images?.length || 0 }))
            );

            const advancedUploadUrls = (responseData['uploadUrls'] as Record<string, unknown>)[
              'advancedImagesUploadUrls'
            ] as unknown[];
            console.log('🔍 Advanced upload URLs:', advancedUploadUrls.length);

            // Upload ảnh cho từng version theo thứ tự
            let uploadUrlIndex = 0;
            for (let versionIndex = 0; versionIndex < versionsWithImages.length; versionIndex++) {
              const version = versionsWithImages[versionIndex];

              // Kiểm tra version và images tồn tại
              if (!version || !version.images || version.images.length === 0) {
                continue;
              }

              console.log(
                `🔍 Processing version ${versionIndex} (${version.name}) with ${version.images.length} images`
              );

              // Lấy số lượng upload URLs cần thiết cho version này
              const versionImageCount = version.images.length;
              const versionUploadUrls = advancedUploadUrls.slice(
                uploadUrlIndex,
                uploadUrlIndex + versionImageCount
              );

              if (versionUploadUrls.length > 0) {
                console.log(
                  `🔍 Found ${versionUploadUrls.length} upload URLs for version ${version.name}`
                );

                // Tạo mapping giữa image files và upload URLs từ backend
                const uploadTasks = version.images
                  .slice(0, versionUploadUrls.length)
                  .map((fileData, index) => {
                    const uploadInfo = versionUploadUrls[index] as Record<string, unknown>;
                    if (!uploadInfo) {
                      throw new Error(
                        `Upload info not found for version ${version.name} at index ${index}`
                      );
                    }
                    return {
                      file: fileData.file,
                      uploadUrl: uploadInfo['url'] as string,
                      key: uploadInfo['key'] as string,
                      index: uploadInfo['index'] as number,
                    };
                  });

                // Upload tất cả ảnh của version cùng lúc với Promise.all
                const filesToUpload = uploadTasks.map((task, index) => ({
                  file: task.file,
                  id: `${Date.now()}_${version.id}_${index}`,
                }));
                const urlsToUpload = uploadTasks.map(task => task.uploadUrl);

                // Upload tất cả ảnh cùng lúc, skip cache invalidation trong hook
                await uploadProductImages(filesToUpload, urlsToUpload, {
                  skipCacheInvalidation: true,
                });

                console.log(`✅ Uploaded ${uploadTasks.length} images for version ${version.name}`);

                // Cập nhật index cho version tiếp theo
                uploadUrlIndex += versionImageCount;
              } else {
                console.log(`⚠️ No upload URLs available for version ${version.name}`);
              }
            }

            NotificationUtil.success({
              message: t(
                'business:product.form.validation.versionImagesUploadSuccess',
                'Tải lên ảnh phiên bản thành công'
              ),
              duration: 3000,
            });
          }
        } catch (versionUploadError) {
          console.error('❌ Error uploading version images:', versionUploadError);
          NotificationUtil.warning({
            message: t(
              'business:product.form.validation.versionImagesUploadError',
              'Có lỗi xảy ra khi tải lên ảnh phiên bản'
            ),
            duration: 5000,
          });
        }
      } else {
        console.log(
          '🔍 No advancedImagesUploadUrls found in response, skipping variant image upload'
        );
      }

      setIsUploading(false);

      // Gọi callback onSuccess nếu có
      if (onSuccess) {
        onSuccess();
      }

      NotificationUtil.success({
        message: t('business:product.createSuccess', 'Tạo sản phẩm số thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in DigitalProductForm handleSubmit:', error);
      setIsUploading(false);

      NotificationUtil.error({
        message: t('business:product.createError'),
        duration: 3000,
      });
    }
  };

  // Hàm lấy dữ liệu giá dựa trên loại giá
  const getPriceData = (values: DigitalProductFormValues): HasPriceDto | StringPriceDto => {
    if (values.typePrice === PriceTypeEnum.HAS_PRICE) {
      if (!values.listPrice || values.listPrice === '') {
        throw new Error('Vui lòng nhập giá niêm yết');
      }

      if (!values.salePrice || values.salePrice === '') {
        throw new Error('Vui lòng nhập giá bán');
      }

      if (!values.currency || values.currency.trim() === '') {
        throw new Error('Vui lòng chọn đơn vị tiền tệ');
      }

      const listPrice = Number(values.listPrice);
      const salePrice = Number(values.salePrice);

      if (isNaN(listPrice) || listPrice < 0) {
        throw new Error('Giá niêm yết phải là số >= 0');
      }
      if (isNaN(salePrice) || salePrice < 0) {
        throw new Error('Giá bán phải là số >= 0');
      }
      if (listPrice <= salePrice) {
        throw new Error('Giá niêm yết phải lớn hơn giá bán');
      }

      return {
        listPrice,
        salePrice,
        currency: values.currency.trim(),
      };
    } else if (values.typePrice === PriceTypeEnum.STRING_PRICE) {
      if (!values.priceDescription || !values.priceDescription.trim()) {
        throw new Error('Vui lòng nhập mô tả giá');
      }
      return {
        priceDescription: values.priceDescription.trim(),
      };
    }

    throw new Error('Loại giá không hợp lệ');
  };

  // Thêm/xóa trường tùy chỉnh vào sản phẩm chính
  const handleToggleCustomFieldToProduct = useCallback(
    (fieldId: number, fieldData?: Record<string, unknown>) => {
      setProductCustomFields(prev => {
        const existingFieldIndex = prev.findIndex(field => field.fieldId === fieldId);

        if (existingFieldIndex !== -1) {
          return prev.filter((_, index) => index !== existingFieldIndex);
        }

        const newField: SelectedCustomField = {
          id: Date.now(),
          fieldId,
          label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
          component:
            (fieldData?.['component'] as string) || (fieldData?.['type'] as string) || 'text',
          type: (fieldData?.['type'] as string) || 'text',
          required: (fieldData?.['required'] as boolean) || false,
          configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
          value: { value: '' },
        };

        return [...prev, newField];
      });
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi sản phẩm chính
  const handleRemoveCustomFieldFromProduct = useCallback((customFieldId: number) => {
    setProductCustomFields(prev => prev.filter(field => field.id !== customFieldId));
  }, []);

  // Cập nhật giá trị trường tùy chỉnh trong sản phẩm chính
  const handleUpdateCustomFieldInProduct = useCallback((customFieldId: number, value: string) => {
    setProductCustomFields(prev =>
      prev.map(field => {
        if (field.id === customFieldId) {
          return {
            ...field,
            value: { value },
          };
        }
        return field;
      })
    );
  }, []);

  // Thêm phiên bản mới
  const handleAddVersion = useCallback(() => {
    const newVersion: FormDigitalProductVersion = {
      id: `version-${Date.now()}`,
      name: '',
      price: 0,
      currency: 'VND',
      description: '',
      quantity: 1,
      minQuantityPerPurchase: 1,
      maxQuantityPerPurchase: 10,
      images: [], // Khởi tạo mảng ảnh rỗng
      customFields: [], // Khởi tạo mảng trường tùy chỉnh rỗng
    };
    setVersions(prev => [...prev, newVersion]);
  }, []);

  // Xóa phiên bản
  const handleRemoveVersion = useCallback((versionId: string) => {
    setVersions(prev => prev.filter(version => version.id !== versionId));
  }, []);

  // Cập nhật thông tin phiên bản
  const handleUpdateVersion = useCallback(
    (versionId: string, field: keyof FormDigitalProductVersion, value: string | number) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              [field]: value,
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Xử lý upload nhiều ảnh cho phiên bản
  const handleVersionImagesChange = useCallback(
    (versionId: string, files: ExtendedFileWithMetadata[]) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return { ...version, images: files };
          }
          return version;
        })
      );
    },
    []
  );

  // Thêm/xóa trường tùy chỉnh vào phiên bản
  const handleToggleCustomFieldToVersion = useCallback(
    (versionId: string, fieldId: number, fieldData?: Record<string, unknown>) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            // Kiểm tra xem trường đã tồn tại trong phiên bản chưa
            const existingFieldIndex = version.customFields.findIndex(
              field => field.fieldId === fieldId
            );

            if (existingFieldIndex !== -1) {
              // Nếu đã tồn tại, xóa nó (bỏ chọn)
              return {
                ...version,
                customFields: version.customFields.filter(
                  (_, index) => index !== existingFieldIndex
                ),
              };
            }

            // Thêm trường mới vào phiên bản với thông tin đầy đủ
            return {
              ...version,
              customFields: [
                ...version.customFields,
                {
                  id: Date.now(), // ID tạm thời
                  fieldId,
                  label: (fieldData?.['label'] as string) || `Field ${fieldId}`,
                  component:
                    (fieldData?.['component'] as string) ||
                    (fieldData?.['type'] as string) ||
                    'text',
                  type: (fieldData?.['type'] as string) || 'text',
                  required: (fieldData?.['required'] as boolean) || false,
                  configJson: (fieldData?.['configJson'] as Record<string, unknown>) || {},
                  value: { value: '' }, // Giá trị mặc định
                },
              ],
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Xóa trường tùy chỉnh khỏi phiên bản
  const handleRemoveCustomFieldFromVersion = useCallback(
    (versionId: string, customFieldId: number) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.filter(field => field.id !== customFieldId),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Cập nhật giá trị trường tùy chỉnh trong phiên bản
  const handleUpdateCustomFieldInVersion = useCallback(
    (versionId: string, customFieldId: number, value: string) => {
      setVersions(prev =>
        prev.map(version => {
          if (version.id === versionId) {
            return {
              ...version,
              customFields: version.customFields.map(field => {
                if (field.id === customFieldId) {
                  return {
                    ...field,
                    value: { value },
                  };
                }
                return field;
              }),
            };
          }
          return version;
        })
      );
    },
    []
  );

  // Giá trị mặc định cho form
  const defaultValues = useMemo(
    () => ({
      name: '',
      typePrice: PriceTypeEnum.HAS_PRICE,
      listPrice: '',
      salePrice: '',
      currency: 'VND',
      priceDescription: '',
      description: '',
      tags: [],
      customFields: [],
      media: [],
      // Digital product defaults (theo backend API mới)
      deliveryMethod: 'DASHBOARD' as const,
      deliveryTiming: 'IMMEDIATE' as const,
      digitalProductType: 'ACCESS_CODE' as const,
      accessLink: '',
      usageInstructions: '',
      // Phiên bản mặc định
      versions: [],
    }),
    []
  );

  return (
    <FormMultiWrapper title={t('business:product.form.createDigitalTitle', 'Tạo sản phẩm số')}>
      <Form
        ref={formRef}
        schema={digitalProductSchema}
        onSubmit={handleSubmit}
        onError={errors => {
          console.error('🔥 Form validation errors:', errors);
          const firstError = Object.values(errors)[0];
          const errorMessage = firstError?.message || 'Vui lòng kiểm tra lại thông tin đã nhập';
          NotificationUtil.error({
            message: errorMessage,
            duration: 5000,
          });
        }}
        defaultValues={defaultValues}
        submitOnEnter={false}
        className="space-y-4"
      >
        {/* 1. Thông tin chung */}
        <GeneralInfoSection tempTags={tempTags} setTempTags={setTempTags} />

        {/* 2. Giá sản phẩm */}
        <DigitalPricingSection />
        {/* 3. Hình ảnh sản phẩm */}
        <MediaSection
          mediaFiles={mediaFiles}
          setMediaFiles={setMediaFiles}
          tempTags={tempTags}
          setTempTags={setTempTags}
        />
        {/* 4. Quy trình xử lý đơn hàng số */}
        <DigitalProcessingSection />

        {/* 5. Đầu ra sản phẩm số */}
        <DigitalOutputSection />

        {/* 6. Phiên bản */}
        <DigitalVersionsSection
          versions={versions}
          setVersions={setVersions}
          handleAddVersion={handleAddVersion}
          handleRemoveVersion={handleRemoveVersion}
          handleUpdateVersion={handleUpdateVersion}
          handleVersionImagesChange={handleVersionImagesChange}
          handleToggleCustomFieldToVersion={handleToggleCustomFieldToVersion}
          handleUpdateCustomFieldInVersion={handleUpdateCustomFieldInVersion}
          handleRemoveCustomFieldFromVersion={handleRemoveCustomFieldFromVersion}
        />

        {/* 7. Trường tùy chỉnh */}
        <CustomFieldsSection
          productCustomFields={productCustomFields}
          setProductCustomFields={setProductCustomFields}
          handleToggleCustomFieldToProduct={handleToggleCustomFieldToProduct}
          handleUpdateCustomFieldInProduct={handleUpdateCustomFieldInProduct}
          handleRemoveCustomFieldFromProduct={handleRemoveCustomFieldFromProduct}
        />

        {/* Actions */}
        <div className="flex justify-end space-x-4 pt-6">
          <IconCard
            icon="x"
            title={t('common:cancel')}
            onClick={onCancel}
            variant="secondary"
            className="cursor-pointer"
          />
          <IconCard
            icon="check"
            title={
              createDigitalProductMutation.isPending || isUploading
                ? t('business:product.form.creating', 'Đang tạo...')
                : t('business:product.form.create', 'Tạo sản phẩm')
            }
            onClick={() => formRef.current?.submit()}
            variant="primary"
            disabled={createDigitalProductMutation.isPending || isUploading}
            className="cursor-pointer"
          />
        </div>
      </Form>
    </FormMultiWrapper>
  );
};

export default DigitalProductForm;
