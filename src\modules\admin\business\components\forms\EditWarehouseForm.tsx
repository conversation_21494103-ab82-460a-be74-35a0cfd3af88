import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Card, Form, FormItem, Input, Select, Button, Textarea } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import { useUpdateWarehouse, useWarehouse } from '../../hooks/useWarehouseQuery';
import { WarehouseTypeEnum, UpdateWarehouseDto } from '../../types/warehouse.types';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import { useFormErrors } from '@/shared/hooks/form';

// Schema cho form
const getWarehouseSchema = (t: (key: string) => string) =>
  z.object({
    name: z.string().min(1, t('admin:business.warehouse.form.namePlaceholder')),
    description: z.string().optional(),
    type: z.nativeEnum(WarehouseTypeEnum, {
      errorMap: () => ({
        message: t('admin:business.warehouse.form.typePlaceholder'),
      }),
    }),
  });

export type EditWarehouseFormValues = z.infer<ReturnType<typeof getWarehouseSchema>>;

interface EditWarehouseFormProps {
  warehouseId: number;
  onSubmit?: (data: EditWarehouseFormValues) => void;
  onCancel?: () => void;
}

/**
 * Component form chỉnh sửa kho
 */
const EditWarehouseForm: React.FC<EditWarehouseFormProps> = ({
  warehouseId,
  onSubmit,
  onCancel,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const notification = useSmartNotification();
  const [isLoading, setIsLoading] = useState(true);

  // Hook để xử lý lỗi form
  const { formRef, setFormErrors } = useFormErrors<EditWarehouseFormValues>();

  // Lấy thông tin kho
  const { data: warehouseData, isLoading: isLoadingWarehouse } = useWarehouse(warehouseId);

  // Mutation để cập nhật kho
  const { mutateAsync: updateWarehouse, isPending: isUpdating } = useUpdateWarehouse();

  // Schema cho form
  const warehouseSchema = getWarehouseSchema(t);

  // Tính toán defaultValues từ dữ liệu warehouse
  const defaultValues: EditWarehouseFormValues = {
    name: warehouseData?.result?.name || '',
    description: warehouseData?.result?.description || '',
    type: (warehouseData?.result?.type as WarehouseTypeEnum) || WarehouseTypeEnum.PHYSICAL,
  };

  // Cập nhật loading state khi có dữ liệu
  useEffect(() => {
    if (warehouseData?.result) {
      setIsLoading(false);
      console.log('Warehouse data loaded, form should be ready');
    }
  }, [warehouseData]);

  // Xử lý submit form
  const handleSubmit = async (values: EditWarehouseFormValues) => {
    try {
      // Cập nhật kho
      await updateWarehouse({
        id: warehouseId,
        data: values as UpdateWarehouseDto,
      });
      notification.success({ message: t('admin:business.warehouse.updateSuccess') });

      // Gọi callback onSubmit nếu có
      if (onSubmit) {
        onSubmit(values);
      }
    } catch (error: unknown) {
      console.error('Error updating warehouse:', error);

      // Type guard for axios error
      const isAxiosError = (
        err: unknown
      ): err is { response?: { data?: { code?: number; message?: string } } } => {
        return typeof err === 'object' && err !== null && 'response' in err;
      };

      // Xử lý lỗi code 30043 - tên kho đã tồn tại
      if (isAxiosError(error) && error.response?.data?.code === 30043) {
        console.log('Setting form error for warehouse name:', error.response.data.message);
        console.log('Form ref current:', formRef.current);

        // Đảm bảo form đã được khởi tạo trước khi set error
        setTimeout(() => {
          setFormErrors({
            name:
              error.response?.data?.message ||
              t('admin:business.warehouse.errors.nameExists', 'Tên kho đã tồn tại'),
          });
        }, 100);
        return;
      }

      // Xử lý các lỗi khác
      notification.error({
        message: isAxiosError(error)
          ? error.response?.data?.message || t('admin:business.warehouse.updateError')
          : t('admin:business.warehouse.updateError'),
      });
    }
  };

  return (
    <Card title={t('admin:business.warehouse.edit')}>
      <Form
        key={warehouseData?.result?.id} // Force re-render when data changes
        ref={formRef as unknown as React.RefObject<FormRef<Record<string, unknown>>>}
        schema={warehouseSchema}
        defaultValues={defaultValues}
        onSubmit={handleSubmit as unknown as (data: Record<string, unknown>) => void}
        className="p-4 space-y-4"
      >
        <div className="grid grid-cols-1 gap-4">
          <FormItem name="name" label={t('admin:business.warehouse.name')} required>
            <Input
              fullWidth
              placeholder={t('admin:business.warehouse.form.namePlaceholder')}
              disabled={isLoading || isLoadingWarehouse}
            />
          </FormItem>

          <FormItem name="description" label={t('admin:business.warehouse.desc')}>
            <Textarea
              rows={4}
              placeholder={t('admin:business.warehouse.form.descriptionPlaceholder')}
              disabled={isLoading || isLoadingWarehouse}
            />
          </FormItem>

          <FormItem name="type" label={t('admin:business.warehouse.type')} required>
            <Select
              fullWidth
              placeholder={t('admin:business.warehouse.form.selectType')}
              options={[
                {
                  value: WarehouseTypeEnum.PHYSICAL,
                  label: t('admin:business.warehouse.types.PHYSICAL'),
                },
                {
                  value: WarehouseTypeEnum.VIRTUAL,
                  label: t('admin:business.warehouse.types.VIRTUAL'),
                },
              ]}
              disabled={isLoading || isLoadingWarehouse}
            />
          </FormItem>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" isLoading={isUpdating}>
            {t('common:save')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditWarehouseForm;
