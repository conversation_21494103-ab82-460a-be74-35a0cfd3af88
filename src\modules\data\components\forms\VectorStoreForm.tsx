import React from 'react';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import { Form, FormItem, Input, IconCard, Typography, Card } from '@/shared/components/common';

// Đ<PERSON>nh nghĩa schema validation với Zod
const vectorStoreSchema = z.object({
  name: z.string().min(1, 'Tên Vector Store là bắt buộc'),
});

// Định nghĩa kiểu dữ liệu cho form
export interface VectorStoreFormValues {
  name: string;
}

interface VectorStoreFormProps {
  onSubmit: (values: VectorStoreFormValues) => void;
  onCancel: () => void;
  initialValues?: Partial<VectorStoreFormValues>;
  isLoading?: boolean;
}

/**
 * Component form tạo/chỉnh sửa Vector Store
 */
const VectorStoreForm: React.FC<VectorStoreFormProps> = ({ onSubmit, onCancel, initialValues, isLoading = false }) => {
  const { t } = useTranslation();

  // Gi<PERSON> trị mặc định cho form
  const defaultValues: VectorStoreFormValues = {
    name: initialValues?.name || '',
  };

  return (
    <Card className="space-y-4">
      <Typography variant="h6" className="mb-4">
        {t('data:vectorStore.form.title', 'Tạo Vector Store mới')}
      </Typography>

      <Form
        schema={vectorStoreSchema}
        defaultValues={defaultValues}
        onSubmit={onSubmit as (data: unknown) => void}
        className="space-y-4"
      >
        <FormItem name="name" label={t('data:vectorStore.form.name', 'Tên Vector Store')} required>
          <Input
            placeholder={t('data:vectorStore.form.namePlaceholder', 'Nhập tên Vector Store')}
            fullWidth
          />
        </FormItem>

        <div className="flex justify-end space-x-3 pt-4">
          <IconCard
            icon="x"
            title={t('common.cancel')}
            onClick={onCancel}
            variant="default"
          />
          <IconCard
            icon="plus"
            title={t('common.save')}
            onClick={() => {
              // Trigger form submission
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
            variant="primary"
            isLoading={isLoading}
          />
        </div>
      </Form>
    </Card>
  );
};

export default VectorStoreForm;
