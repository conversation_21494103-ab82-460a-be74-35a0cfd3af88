import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getCurrentUser,
  updatePersonalInfo,
  getBusinessInfo,
  updateBusinessInfo,
  getBankInfo,
  updateBankInfo,
  createAvatarUploadUrl,
  updateAvatar,
  getTwoFactorAuthStatus,
  toggleSmsAuth,
  toggleEmailAuth,
  setupGoogleAuth,
  verifyGoogleAuth,
  getNotificationSettings,
  updateNotificationSettings,
  getUserPoints,
  getBanks,
} from '../services/user.service';

// Query keys
export const USER_QUERY_KEYS = {
  profile: ['admin', 'profile', 'user'],
  businessInfo: ['admin', 'profile', 'businessInfo'],
  bankInfo: ['admin', 'profile', 'bankInfo'],
  twoFactorAuth: ['admin', 'profile', '2fa'],
  notificationSettings: ['admin', 'profile', 'notificationSettings'],
  points: ['admin', 'profile', 'points'],
  banks: ['admin', 'profile', 'banks'],
};

/**
 * Hook để lấy thông tin người dùng hiện tại
 */
export const useCurrentUser = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.profile,
    queryFn: getCurrentUser,
  });
};

/**
 * Hook để cập nhật thông tin cá nhân
 */
export const useUpdatePersonalInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updatePersonalInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
    },
  });
};

/**
 * Hook để lấy thông tin doanh nghiệp
 */
export const useBusinessInfo = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.businessInfo,
    queryFn: getBusinessInfo,
  });
};

/**
 * Hook để cập nhật thông tin doanh nghiệp
 */
export const useUpdateBusinessInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBusinessInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.businessInfo });
    },
  });
};

/**
 * Hook để lấy thông tin ngân hàng
 */
export const useBankInfo = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.bankInfo,
    queryFn: getBankInfo,
  });
};

/**
 * Hook để cập nhật thông tin ngân hàng
 */
export const useUpdateBankInfo = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateBankInfo,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.bankInfo });
    },
  });
};

/**
 * Hook để lấy danh sách ngân hàng
 */
export const useBanks = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.banks,
    queryFn: getBanks,
  });
};

/**
 * Hook để tạo URL upload avatar
 */
export const useCreateAvatarUploadUrl = () => {
  return useMutation({
    mutationFn: createAvatarUploadUrl,
  });
};

/**
 * Hook để cập nhật avatar
 */
export const useUpdateAvatar = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateAvatar,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.profile });
    },
  });
};

/**
 * Hook để lấy trạng thái xác thực 2 yếu tố
 */
export const useTwoFactorAuthStatus = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.twoFactorAuth,
    queryFn: getTwoFactorAuthStatus,
  });
};

/**
 * Hook để bật/tắt xác thực SMS
 */
export const useToggleSmsAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: toggleSmsAuth,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để bật/tắt xác thực Email
 */
export const useToggleEmailAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: toggleEmailAuth,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để thiết lập Google Authenticator
 */
export const useSetupGoogleAuth = () => {
  return useMutation({
    mutationFn: setupGoogleAuth,
  });
};

/**
 * Hook để xác minh Google Authenticator
 */
export const useVerifyGoogleAuth = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: verifyGoogleAuth,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.twoFactorAuth });
    },
  });
};

/**
 * Hook để lấy cài đặt thông báo
 */
export const useNotificationSettings = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.notificationSettings,
    queryFn: getNotificationSettings,
  });
};

/**
 * Hook để cập nhật cài đặt thông báo
 */
export const useUpdateNotificationSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateNotificationSettings,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.notificationSettings });
    },
  });
};

/**
 * Hook để lấy điểm người dùng
 */
export const useUserPoints = () => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.points,
    queryFn: getUserPoints,
  });
};
