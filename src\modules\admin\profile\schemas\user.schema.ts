import { z } from 'zod';

/**
 * Schema cho thông tin cá nhân
 */
export const personalInfoSchema = z.object({
  fullName: z
    .string()
    .min(1, 'Họ và tên là bắt buộc')
    .max(100, '<PERSON>ọ và tên không được vượt quá 100 ký tự'),
  
  email: z
    .string()
    .min(1, '<PERSON>ail là bắt buộc')
    .email('Email không hợp lệ'),
  
  phoneNumber: z
    .string()
    .min(1, 'Số điện thoại là bắt buộc')
    .regex(/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ'),
  
  gender: z
    .enum(['MALE', 'FEMALE', 'OTHER'])
    .optional(),
  
  dateOfBirth: z
    .string()
    .optional(),
  
  address: z
    .string()
    .max(255, '<PERSON><PERSON><PERSON> chỉ không được vượt quá 255 ký tự')
    .optional(),
});

/**
 * Schema cho thông tin doanh nghiệp
 */
export const businessInfoSchema = z.object({
  companyName: z
    .string()
    .min(1, 'Tên công ty là bắt buộc')
    .max(200, 'Tên công ty không được vượt quá 200 ký tự'),
  
  taxCode: z
    .string()
    .min(1, 'Mã số thuế là bắt buộc')
    .regex(/^[0-9]{10,13}$/, 'Mã số thuế không hợp lệ'),
  
  businessAddress: z
    .string()
    .min(1, 'Địa chỉ kinh doanh là bắt buộc')
    .max(500, 'Địa chỉ kinh doanh không được vượt quá 500 ký tự'),
  
  businessPhone: z
    .string()
    .min(1, 'Số điện thoại doanh nghiệp là bắt buộc')
    .regex(/^[0-9]{10,11}$/, 'Số điện thoại không hợp lệ'),
  
  businessEmail: z
    .string()
    .min(1, 'Email doanh nghiệp là bắt buộc')
    .email('Email không hợp lệ'),
});

/**
 * Schema cho thông tin ngân hàng
 */
export const bankInfoSchema = z.object({
  bankName: z
    .string()
    .min(1, 'Tên ngân hàng là bắt buộc'),
  
  accountNumber: z
    .string()
    .min(1, 'Số tài khoản là bắt buộc')
    .regex(/^[0-9]+$/, 'Số tài khoản chỉ được chứa số'),
  
  accountHolderName: z
    .string()
    .min(1, 'Tên chủ tài khoản là bắt buộc')
    .max(100, 'Tên chủ tài khoản không được vượt quá 100 ký tự'),
  
  branchName: z
    .string()
    .max(200, 'Tên chi nhánh không được vượt quá 200 ký tự')
    .optional(),
});

/**
 * Schema cho thông tin bảo mật
 */
export const securityInfoSchema = z.object({
  currentPassword: z
    .string()
    .min(1, 'Mật khẩu hiện tại là bắt buộc'),
  
  newPassword: z
    .string()
    .min(8, 'Mật khẩu mới phải có ít nhất 8 ký tự')
    .max(50, 'Mật khẩu mới không được vượt quá 50 ký tự'),
  
  confirmPassword: z
    .string()
    .min(1, 'Xác nhận mật khẩu là bắt buộc'),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: 'Mật khẩu xác nhận không khớp',
  path: ['confirmPassword'],
});

/**
 * Schema cho cài đặt thông báo
 */
export const notificationSettingsSchema = z.object({
  emailNotifications: z.boolean().default(true),
  smsNotifications: z.boolean().default(false),
  pushNotifications: z.boolean().default(true),
  marketingEmails: z.boolean().default(false),
  securityAlerts: z.boolean().default(true),
});
