import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Form,
  FormItem,
  Input,
  Button,
  Typography,
} from '@/shared/components/common';
import { useFormErrors } from '@/shared/hooks/form';
import { OCBBankAccountFormValues } from '../../types/banking.types';
import { ocbBankAccountSchema } from '../../schemas/banking.schema';
import { NotificationUtil } from '@/shared/utils/notification';

interface OCBBankAccountFormProps {
  /**
   * D<PERSON> liệu ban đầu (cho chế độ chỉnh sửa)
   */
  initialData?: OCBBankAccountFormValues;

  /**
   * Callback khi submit form
   */
  onSubmit: (values: OCBBankAccountFormValues) => Promise<void>;

  /**
   * Callback khi hủy
   */
  onCancel?: () => void;

  /**
   * Trạng thái loading
   */
  loading?: boolean;
}

/**
 * Form liên kết tài khoản ngân hàng OCB
 */
const OCBBankAccountForm: React.FC<OCBBankAccountFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  loading = false,
}) => {
  const { t } = useTranslation(['integrations', 'common']);
  const { formRef, setFormErrors } = useFormErrors();

  // State cho form data
  const [formData, setFormData] = useState<OCBBankAccountFormValues>({
    account_holder_name: initialData?.account_holder_name || '',
    account_number: initialData?.account_number || '',
    identification_number: initialData?.identification_number || '',
    phone_number: initialData?.phone_number || '',
    label: initialData?.label || '',
  });

  // State cho việc fetch tên chủ tài khoản
  const [fetchingAccountName, setFetchingAccountName] = useState(false);

  // Xử lý thay đổi input
  const handleInputChange = (field: keyof OCBBankAccountFormValues, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Mock API để lấy tên chủ tài khoản từ số tài khoản
  const fetchAccountHolderName = useCallback(async (accountNumber: string) => {
    if (!accountNumber || accountNumber.length < 6) return;

    setFetchingAccountName(true);
    try {
      // TODO: Thay thế bằng API thực tế
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call

      // Mock response - thay thế bằng API call thực tế
      const mockName = `Trần Thị B - ${accountNumber}`;

      setFormData(prev => ({
        ...prev,
        account_holder_name: mockName,
      }));

      NotificationUtil.success({
        message: t('integrations:banking.ocb.fetchNameSuccess', 'Đã lấy tên chủ tài khoản thành công'),
        duration: 3000,
      });
    } catch (error) {
      console.error('Error fetching account holder name:', error);
      NotificationUtil.error({
        message: t('integrations:banking.ocb.fetchNameError', 'Không thể lấy tên chủ tài khoản'),
        duration: 5000,
      });
    } finally {
      setFetchingAccountName(false);
    }
  }, [t]);

  // Auto-fetch account holder name when account number changes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.account_number && formData.account_number.length >= 6) {
        fetchAccountHolderName(formData.account_number);
      }
    }, 1000); // Debounce 1 second

    return () => clearTimeout(timeoutId);
  }, [formData.account_number, fetchAccountHolderName]);

  // Xử lý submit form
  const handleSubmit = async (data: OCBBankAccountFormValues) => {
    try {
      // Validate dữ liệu với Zod schema
      const validatedData = ocbBankAccountSchema.parse(data);

      // Prepare data for submission, ensuring optional fields are handled correctly
      const submitData: OCBBankAccountFormValues = {
        account_holder_name: validatedData.account_holder_name,
        account_number: validatedData.account_number,
        identification_number: validatedData.identification_number,
        phone_number: validatedData.phone_number,
        ...(validatedData.label && validatedData.label.trim() !== '' && { label: validatedData.label }),
      };

      // Gọi callback onSubmit
      await onSubmit(submitData);
    } catch (error) {
      if (error instanceof Error && 'issues' in error) {
        // Zod validation errors
        const zodError = error as { issues: Array<{ path: string[]; message: string }> };
        const fieldErrors: Partial<OCBBankAccountFormValues> = {};

        zodError.issues.forEach((issue: { path: string[]; message: string }) => {
          const fieldName = issue.path[0] as keyof OCBBankAccountFormValues;
          fieldErrors[fieldName] = issue.message;
        });

        setFormErrors(fieldErrors);
      } else {
        console.error('Form submission error:', error);
        NotificationUtil.error({
          message: t('integrations:banking.ocb.submitError', 'Có lỗi xảy ra khi lưu thông tin tài khoản'),
          duration: 5000,
        });
      }
    }
  };

  return (
    <div className="w-full bg-background text-foreground">
      <Card className="border-0">
        <div className="p-6">
          {/* Header */}
          <div className="mb-6">
            <Typography variant="h5" className="font-semibold mb-2">
              {t('integrations:banking.ocb.title', 'Liên kết tài khoản OCB')}
            </Typography>
            <Typography variant="body2" className="text-muted-foreground">
              {t('integrations:banking.ocb.description', 'Nhập thông tin tài khoản ngân hàng OCB để liên kết với hệ thống')}
            </Typography>
          </div>

          {/* Form */}
          <Form
            ref={formRef}
            schema={ocbBankAccountSchema}
            onSubmit={handleSubmit as (data: unknown) => void}
            defaultValues={formData}
          >
            <div className="space-y-4">
              {/* Số tài khoản */}
              <FormItem
                label={t('integrations:banking.ocb.accountNumber', 'Số tài khoản')}
                name="account_number"
                required
              >
                <Input
                  type="text"
                  value={formData.account_number}
                  onChange={(e) => handleInputChange('account_number', e.target.value)}
                  placeholder={t('integrations:banking.ocb.accountNumberPlaceholder', 'Nhập số tài khoản OCB')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên chủ tài khoản */}
              <FormItem
                label={t('integrations:banking.ocb.accountHolderName', 'Tên chủ tài khoản')}
                name="account_holder_name"
                required
              >
                <Input
                  type="text"
                  value={formData.account_holder_name}
                  onChange={(e) => handleInputChange('account_holder_name', e.target.value)}
                  placeholder={t('integrations:banking.ocb.accountHolderNamePlaceholder', 'Tên sẽ được tự động lấy từ API')}
                  fullWidth
                  disabled={fetchingAccountName}
                />
              </FormItem>

              {/* Số CMND/CCCD */}
              <FormItem
                label={t('integrations:banking.ocb.identificationNumber', 'Số CMND/CCCD')}
                name="identification_number"
                required
              >
                <Input
                  type="text"
                  value={formData.identification_number}
                  onChange={(e) => handleInputChange('identification_number', e.target.value)}
                  placeholder={t('integrations:banking.ocb.identificationNumberPlaceholder', 'Nhập số CMND/CCCD đăng ký OCB')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>

              {/* Số điện thoại */}
              <FormItem
                label={t('integrations:banking.ocb.phoneNumber', 'Số điện thoại')}
                name="phone_number"
                required
              >
                <Input
                  type="tel"
                  value={formData.phone_number}
                  onChange={(e) => handleInputChange('phone_number', e.target.value)}
                  placeholder={t('integrations:banking.ocb.phoneNumberPlaceholder', 'Nhập số điện thoại đăng ký OCB')}
                  maxLength={20}
                  fullWidth
                />
              </FormItem>

              {/* Tên gợi nhớ */}
              <FormItem
                label={t('integrations:banking.ocb.label', 'Tên gợi nhớ')}
                name="label"
              >
                <Input
                  type="text"
                  value={formData.label}
                  onChange={(e) => handleInputChange('label', e.target.value)}
                  placeholder={t('integrations:banking.ocb.labelPlaceholder', 'Nhập tên gợi nhớ (tùy chọn)')}
                  maxLength={100}
                  fullWidth
                />
              </FormItem>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={loading}
                >
                  {t('common:cancel', 'Hủy')}
                </Button>
              )}
              <Button
                type="submit"
                variant="primary"
                isLoading={loading}
              >
                {initialData 
                  ? t('common:update', 'Cập nhật')
                  : t('common:save', 'Lưu')
                }
              </Button>
            </div>
          </Form>
        </div>
      </Card>
    </div>
  );
};

export default OCBBankAccountForm;
