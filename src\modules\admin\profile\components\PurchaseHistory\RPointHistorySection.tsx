import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Table } from '@/shared/components/common';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { TableColumn } from '@/shared/components/common/Table/types';

// Type for R-Point history item
interface RPointHistoryItem {
  id: number;
  date: string;
  amount: number;
  points: number;
  method: string;
  status: string;
}

/**
 * Component hiển thị lịch sử nạp R-Point
 */
const RPointHistorySection: React.FC = () => {
  const { t } = useTranslation(['common', 'admin']);

  // Mock data cho R-Point history
  const mockRPointData = useMemo(() => Array.from({ length: 10 }, (_, index) => ({
    id: index + 1,
    date: '20/01/2023',
    amount: 50000 + (index * 10000),
    points: 5000 + (index * 1000),
    method: index % 2 === 0 ? 'Banking' : 'Credit Card',
    status: index % 3 === 0 ? 'Pending' : 'Completed',
  })), []);

  // Cấu hình columns cho table
  const columns = useMemo<TableColumn<RPointHistoryItem>[]>(() => [
    {
      title: t('admin:profile.rpoint.id', 'ID'),
      dataIndex: 'id',
      key: 'id',
      sortable: true,
    },
    {
      title: t('admin:profile.rpoint.date', 'Ngày nạp'),
      dataIndex: 'date',
      key: 'date',
      sortable: true,
    },
    {
      title: t('admin:profile.rpoint.amount', 'Số tiền'),
      dataIndex: 'amount',
      key: 'amount',
      sortable: true,
      render: (value: unknown) => `${(value as number).toLocaleString()} VND`,
    },
    {
      title: t('admin:profile.rpoint.points', 'R-Point'),
      dataIndex: 'points',
      key: 'points',
      sortable: true,
      render: (value: unknown) => (value as number).toLocaleString(),
    },
    {
      title: t('admin:profile.rpoint.method', 'Phương thức'),
      dataIndex: 'method',
      key: 'method',
    },
    {
      title: t('admin:profile.rpoint.status', 'Trạng thái'),
      dataIndex: 'status',
      key: 'status',
      render: (value: unknown) => (
        <span className={(value as string) === 'Completed' ? 'text-green-600' : 'text-yellow-600'}>
          {(value as string) === 'Completed' ? 'Hoàn thành' : 'Đang xử lý'}
        </span>
      ),
    },
  ], [t]);

  const dataTable = useDataTable(useDataTableConfig<RPointHistoryItem, Record<string, unknown>>({
    columns,
    createQueryParams: (params) => ({
      page: params.page,
      pageSize: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortOrder: params.sortDirection || undefined,
    }),
  }));

  return (
    <Card className="w-full">
      <div className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('admin:profile.rpoint.title', 'Lịch sử nạp R-Point')}
        </Typography>
        
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={mockRPointData}
          loading={false}
        />
      </div>
    </Card>
  );
};

export default RPointHistorySection;
