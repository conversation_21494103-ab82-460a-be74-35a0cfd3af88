import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Loading, Pagination, Typography } from '@/shared/components/common';
import { useTheme } from '@/shared/contexts';
import BlogGrid from '../components/BlogGrid';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { useGetBlogs } from '../hooks/useBlogList';
import { OwnershipType } from '../types/blog.types';

interface BlogPersonalPageProps {
  initialTag?: string;
}

/**
 * Trang hiển thị danh sách blog cá nhân (do người dùng sở hữu)
 */
const BlogPersonalPage: React.FC<BlogPersonalPageProps> = ({ initialTag }) => {
  const { t } = useTranslation();
  const [page, setPage] = useState(1);
  const [search, setSearch] = useState('');
  const [searchInput] = useState('');
  const [tag, setTag] = useState<string | undefined>(initialTag);
  const [, setFilterType] = useState('all');

  // Sử dụng hook theme mới
  useTheme();
  // Sử dụng hằng số cho limit vì không cần thay đổi trong phiên bản tối giản
  const limit = 10;

  // Sử dụng TanStack Query để lấy dữ liệu blog
  // Build query params conditionally to avoid undefined values
  const queryParams: Record<string, unknown> = {
    page,
    limit,
    search,
    ownership_type: OwnershipType.OWN // Lọc theo blog do người dùng sở hữu
  };

  // Only include tags if it has a value
  if (tag !== undefined) {
    queryParams['tags'] = tag; // Sử dụng tags thay vì tag để phù hợp với API
  }

  const { data, isLoading, error } = useGetBlogs(queryParams);
  
  // Xử lý khi thay đổi trang
  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const handleAddBlog = () => {
    console.log('Add new agent');
    // Xử lý logic khi thêm agent mới
  };

  const handleFilterChange = (type: string) => {
    setFilterType(type);
  };

  // Xử lý khi tìm kiếm
  const handleSearch = useCallback(() => {
    setSearch(searchInput);
    setPage(1);
  }, [searchInput]);

  // Không cần hàm formatDate nữa vì chúng ta sử dụng định dạng ngày trực tiếp

  // Reset page when tag changes
  useEffect(() => {
    setPage(1);
  }, [tag]);

  // Update tag when initialTag changes
  useEffect(() => {
    setTag(initialTag);
  }, [initialTag]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddBlog}
        items={[
          {
            id: 'all',
            label: t('common.all'),
            icon: 'list',
            onClick: () => handleFilterChange('all'),
          },
          {
            id: 'tag',
            label: t('chat.aiAssistants'),
            icon: 'assistant',
            onClick: () => handleFilterChange('assistant'),
          },
          {
            id: 'agent',
            label: t('chat.specializedAgents', 'Specialized Agents'),
            icon: 'robot',
            onClick: () => handleFilterChange('agent'),
          },
        ]}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="flex justify-center items-center min-h-[400px]">
          <Loading />
        </div>
      )}

      {/* Blog list */}
      {!isLoading && !error && data?.result && (
        <>
          <BlogGrid blogs={data.result.content} />

          {/* Pagination - Simplified and elegant */}
          {data.result.totalItems > 0 && (
            <div className="flex justify-end">
              <Pagination
                variant="compact"
                currentPage={page}
                totalPages={data.result.totalPages}
                onPageChange={handlePageChange}
                showFirstLastButtons={false}
                showItemsPerPageSelector={false}
                showPageInfo={false}
                maxPageButtons={5}
                size="md"
                borderless={true}
              />
            </div>
          )}

          {/* Empty state */}
          {data.result.content.length === 0 && (
            <div className="text-center py-12">
              <Typography variant="body1" color="muted" className="text-lg">
                {t('blog.noResults', 'Không tìm thấy bài viết nào.')}
              </Typography>
            </div>
          )}
        </>
      )}
    </div>
  );
};

export default BlogPersonalPage;
