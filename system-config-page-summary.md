# System Config Page Summary

## ✅ Đã xây dựng trang cấu hình hệ thống

### 📁 **File Location:**
- `src/modules/admin/config/pages/SystemConfigPage.tsx`
- `src/modules/admin/config/pages/index.ts`

### 🎯 **Cấu trúc trang gồm 3 phần chính:**

#### **1. Mẫu hợp đồng (CollapsibleCard 1) 📄**
- **Icon**: 📄 (document icon)
- **Default**: Mở sẵn (`defaultOpen={true}`)
- **4 loại upload file:**
  - ✅ Mẫu hợp đồng nguyên tắc dành cho cá nhân
  - ✅ Mẫu hợp đồng nguyên tắc dành cho doanh nghiệp  
  - ✅ Mẫu hợp đồng affiliate dành cho cá nhân
  - ✅ Mẫu hợp đồng affiliate dành cho doanh nghiệp
- **File types**: `.pdf`, `.doc`, `.docx`
- **UI**: Custom file upload buttons với hiển thị tên file đã chọn

#### **2. C<PERSON>u hình cổng thanh toán (CollapsibleCard 2) 💳**
- **Icon**: 💳 (credit card icon)
- **Default**: Đóng (`defaultOpen={false}`)
- **3 fields sử dụng FormItem + Input:**
  - ✅ Ngân hàng (required)
  - ✅ Số tài khoản (required)
  - ✅ Tên tài khoản (required)
- **Button**: "Lưu cấu hình" để save settings
- **Notification**: Success message khi lưu

#### **3. Cấu hình mẫu hóa đơn đầu vào (CollapsibleCard 3) 🧾**
- **Icon**: 🧾 (receipt icon)
- **Default**: Đóng (`defaultOpen={false}`)
- **File upload**: Chỉ chấp nhận PDF
- **Validation**: Kích thước tối đa 10MB
- **UI**: Custom PDF upload button

### 🎨 **UI Features:**

#### **CollapsibleCard Design:**
```typescript
<CollapsibleCard
  title={
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
        <span className="text-primary text-lg">📄</span>
      </div>
      <div>
        <Typography variant="h3" className="text-lg font-semibold">
          Title
        </Typography>
        <Typography variant="body2" className="text-muted-foreground">
          Description
        </Typography>
      </div>
    </div>
  }
  defaultOpen={true/false}
  className="mb-6"
>
```

#### **File Upload UI:**
```typescript
<input
  type="file"
  accept=".pdf,.doc,.docx"
  onChange={(e) => handleFileUpload('type', e.target.files?.[0] || null)}
  className="hidden"
  id="upload-id"
/>
<label
  htmlFor="upload-id"
  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
>
  Chọn file
</label>
```

#### **FormItem Usage:**
```typescript
<FormItem name="bankName" label="Ngân hàng" required>
  <Input
    value={paymentConfig.bankName}
    onChange={(e) => handlePaymentConfigChange('bankName', e.target.value)}
    placeholder="Nhập tên ngân hàng"
    fullWidth
  />
</FormItem>
```

### 🔧 **State Management:**

#### **File Upload State:**
```typescript
const [contractFiles, setContractFiles] = useState({
  personalPrinciple: null as File | null,
  businessPrinciple: null as File | null,
  personalAffiliate: null as File | null,
  businessAffiliate: null as File | null,
  invoiceTemplate: null as File | null,
});
```

#### **Payment Config State:**
```typescript
const [paymentConfig, setPaymentConfig] = useState({
  bankName: '',
  accountNumber: '',
  accountName: '',
});
```

### 🎯 **Functionality:**

#### **File Upload Handler:**
- ✅ Handle multiple file types
- ✅ Update state với file đã chọn
- ✅ Show success notification
- ✅ Display selected file name

#### **Payment Config Handler:**
- ✅ Update form fields
- ✅ Save configuration
- ✅ Show success notification

#### **Notifications:**
- ✅ File upload success
- ✅ Payment config save success
- ✅ Using NotificationUtil

### 📱 **Responsive Design:**
- ✅ Full width layout
- ✅ Proper spacing với `space-y-6`
- ✅ Card margins với `mb-6`
- ✅ Responsive typography
- ✅ Hover effects trên buttons

### 🎨 **Visual Design:**
- ✅ **Icons**: Emoji icons cho mỗi section
- ✅ **Colors**: Primary, success, warning themes
- ✅ **Typography**: Proper heading hierarchy
- ✅ **Spacing**: Consistent spacing system
- ✅ **Buttons**: Primary styled với hover effects

### 🚀 **Usage:**
```typescript
import { SystemConfigPage } from '@/modules/admin/config/pages';

// Trong router
{
  path: '/admin/config/system',
  element: <SystemConfigPage />,
}
```

### 📝 **Notes:**
- ✅ **Chỉ UI**: Không có API calls thực tế
- ✅ **TODO comments**: Đã đánh dấu nơi cần implement API
- ✅ **File validation**: Accept types đã được set
- ✅ **Accessibility**: Proper labels và IDs
- ✅ **TypeScript**: Full type safety

## Status: ✅ Completed
Trang cấu hình hệ thống đã được xây dựng hoàn chỉnh theo yêu cầu!
