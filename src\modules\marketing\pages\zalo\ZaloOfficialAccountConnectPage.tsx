import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { FieldValues } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

// UI Components
import { Card, Form, FormItem, PasswordInput, Typography } from '@/shared/components/common';
import { IconCard } from '@/shared/components/common';
import { FormRef } from '@/shared/components/common/Form/Form';
import OverviewCard from '@/shared/components/widgets/OverviewCard/OverviewCard';
import { ResponsiveGrid } from '@/shared/components/common';
import { ShieldCheck, Code } from 'lucide-react';

// Hooks and Services
import { useConnectOfficialAccount } from '../../hooks/zalo/useZaloConnect';
import { useZaloOAuthUrl } from '@/modules/integration/hooks/useZaloOAuth';
import {
  connectOfficialAccountSchema,
  type ConnectOfficialAccountFormData,
} from '../../schemas/zalo.schema';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang kết nối Zalo Official Account với hệ thống
 */
const ZaloOfficialAccountConnectPage: React.FC = () => {
  const { t } = useTranslation('marketing');
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedMethod, setSelectedMethod] = useState<'oauth' | 'api' | null>(null);
  const formRef = useRef<FormRef<FieldValues>>(null);

  // Hook kết nối Official Account
  const connectMutation = useConnectOfficialAccount();
  const oauthMutation = useZaloOAuthUrl();

  // Handle form submission
  const onSubmit = async (data: FieldValues) => {
    setIsSubmitting(true);

    try {
      const formData = data as ConnectOfficialAccountFormData;
      const response = await connectMutation.mutateAsync(formData);

      if (response.code === 0 && response.result) {
        NotificationUtil.success({
          message: t('zalo:accounts.connect.page.success'),
        });

        // Redirect to accounts page or dashboard
        navigate('/marketing/zalo/accounts');
      } else {
        NotificationUtil.error({
          message: response.message || t('zalo:accounts.connect.page.error'),
        });
      }
    } catch (error: unknown) {
      console.error('Lỗi kết nối Official Account:', error);
      const errorMessage =
        error instanceof Error ? error.message : 'Không thể kết nối Official Account';
      NotificationUtil.error({
        message: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle OAuth method
  const handleOAuthMethod = () => {
    setSelectedMethod('oauth');
    const redirectUri = import.meta.env['VITE_ZALO_OA_REDIRECT_URI'] || 'https://v2.redai.vn/integration/zalo/oa';
    oauthMutation.mutate(redirectUri);
  };

  // Handle API Explorer method
  const handleApiMethod = () => {
    setSelectedMethod('api');
  };

  // Handle cancel
  const handleCancel = () => {
    if (selectedMethod) {
      setSelectedMethod(null);
    } else {
      navigate(-1); // Go back to previous page
    }
  };

  // Integration options
  const integrationOptions = [
    {
      title: t('zalo:accounts.connect.page.methods.oauth.title'),
      value: t('zalo:accounts.connect.page.methods.oauth.value'),
      description: t('zalo:accounts.connect.page.methods.oauth.description'),
      icon: ShieldCheck,
      color: 'green' as const,
      onClick: handleOAuthMethod,
      hoverable: true,
    },
    {
      title: t('zalo:accounts.connect.page.methods.api.title'),
      value: t('zalo:accounts.connect.page.methods.api.value'),
      description: t('zalo:accounts.connect.page.methods.api.description'),
      icon: Code,
      color: 'blue' as const,
      onClick: handleApiMethod,
      hoverable: true,
    },
  ];

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Header */}
        <div className="text-center">
          <Typography variant="h3" className="mb-2">
            {t('zalo:accounts.connect.page.title')}
          </Typography>
          <Typography variant="body1" color="muted">
            {t('zalo:accounts.connect.page.description')}
          </Typography>
        </div>

        {/* Integration Options */}
        <div className="max-w-4xl mx-auto">
          <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2 }} gap={6}>
            {integrationOptions.map((option, index) => (
              <OverviewCard
                key={index}
                {...option}
              />
            ))}
          </ResponsiveGrid>
        </div>

        {/* API Form */}
        {selectedMethod === 'api' && (
          /* API Form */
          <div className="max-w-2xl mx-auto">
            <Card className="p-6">
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <Typography variant="h5" className="mb-2">
                    {t('zalo:accounts.connect.page.methods.api.formTitle')}
                  </Typography>
                  <Typography variant="body2" color="muted">
                    {t('zalo:accounts.connect.page.methods.api.formDescription')}
                  </Typography>
                </div>

                <Form
                  ref={formRef}
                  schema={connectOfficialAccountSchema}
                  onSubmit={onSubmit}
                  defaultValues={{
                    accessToken: '',
                    refreshToken: '',
                  }}
                >
                  <div className="space-y-6">
                    {/* Access Token */}
                    <FormItem name="accessToken" label={t('zalo:accounts.connect.page.methods.api.accessTokenLabel')} required>
                      <PasswordInput
                        placeholder={t('zalo:accounts.connect.page.methods.api.accessTokenPlaceholder')}
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Refresh Token */}
                    <FormItem name="refreshToken" label={t('zalo:accounts.connect.page.methods.api.refreshTokenLabel')} required>
                      <PasswordInput
                        placeholder={t('zalo:accounts.connect.page.methods.api.refreshTokenPlaceholder')}
                        disabled={isSubmitting}
                        fullWidth
                      />
                    </FormItem>

                    {/* Action Buttons */}
                    <div className="flex justify-end space-x-4 pt-6">
                      <IconCard
                        icon="arrow-left"
                        title={t('zalo:accounts.connect.page.methods.api.backButton')}
                        onClick={handleCancel}
                        className="cursor-pointer"
                        disabled={isSubmitting}
                      />
                      <IconCard
                        icon="check"
                        title={t('zalo:accounts.connect.page.methods.api.connectButton')}
                        onClick={() => formRef.current?.submit()}
                        variant="primary"
                        disabled={isSubmitting}
                        className="cursor-pointer"
                      />
                    </div>
                  </div>
                </Form>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default ZaloOfficialAccountConnectPage;
