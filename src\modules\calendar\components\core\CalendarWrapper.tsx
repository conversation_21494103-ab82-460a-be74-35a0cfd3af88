import React, { useState, useEffect } from 'react';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import listPlugin from '@fullcalendar/list';
import viLocale from '@fullcalendar/core/locales/vi';
import { CalendarProps, CalendarEvent } from '../../types';
import { useTheme } from '@/shared/contexts/theme';
import { cn } from '@/shared/utils/cn';
import useCalendar from '../../hooks/useCalendar';
import CalendarHeader from '../ui/CalendarHeader';
import CalendarToolbar from '../ui/CalendarToolbar';
import EventForm from '../events/EventForm';
import { DateSelectArg } from '@fullcalendar/core';
import type { IconName } from '@/shared/components/common/Icon';

/**
 * Component wrapper cho calendar với đầy đủ tính năng
 *
 * @example
 * ```tsx
 * <CalendarWrapper
 *   events={myEvents}
 *   initialView="dayGridMonth"
 *   editable={true}
 *   selectable={true}
 *   onEventClick={(info) => console.log('Event clicked:', info.event.title)}
 * />
 * ```
 */
const CalendarWrapper: React.FC<CalendarProps> = ({
  events = [],
  initialDate,
  initialView = 'dayGridMonth',
  weekends = true,
  editable = true,
  selectable = true,
  allDaySlot = false,
  height = 'auto',
  className = '',
  onDateSelect,
  // Các callback từ props (không sử dụng trực tiếp)
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onEventClick: _onEventClick,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  onEventChange: _onEventChange,
  onAddEvent,
  onUpdateEvent,
  onDeleteEvent,
}) => {
  // Sử dụng hook useCalendar để quản lý state và logic
  const {
    events: calendarEvents,
    currentView,
    calendarRef,
    calendarTitle,
    // Không sử dụng trong component này
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    selectedEvent: _selectedEvent,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    setSelectedEvent: _setSelectedEvent,
    handleDateSelect: handleDateSelectInternal,
    handleEventClick: handleEventClickInternal,
    handleEventChange: handleEventChangeInternal,
    handleViewChange,
    handlePrev,
    handleNext,
    handleToday,
    addEvent,
    // Không sử dụng trong component này
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    updateEvent: _updateEvent,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    deleteEvent: _deleteEvent,
  } = useCalendar({
    initialEvents: events,
    initialView,
    onAddEvent,
    onUpdateEvent,
    onDeleteEvent,
  });

  // State
  const [showWeekends, setShowWeekends] = useState<boolean>(weekends);
  const [showEventModal, setShowEventModal] = useState<boolean>(false);
  const [newEvent, setNewEvent] = useState<CalendarEvent | null>(null);

  // Theme
  const { themeMode } = useTheme();

  // Xử lý khi chọn ngày/khoảng thời gian
  const handleDateSelect = (selectInfo: DateSelectArg) => {
    // Gọi callback nội bộ
    handleDateSelectInternal(selectInfo);

    // Tạo sự kiện mới
    const calendarApi = selectInfo.view.calendar;
    calendarApi.unselect();

    const start = selectInfo.start;
    const end = selectInfo.end;

    // Tạo sự kiện mới
    const event: CalendarEvent = {
      id: '',
      title: '',
      start,
      end,
      allDay: selectInfo.allDay,
      extendedProps: {
        type: 'meeting',
      },
    };

    setNewEvent(event);
    setShowEventModal(true);

    // Gọi callback từ props nếu có
    if (onDateSelect) {
      onDateSelect(selectInfo);
    }
  };

  // Xử lý khi thêm sự kiện mới
  const handleAddEvent = () => {
    const start = new Date();
    const end = new Date();
    end.setHours(end.getHours() + 1);

    // Tạo sự kiện mới
    const event: CalendarEvent = {
      id: '',
      title: '',
      start,
      end,
      allDay: false,
      extendedProps: {
        type: 'meeting',
      },
    };

    setNewEvent(event);
    setShowEventModal(true);
  };

  // Xử lý khi lưu sự kiện mới
  const handleSaveEvent = () => {
    if (newEvent && newEvent.title.trim()) {
      // Thêm sự kiện mới
      addEvent(newEvent);

      // Đóng modal
      setShowEventModal(false);
      setNewEvent(null);
    }
  };

  // Xử lý khi cập nhật trường của sự kiện mới
  const updateNewEventField = (
    field: keyof CalendarEvent,
    value: string | Date | boolean | undefined
  ) => {
    if (newEvent) {
      setNewEvent({
        ...newEvent,
        [field]: value,
      });
    }
  };

  // Xử lý khi cập nhật loại sự kiện
  const updateEventType = (
    type: 'meeting' | 'appointment' | 'deadline' | 'training' | 'workshop' | 'planning' | 'event' | 'leave' | 'client' | 'personal'
  ) => {
    if (newEvent) {
      setNewEvent({
        ...newEvent,
        className: `calendar-event-${type}`,
        extendedProps: {
          ...newEvent.extendedProps,
          type,
        },
      });
    }
  };

  // Danh sách các loại sự kiện
  const eventTypes: Array<{
    value: string;
    label: string;
    color: string;
    icon: IconName;
  }> = [
    {
      value: 'meeting',
      label: 'Cuộc họp',
      color: 'blue-500',
      icon: 'users'
    },
    {
      value: 'appointment',
      label: 'Cuộc hẹn',
      color: 'green-500',
      icon: 'calendar'
    },
    {
      value: 'deadline',
      label: 'Hạn chót',
      color: 'red-500',
      icon: 'alert-circle'
    },
    {
      value: 'training',
      label: 'Đào tạo',
      color: 'purple-500',
      icon: 'document'
    },
    {
      value: 'workshop',
      label: 'Hội thảo',
      color: 'orange-500',
      icon: 'presentation'
    },
    {
      value: 'planning',
      label: 'Lập kế hoạch',
      color: 'yellow-500',
      icon: 'layout'
    },
    {
      value: 'event',
      label: 'Sự kiện',
      color: 'pink-500',
      icon: 'star'
    },
    {
      value: 'leave',
      label: 'Nghỉ phép',
      color: 'indigo-500',
      icon: 'home'
    },
    {
      value: 'client',
      label: 'Khách hàng',
      color: 'teal-500',
      icon: 'building'
    },
    {
      value: 'personal',
      label: 'Cá nhân',
      color: 'gray-500',
      icon: 'user'
    },
  ];

  // Xác định class cho calendar container
  const calendarClass = cn(
    'calendar-container',
    themeMode === 'dark' ? 'fc-theme-dark' : 'fc-theme-light',
    className
  );

  // Đảm bảo calendar được render đúng khi theme thay đổi
  useEffect(() => {
    const timer = setTimeout(() => {
      if (calendarRef.current) {
        const api = calendarRef.current.getApi();
        api.updateSize();
      }
    }, 100);

    return () => clearTimeout(timer);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [themeMode]);

  return (
    <div className="calendar-wrapper">
      {/* Toolbar với các tùy chọn lọc */}
      <CalendarToolbar
        weekends={showWeekends}
        onWeekendsToggle={setShowWeekends}
        eventTypes={eventTypes}
        className="mb-4"
      />

      {/* Calendar container */}
      <div className={calendarClass}>
        {/* Header với các nút điều hướng */}
        <CalendarHeader
          title={calendarTitle}
          currentView={currentView}
          onPrev={handlePrev}
          onNext={handleNext}
          onToday={handleToday}
          onViewChange={handleViewChange}
          onAddEvent={handleAddEvent}
        />

        {/* FullCalendar component */}
        <FullCalendar
          ref={calendarRef}
          key={`calendar-${themeMode}`}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin, listPlugin]}
          initialView={initialView}
          {...(initialDate && { initialDate })}
          locale={viLocale}
          headerToolbar={false}
          events={calendarEvents.map(event => {
            // Build event object conditionally to avoid undefined values
            const eventInput: {
              id: string;
              title: string;
              start: string | Date;
              end?: string | Date;
              allDay?: boolean;
              backgroundColor?: string;
              borderColor?: string;
              textColor?: string;
              classNames?: string[];
            } = {
              id: event.id,
              title: event.title,
              start: event.start,
            };

            // Only include optional properties if they have values
            if (event.end !== undefined) {
              eventInput.end = event.end;
            }

            if (event.allDay !== undefined) {
              eventInput.allDay = event.allDay;
            }

            if (event.backgroundColor !== undefined) {
              eventInput.backgroundColor = event.backgroundColor;
            }

            if (event.borderColor !== undefined) {
              eventInput.borderColor = event.borderColor;
            }

            if (event.textColor !== undefined) {
              eventInput.textColor = event.textColor;
            }

            if (event.classNames !== undefined) {
              eventInput.classNames = event.classNames;
            }

            return eventInput;
          })}
          editable={editable}
          selectable={selectable}
          selectMirror={true}
          dayMaxEvents={true}
          weekends={showWeekends}
          allDaySlot={allDaySlot}
          height={height}
          select={handleDateSelect}
          eventClick={handleEventClickInternal}
          eventChange={handleEventChangeInternal}
          eventTimeFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }}
          slotLabelFormat={{
            hour: '2-digit',
            minute: '2-digit',
            hour12: false,
          }}
          eventDidMount={info => {
            // Đảm bảo class được áp dụng đúng
            if (info.event.extendedProps?.['type']) {
              const type = info.event.extendedProps['type'];
              info.el.classList.add(`calendar-event-${type}`);
            }
          }}
          themeSystem="standard"
          firstDay={1} // Bắt đầu từ thứ 2
          fixedWeekCount={false} // Số tuần hiển thị linh hoạt theo tháng
          showNonCurrentDates={false} // Ẩn các ngày không thuộc tháng hiện tại
        />
      </div>

      {/* Form thêm/sửa sự kiện */}
      {showEventModal && newEvent && (
        <EventForm
          event={newEvent}
          eventTypes={eventTypes}
          isOpen={showEventModal}
          title="Thêm sự kiện"
          onClose={() => setShowEventModal(false)}
          onSave={handleSaveEvent}
          onUpdateField={updateNewEventField}
          onUpdateEventType={updateEventType}
        />
      )}
    </div>
  );
};

export default CalendarWrapper;
