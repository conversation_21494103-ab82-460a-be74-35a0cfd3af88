import React, { useMemo } from 'react';
import { ListOverviewCard } from '@/shared/components/widgets';
import { useDataOverview } from '@/modules/data/hooks/useDataOverview';
import { Database, FileText, Link, HardDrive, Hash } from 'lucide-react';

/**
 * Widget hiển thị tổng số lượng dữ liệu chi tiết
 */
const DataCountWidget: React.FC = () => {
  const { data: overviewData, isLoading: isOverviewLoading } = useDataOverview();

  const dataCountStats = useMemo(() => [
    {
      title: 'Tổng số lượng dữ liệu',
      value: (overviewData?.totalMedia || 0) + 
             (overviewData?.totalKnowledgeFiles || 0) + 
             (overviewData?.totalUrls || 0) + 
             (overviewData?.totalVectorStores || 0),
      icon: Hash,
      color: 'blue' as const,
      isLoading: isOverviewLoading,
      description: 'Tổng cộng tất cả dữ liệu trong hệ thống'
    },
    {
      title: 'Media Files',
      value: overviewData?.totalMedia || 0,
      icon: FileText,
      color: 'green' as const,
      isLoading: isOverviewLoading,
      description: 'Hình ảnh, video, âm thanh và tài liệu'
    },
    {
      title: 'Knowledge Files',
      value: overviewData?.totalKnowledgeFiles || 0,
      icon: Database,
      color: 'red' as const,
      isLoading: isOverviewLoading,
      description: 'Tệp tin tri thức cho AI và vector store'
    },
    {
      title: 'URLs',
      value: overviewData?.totalUrls || 0,
      icon: Link,
      color: 'orange' as const,
      isLoading: isOverviewLoading,
      description: 'Liên kết và tài nguyên web'
    },
    {
      title: 'Vector Stores',
      value: overviewData?.totalVectorStores || 0,
      icon: HardDrive,
      color: 'purple' as const,
      isLoading: isOverviewLoading,
      description: 'Kho lưu trữ vector cho tìm kiếm ngữ nghĩa'
    },
  ], [overviewData, isOverviewLoading]);

  return (
    <div className="h-full flex flex-col overflow-hidden p-2">
      {/* Overview Cards - Fully responsive */}
      <div className="flex-1 min-h-0 overflow-y-auto">
        <ListOverviewCard
          items={dataCountStats}
          maxColumns={{ xs: 1, sm: 1, md: 2, lg: 3, xl: 4 }}
          gap={2}
          isLoading={isOverviewLoading}
          skeletonCount={5}
        />
      </div>
    </div>
  );
};

export default DataCountWidget;
