import { useQuery } from '@tanstack/react-query';
import { getPurchaseHistory } from '../services/purchase-history.service';
import { PurchaseHistoryQueryParams } from '../types/purchase-history.types';

// Query keys
export const PURCHASE_HISTORY_QUERY_KEYS = {
  all: ['admin', 'profile', 'purchase-history'] as const,
  list: (params: PurchaseHistoryQueryParams) => [...PURCHASE_HISTORY_QUERY_KEYS.all, 'list', params] as const,
};

/**
 * Hook để lấy lịch sử mua hàng
 */
export const usePurchaseHistory = (params: PurchaseHistoryQueryParams = {}) => {
  return useQuery({
    queryKey: PURCHASE_HISTORY_QUERY_KEYS.list(params),
    queryFn: () => getPurchaseHistory(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
