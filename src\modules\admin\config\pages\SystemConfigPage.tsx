import React, { useState } from 'react';

import { Typo<PERSON>, Button, FormItem, Input } from '@/shared/components/common';
import CollapsibleCard from '@/shared/components/common/CollapsibleCard';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang cấu hình hệ thống
 */
const SystemConfigPage: React.FC = () => {
  // State cho file uploads
  const [contractFiles, setContractFiles] = useState({
    personalPrinciple: [] as FileWithMetadata[],
    businessPrinciple: [] as FileWithMetadata[],
    personalAffiliate: [] as FileWithMetadata[],
    businessAffiliate: [] as FileWithMetadata[],
    invoiceTemplate: [] as FileWithMetadata[],
  });

  // State cho cấu hình thanh toán
  const [paymentConfig, setPaymentConfig] = useState({
    bankName: '',
    accountNumber: '',
    accountName: '',
  });

  // Handle file upload
  const handleFileUpload = (type: keyof typeof contractFiles, files: FileWithMetadata[]) => {
    setContractFiles(prev => ({
      ...prev,
      [type]: files,
    }));

    if (files.length > 0) {
      NotificationUtil.success({
        title: 'Upload thành công',
        message: `Đã upload ${files.length} file`,
        duration: 3000,
      });
    }
  };

  // Handle payment config change
  const handlePaymentConfigChange = (field: keyof typeof paymentConfig, value: string) => {
    setPaymentConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Save payment config
  const handleSavePaymentConfig = () => {
    // TODO: Implement API call
    NotificationUtil.success({
      title: 'Lưu thành công',
      message: 'Cấu hình cổng thanh toán đã được lưu',
      duration: 3000,
    });
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* 1. Mẫu hợp đồng */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <span className="text-primary text-lg">📄</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Mẫu hợp đồng
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Upload các mẫu hợp đồng cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={true}
          className="mb-6"
        >
          <div className="space-y-6">
            {/* Hợp đồng nguyên tắc cá nhân */}
            <MultiFileUpload
              label="Mẫu hợp đồng nguyên tắc dành cho cá nhân"
              value={contractFiles.personalPrinciple}
              onChange={files => handleFileUpload('personalPrinciple', files)}
              accept=".pdf,.doc,.docx"
              placeholder="Kéo thả hoặc click để tải lên mẫu hợp đồng nguyên tắc cá nhân"
              height="h-32"
            />

            {/* Hợp đồng nguyên tắc doanh nghiệp */}
            <MultiFileUpload
              label="Mẫu hợp đồng nguyên tắc dành cho doanh nghiệp"
              value={contractFiles.businessPrinciple}
              onChange={files => handleFileUpload('businessPrinciple', files)}
              accept=".pdf,.doc,.docx"
              placeholder="Kéo thả hoặc click để tải lên mẫu hợp đồng nguyên tắc doanh nghiệp"
              height="h-32"
            />

            {/* Hợp đồng affiliate cá nhân */}
            <MultiFileUpload
              label="Mẫu hợp đồng affiliate dành cho cá nhân"
              value={contractFiles.personalAffiliate}
              onChange={files => handleFileUpload('personalAffiliate', files)}
              accept=".pdf,.doc,.docx"
              placeholder="Kéo thả hoặc click để tải lên mẫu hợp đồng affiliate cá nhân"
              height="h-32"
            />

            {/* Hợp đồng affiliate doanh nghiệp */}
            <MultiFileUpload
              label="Mẫu hợp đồng affiliate dành cho doanh nghiệp"
              value={contractFiles.businessAffiliate}
              onChange={files => handleFileUpload('businessAffiliate', files)}
              accept=".pdf,.doc,.docx"
              placeholder="Kéo thả hoặc click để tải lên mẫu hợp đồng affiliate doanh nghiệp"
              height="h-32"
            />
          </div>
        </CollapsibleCard>

        {/* 2. Cấu hình cổng thanh toán */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                <span className="text-success text-lg">💳</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Cấu hình cổng thanh toán
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Thiết lập thông tin ngân hàng cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-6"
        >
          <div className="space-y-6">
            <FormItem name="bankName" label="Ngân hàng" required>
              <Input
                value={paymentConfig.bankName}
                onChange={e => handlePaymentConfigChange('bankName', e.target.value)}
                placeholder="Nhập tên ngân hàng"
                fullWidth
              />
            </FormItem>

            <FormItem name="accountNumber" label="Số tài khoản" required>
              <Input
                value={paymentConfig.accountNumber}
                onChange={e => handlePaymentConfigChange('accountNumber', e.target.value)}
                placeholder="Nhập số tài khoản"
                fullWidth
              />
            </FormItem>

            <FormItem name="accountName" label="Tên tài khoản" required>
              <Input
                value={paymentConfig.accountName}
                onChange={e => handlePaymentConfigChange('accountName', e.target.value)}
                placeholder="Nhập tên chủ tài khoản"
                fullWidth
              />
            </FormItem>

            <div className="flex justify-end">
              <Button onClick={handleSavePaymentConfig} variant="primary" className="px-6">
                Lưu cấu hình
              </Button>
            </div>
          </div>
        </CollapsibleCard>

        {/* 3. Cấu hình mẫu hóa đơn */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
                <span className="text-warning text-lg">🧾</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Cấu hình mẫu hóa đơn đầu vào
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Upload mẫu hóa đơn PDF cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-6"
        >
          <div className="space-y-4">
            <Typography variant="body1" className="text-muted-foreground">
              Chỉ chấp nhận file PDF. Kích thước tối đa: 10MB
            </Typography>

            <MultiFileUpload
              label="Mẫu hóa đơn đầu vào"
              value={contractFiles.invoiceTemplate}
              onChange={files => handleFileUpload('invoiceTemplate', files)}
              accept=".pdf"
              placeholder="Kéo thả hoặc click để tải lên mẫu hóa đơn PDF (tối đa 10MB)"
              height="h-32"
            />
          </div>
        </CollapsibleCard>
      </div>
    </div>
  );
};

export default SystemConfigPage;
