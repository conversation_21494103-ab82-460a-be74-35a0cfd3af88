import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {  Typography, Button } from '@/shared/components/common';
import { FormItem } from '@/shared/components/ui/Form/FormItem';
import { Input } from '@/shared/components/ui/Input';
import CollapsibleCard from '@/shared/components/common/CollapsibleCard';
import { NotificationUtil } from '@/shared/utils/notification';

/**
 * Trang cấu hình hệ thống
 */
const SystemConfigPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State cho file uploads
  const [contractFiles, setContractFiles] = useState({
    personalPrinciple: null as File | null,
    businessPrinciple: null as File | null,
    personalAffiliate: null as File | null,
    businessAffiliate: null as File | null,
    invoiceTemplate: null as File | null,
  });

  // State cho cấu hình thanh toán
  const [paymentConfig, setPaymentConfig] = useState({
    bankName: '',
    accountNumber: '',
    accountName: '',
  });

  // Handle file upload
  const handleFileUpload = (type: keyof typeof contractFiles, file: File | null) => {
    setContractFiles(prev => ({
      ...prev,
      [type]: file,
    }));
    
    if (file) {
      NotificationUtil.success({
        title: 'Upload thành công',
        message: `Đã upload file ${file.name}`,
        duration: 3000,
      });
    }
  };

  // Handle payment config change
  const handlePaymentConfigChange = (field: keyof typeof paymentConfig, value: string) => {
    setPaymentConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  // Save payment config
  const handleSavePaymentConfig = () => {
    // TODO: Implement API call
    NotificationUtil.success({
      title: 'Lưu thành công',
      message: 'Cấu hình cổng thanh toán đã được lưu',
      duration: 3000,
    });
  };

  return (
    <div className="w-full bg-background text-foreground">
      <div className="space-y-6">
        {/* Page Header */}
        <div className="mb-8">
          <Typography variant="h1" className="text-3xl font-bold mb-2">
            Cấu hình hệ thống
          </Typography>
          <Typography variant="body1" className="text-muted-foreground">
            Quản lý các cấu hình chung của hệ thống
          </Typography>
        </div>

        {/* 1. Mẫu hợp đồng */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                <span className="text-primary text-lg">📄</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Mẫu hợp đồng
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Upload các mẫu hợp đồng cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={true}
          className="mb-6"
        >
          <div className="space-y-6">
            {/* Hợp đồng nguyên tắc cá nhân */}
            <div className="space-y-2">
              <Typography variant="h4" className="font-medium">
                Mẫu hợp đồng nguyên tắc dành cho cá nhân
              </Typography>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => handleFileUpload('personalPrinciple', e.target.files?.[0] || null)}
                  className="hidden"
                  id="personal-principle-upload"
                />
                <label
                  htmlFor="personal-principle-upload"
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  Chọn file
                </label>
                {contractFiles.personalPrinciple && (
                  <span className="text-sm text-muted-foreground">
                    {contractFiles.personalPrinciple.name}
                  </span>
                )}
              </div>
            </div>

            {/* Hợp đồng nguyên tắc doanh nghiệp */}
            <div className="space-y-2">
              <Typography variant="h4" className="font-medium">
                Mẫu hợp đồng nguyên tắc dành cho doanh nghiệp
              </Typography>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => handleFileUpload('businessPrinciple', e.target.files?.[0] || null)}
                  className="hidden"
                  id="business-principle-upload"
                />
                <label
                  htmlFor="business-principle-upload"
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  Chọn file
                </label>
                {contractFiles.businessPrinciple && (
                  <span className="text-sm text-muted-foreground">
                    {contractFiles.businessPrinciple.name}
                  </span>
                )}
              </div>
            </div>

            {/* Hợp đồng affiliate cá nhân */}
            <div className="space-y-2">
              <Typography variant="h4" className="font-medium">
                Mẫu hợp đồng affiliate dành cho cá nhân
              </Typography>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => handleFileUpload('personalAffiliate', e.target.files?.[0] || null)}
                  className="hidden"
                  id="personal-affiliate-upload"
                />
                <label
                  htmlFor="personal-affiliate-upload"
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  Chọn file
                </label>
                {contractFiles.personalAffiliate && (
                  <span className="text-sm text-muted-foreground">
                    {contractFiles.personalAffiliate.name}
                  </span>
                )}
              </div>
            </div>

            {/* Hợp đồng affiliate doanh nghiệp */}
            <div className="space-y-2">
              <Typography variant="h4" className="font-medium">
                Mẫu hợp đồng affiliate dành cho doanh nghiệp
              </Typography>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={(e) => handleFileUpload('businessAffiliate', e.target.files?.[0] || null)}
                  className="hidden"
                  id="business-affiliate-upload"
                />
                <label
                  htmlFor="business-affiliate-upload"
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  Chọn file
                </label>
                {contractFiles.businessAffiliate && (
                  <span className="text-sm text-muted-foreground">
                    {contractFiles.businessAffiliate.name}
                  </span>
                )}
              </div>
            </div>
          </div>
        </CollapsibleCard>

        {/* 2. Cấu hình cổng thanh toán */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-success/10 rounded-lg flex items-center justify-center">
                <span className="text-success text-lg">💳</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Cấu hình cổng thanh toán
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Thiết lập thông tin ngân hàng cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-6"
        >
          <div className="space-y-6">
            <FormItem
              name="bankName"
              label="Ngân hàng"
              required
            >
              <Input
                value={paymentConfig.bankName}
                onChange={(e) => handlePaymentConfigChange('bankName', e.target.value)}
                placeholder="Nhập tên ngân hàng"
                fullWidth
              />
            </FormItem>

            <FormItem
              name="accountNumber"
              label="Số tài khoản"
              required
            >
              <Input
                value={paymentConfig.accountNumber}
                onChange={(e) => handlePaymentConfigChange('accountNumber', e.target.value)}
                placeholder="Nhập số tài khoản"
                fullWidth
              />
            </FormItem>

            <FormItem
              name="accountName"
              label="Tên tài khoản"
              required
            >
              <Input
                value={paymentConfig.accountName}
                onChange={(e) => handlePaymentConfigChange('accountName', e.target.value)}
                placeholder="Nhập tên chủ tài khoản"
                fullWidth
              />
            </FormItem>

            <div className="flex justify-end">
              <Button
                onClick={handleSavePaymentConfig}
                variant="primary"
                className="px-6"
              >
                Lưu cấu hình
              </Button>
            </div>
          </div>
        </CollapsibleCard>

        {/* 3. Cấu hình mẫu hóa đơn */}
        <CollapsibleCard
          title={
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-warning/10 rounded-lg flex items-center justify-center">
                <span className="text-warning text-lg">🧾</span>
              </div>
              <div>
                <Typography variant="h3" className="text-lg font-semibold">
                  Cấu hình mẫu hóa đơn đầu vào
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  Upload mẫu hóa đơn PDF cho hệ thống
                </Typography>
              </div>
            </div>
          }
          defaultOpen={false}
          className="mb-6"
        >
          <div className="space-y-4">
            <Typography variant="body1" className="text-muted-foreground">
              Chỉ chấp nhận file PDF. Kích thước tối đa: 10MB
            </Typography>
            
            <div className="space-y-2">
              <Typography variant="h4" className="font-medium">
                Mẫu hóa đơn đầu vào
              </Typography>
              <div className="flex items-center gap-4">
                <input
                  type="file"
                  accept=".pdf"
                  onChange={(e) => handleFileUpload('invoiceTemplate', e.target.files?.[0] || null)}
                  className="hidden"
                  id="invoice-template-upload"
                />
                <label
                  htmlFor="invoice-template-upload"
                  className="px-4 py-2 bg-primary text-primary-foreground rounded-md cursor-pointer hover:bg-primary/90 transition-colors"
                >
                  Chọn file PDF
                </label>
                {contractFiles.invoiceTemplate && (
                  <span className="text-sm text-muted-foreground">
                    {contractFiles.invoiceTemplate.name}
                  </span>
                )}
              </div>
            </div>
          </div>
        </CollapsibleCard>
      </div>
    </div>
  );
};

export default SystemConfigPage;
